/*
Copyright © 2025 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"context"
	"github.com/fsnotify/fsnotify"
	"github.com/gofiber/fiber/v2"
	"github.com/soheilhy/cmux"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"google.golang.org/grpc"
	"net"
	"os"
	"os/signal"
	"resflow/configs"
	"resflow/database"
	"resflow/http"
	"resflow/internal/plugin"
	"resflow/middlewares"
	"resflow/router"
	"resflow/store"
	"resflow/utils"
	"strconv"
	"syscall"
)

var server *grpc.Server
var httpServer *fiber.App

// serveCmd represents the serve command
var serveCmd = &cobra.Command{
	Use:   "serve",
	Short: "start server",
	Long:  ``,
	Run: func(cmd *cobra.Command, args []string) {
		listener, err := net.Listen("tcp", ":"+strconv.Itoa(configs.ParsedConfig.AppConfig.Port))
		if err != nil {
			utils.Logger.Errorf("listen error: %v", err)
			panic(err)
		}

		m := cmux.New(listener)

		utils.Logger.Info("Starting server...")
		startGrpcServer(m)
		startHttpServer(m)

		go func() {
			if err := m.Serve(); err != nil {
				utils.Logger.Fatalf("cmux error: %v", err)
			}
		}()

		viper.OnConfigChange(func(e fsnotify.Event) {
			utils.Logger.Debugln("Config file changed:", e.Name)

			stopGrpcServer()
			stopHttpServer()

			configs.ReadConfig()

			startGrpcServer(m)
			startHttpServer(m)
		})

		// 监听系统信号以优雅关闭
		ctx, cancel := context.WithCancel(context.Background())
		signalChan := make(chan os.Signal, 1)
		signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM, os.Kill)
		go func() {
			utils.Logger.Debug("Listen for signalChan signal...")
			sig := <-signalChan
			utils.Logger.Debugln("Received signal:", sig)
			stopGrpcServer()
			stopHttpServer()
			cancel()
		}()

		<-ctx.Done()
	},
}

func init() {
	rootCmd.AddCommand(serveCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// serveCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// serveCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
}

// startGrpcServer 启动Grpc服务器
func startGrpcServer(m cmux.CMux) {
	utils.Logger.Debug("Starting grpc server...")

	listener := m.Match(cmux.HTTP2HeaderField("content-type", "application/grpc"))

	server = grpc.NewServer(
		grpc.UnaryInterceptor(middlewares.BuildAuthUnaryInterceptor(store.NewUserStore(database.Client))),
	)

	router.SetupGrpcRoutes(server, store.NewStore(database.Client))

	go func() {
		utils.Logger.Debug("gRPC server started at %s", listener.Addr().String())
		if err := server.Serve(listener); err != nil {
			utils.Logger.Fatalf("启动服务器失败: %v", err)
		}
	}()
}

// stopGrpcServer 关闭Grpc服务器
func stopGrpcServer() {
	if server != nil {
		utils.Logger.Info("Stopping server...")
		server.GracefulStop()
	}
}

func startHttpServer(m cmux.CMux) {
	httpServer = fiber.New(fiber.Config{
		DisableStartupMessage: !configs.ParsedConfig.Debug,
	})
	router.SetupHttpRoutes(httpServer, http.NewService(http.NewPluginService(plugin.NewPluginService(plugin.NewPluginStore(database.Client)))))
	listener := m.Match(cmux.Any())

	go func() {
		utils.Logger.Debug("Http server started at %s", listener.Addr().String())
		if err := httpServer.Listener(listener); err != nil {
			utils.Logger.Fatalf("启动服务器失败: %v", err)
		}
	}()
}

func stopHttpServer() {
	if httpServer != nil {
		err := httpServer.Shutdown()
		if err != nil {
			utils.Logger.Fatalf("Close http server failed: %v", err)
		}
	}
}
