/*
Copyright © 2025 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"context"
	"errors"
	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/sqlite3"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"os"
	"path/filepath"
	"resflow/database"
	"resflow/service"
	"resflow/store"
	"resflow/utils"
	"runtime"
)

var reset = new(bool)

// initDbCmd represents the initDb command
var initDbCmd = &cobra.Command{
	Use:   "initDb",
	Short: "init database",
	Long:  `初始化数据库`,
	Run: func(cmd *cobra.Command, args []string) {
		utils.Logger.Debug("initDb called")
		if *reset {
			// 重置数据库
			err := os.Remove(database.AppDatabaseFile)
			if err != nil {
				utils.Logger.Errorln("删除数据库文件失败：", err)
				return
			}
		}
		// 初始化数据库
		instance, err := sqlite3.WithInstance(database.DB, &sqlite3.Config{})
		if err != nil {
			utils.Logger.Error("sqlite3 instance init error", zap.Error(err))
			return
		}
		// 获取项目根目录路径
		_, filename, _, _ := runtime.Caller(0)
		rootDir := filepath.Dir(filepath.Dir(filename))
		// 构建绝对路径
		migrationsPath := filepath.Join(rootDir, "database", "migrations")
		utils.Logger.Debug("migrations path:", migrationsPath)
		// 执行迁移
		m, err := migrate.NewWithDatabaseInstance("file://"+migrationsPath, "sqlite3", instance)
		if err != nil {
			utils.Logger.Errorln("migrate init error:", err)
			return
		}
		err = m.Up()
		if err != nil && !errors.Is(err, migrate.ErrNoChange) {
			utils.Logger.Errorln("migration error:", err)
			return
		}
		// 初始化数据
		// 初始化admin用户
		userService := service.NewUserService(store.NewUserStore(database.Client))
		u, password, err := userService.CreateWithRandomPassword(context.Background(), "admin")
		if err != nil {
			if errors.Is(err, service.ErrUserExisted) {
				utils.Logger.Infoln("默认用户已存在，跳过")
			} else {
				utils.Logger.Error("初始化默认用户失败", err)
			}
		} else {
			utils.Logger.Info("默认用户名：", u.Username)
			utils.Logger.Info("默认密码：", password)
		}
	},
}

func init() {
	rootCmd.AddCommand(initDbCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// initDbCmd.PersistentFlags().String("foo", "", "A help for foo")
	initDbCmd.Flags().BoolVarP(reset, "reset", "r", false, "重置数据库")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// initDbCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
}
