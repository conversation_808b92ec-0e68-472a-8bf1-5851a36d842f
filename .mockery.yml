all: false
dir: '{{.InterfaceDir}}'
filename: "mocks_moq.go"
force-file-write: true
formatter: goimports
include-auto-generated: false
log-level: info
structname: "Moq{{.InterfaceName}}"
pkgname: '{{.SrcPackageName}}'
recursive: false
require-template-schema-exists: true
template: matryer
template-schema: '{{.Template}}.schema.json'
packages:
  resflow/repo:
    config:
      all: true
  resflow/internal/node_definition:
    interfaces:
      ManifestLoader: { }
      InstallService: { }
      store: { }
  resflow/internal/plugin:
    interfaces:
      ManifestLoader: { }
      store: { }
  resflow/utils:
    interfaces:
      Manifest: { }
