package router

import (
	"github.com/gofiber/fiber/v2"
	"path/filepath"
	"resflow/http"
	"resflow/utils"
)

func SetupHttpRoutes(app *fiber.App, service *http.Service) {
	app.Get("/", func(c *fiber.Ctx) error {
		return c.SendString("Hello, World!")
	})

	app.Route("/plugin", func(router fiber.Router) {
		router.Get("/icon/:name/:version", service.Plugin.HandleIcon(filepath.Join(utils.GetRootPath(), "plugins")))
	})
}
