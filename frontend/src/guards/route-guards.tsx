import { Navigate } from "react-router";
import { useAuth } from "@/contexts/auth-context.tsx";
import PageLoading from "@/components/page-loading.tsx";

export const PublicRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { user, loading } = useAuth();
  if (loading) {
    return <PageLoading />;
  }
  if (user) {
    return <Navigate to="/" />;
  }
  return <>{children}</>;
};

export const RequireAuth: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { user, loading } = useAuth();
  if (loading) {
    return <PageLoading />;
  }
  if (!user) {
    return <Navigate to="/login" />;
  }
  return <>{children}</>;
};
