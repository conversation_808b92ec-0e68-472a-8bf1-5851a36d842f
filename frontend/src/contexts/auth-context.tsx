import { createContext, useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { User as UserType } from "@/types/user.ts";
import { useToken } from "@/hooks/use-token.ts";
import { useClient } from "@/hooks/use-client.ts";
import {
  AuthService,
  LoginRequest,
  LoginResponse,
} from "@/proto/v1/auth_service_pb.ts";
import { useGrpcRequest } from "@/hooks/use-grpc-request.ts";
import { User, UserService } from "@/proto/v1/user_service_pb";
import { Empty } from "@bufbuild/protobuf/wkt";

interface AuthContextType {
  user: UserType | null;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<UserType | null>(null);
  const [token, setToken, removeToken] = useToken();
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(true);

  const authClient = useClient(AuthService);
  const [requestLogin] = useGrpcRequest<LoginRequest, LoginResponse>(
    authClient.login,
  );
  const userClient = useClient(UserService);
  const [requestCurrentUser] = useGrpcRequest<Empty, User>(
    userClient.getCurrentUser,
  );

  useEffect(() => {
    async function getCurrentUser() {
      try {
        const currentUser = await requestCurrentUser(
          {},
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        );
        setUser(currentUser as unknown as UserType);
      } catch (err) {
        console.log("err", err);
      } finally {
        setLoading(false);
      }
    }

    // 判断是否登录
    if (!token) {
      navigate("/login");
      setLoading(false);
    } else {
      getCurrentUser();
    }
  }, []);

  async function login(username: string, password: string) {
    const res = await requestLogin({
      username,
      password,
    });
    setToken(res.token);
    setUser(res.user! as unknown as UserType);
  }

  function logout() {
    removeToken();
    setUser(null);
  }

  return (
    <AuthContext value={{ user, loading, login, logout }}>
      {children}
    </AuthContext>
  );
};

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within a AuthProvider");
  }
  return context;
}
