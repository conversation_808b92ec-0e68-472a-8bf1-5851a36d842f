import {
  createContext,
  ReactN<PERSON>,
  useCallback,
  useContext,
  useState,
} from "react";

interface HeaderSlotContextType {
  headerSlot: ReactNode;
  setHeaderSlot: (slot: ReactNode) => void;
  clearHeaderSlot: () => void;
}

const HeaderSlotContext = createContext<HeaderSlotContextType | undefined>(
  undefined,
);

export function HeaderSlotProvider({ children }: { children: ReactNode }) {
  const [headerSlot, setHeaderSlot] = useState<ReactNode>(null);

  const clearHeaderSlot = useCallback(() => {
    setHeaderSlot(null);
  }, [setHeaderSlot]);

  return (
    <HeaderSlotContext value={{ headerSlot, setHeaderSlot, clearHeaderSlot }}>
      {children}
    </HeaderSlotContext>
  );
}

export function useHeaderSlotContext() {
  const context = useContext(HeaderSlotContext);
  if (!context) {
    throw new Error("useHeaderSlot must be used within a AuthProvider");
  }
  return context;
}
