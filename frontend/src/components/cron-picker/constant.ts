import { Locale } from "react-js-cron";

export const CRON_CHINESE_LOCALE: Locale = {
  everyText: "每",
  emptyMonths: "每月",
  emptyMonthDays: "每天",
  emptyMonthDaysShort: "每天",
  emptyWeekDays: "周的每天",
  emptyWeekDaysShort: "周几",
  emptyHours: "每小时",
  emptyMinutes: "每分钟",
  emptyMinutesForHourPeriod: "每",
  yearOption: "年",
  monthOption: "月",
  weekOption: "周",
  dayOption: "天",
  hourOption: "时",
  minuteOption: "分",
  rebootOption: "reboot",
  prefixPeriod: "每",
  prefixMonths: "的",
  prefixMonthDays: "的",
  prefixWeekDays: "的",
  prefixWeekDaysForMonthAndYearPeriod: "的",
  prefixHours: "的",
  prefixMinutes: ":",
  prefixMinutesForHourPeriod: "的",
  suffixMinutesForHourPeriod: "分钟",
  errorInvalidCron: "不合法的Cron表达式",
  clearButtonText: "清除",
  weekDays: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
  months: [
    "1月",
    "2月",
    "3月",
    "4月",
    "5月",
    "6月",
    "7月",
    "8月",
    "9月",
    "10月",
    "11月",
    "12月",
  ],
  altWeekDays: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
  altMonths: [
    "1月",
    "2月",
    "3月",
    "4月",
    "5月",
    "6月",
    "7月",
    "8月",
    "9月",
    "10月",
    "11月",
    "12月",
  ],
};
