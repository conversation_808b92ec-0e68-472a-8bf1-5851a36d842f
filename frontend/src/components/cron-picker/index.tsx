import Cron from "react-js-cron";
import "./index.css";
import { CRON_CHINESE_LOCALE } from "./constant.ts";
import { FC, memo } from "react";
import { Badge } from "@/components/ui/badge.tsx";
import { MousePointerClick } from "lucide-react";

interface CronPickerProps {
  value: string;
  onValueChange: (value: string) => void;
}

export function CronPicker({ value, onValueChange }: CronPickerProps) {
  return (
    <div className="flex flex-col gap-2">
      <Cron
        locale={CRON_CHINESE_LOCALE}
        clearButton={false}
        value={value}
        setValue={onValueChange}
        className="cron-picker"
        leadingZero
      />
      <ShortCuts onShortCut={onValueChange} />
    </div>
  );
}

const SHORT_CUTS: { name: string; cron_expression: string }[] = [
  {
    name: "每分钟",
    cron_expression: "* * * * *",
  },
  {
    name: "每5分钟",
    cron_expression: "*/5 * * * *",
  },
  {
    name: "每小时",
    cron_expression: "0 * * * *",
  },
  {
    name: "每天0点0分",
    cron_expression: "0 0 * * *",
  },
  {
    name: "每天8点15分",
    cron_expression: "15 8 * * *",
  },
  {
    name: "工作日每天8点",
    cron_expression: "0 8 * * 1-5",
  },
  {
    name: "每月15号0点0分",
    cron_expression: "0 0 15 * *",
  },
  {
    name: "每3个月的15号0点0分",
    cron_expression: "0 0 15 */3 *",
  },
];

const ShortCuts: FC<{ onShortCut: (cron_expression: string) => void }> = memo(
  ({ onShortCut }) => {
    return (
      <div className="flex flex-wrap gap-1">
        {SHORT_CUTS.map(({ name, cron_expression }) => (
          <Badge
            className="cursor-pointer"
            variant="outline"
            onClick={() => onShortCut(cron_expression)}
            key={name}
          >
            <MousePointerClick />
            {name}
          </Badge>
        ))}
      </div>
    );
  },
);
