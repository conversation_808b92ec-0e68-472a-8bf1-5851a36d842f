import {
  ChangeEvent,
  HTMLInputTypeAttribute,
  KeyboardEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { Input } from "@/components/ui/input.tsx";
import { cn } from "@/lib/utils.ts";

interface EditableLabelProps {
  value?: string;
  type?: HTMLInputTypeAttribute;
  onChange?: (value: string) => void;
  labelClassName?: string;
  inputClassName?: string;
  className?: string;
  autoFocus?: boolean;
  placeholder: string;
}

export function EditableLabel({
  value,
  type = "text",
  onChange,
  inputClassName,
  labelClassName,
  className,
  placeholder,
  autoFocus = false,
}: EditableLabelProps) {
  const [editable, setEditable] = useState(autoFocus);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange?.(e.currentTarget.value);
    },
    [onChange],
  );

  const handleBlur = useCallback(() => {
    setEditable(false);
  }, [setEditable]);

  const handleLabelClick = useCallback(() => {
    setEditable(true);
  }, [setEditable]);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter") {
        setEditable(false);
      }
    },
    [setEditable],
  );

  useEffect(() => {
    if (autoFocus) {
      inputRef?.current?.focus();
      inputRef?.current?.select();
    }
  }, [autoFocus]);

  useEffect(() => {
    if (editable) {
      inputRef.current?.focus();
    }
  }, [editable, inputRef]);

  if (editable) {
    return (
      <div className={cn(className, inputClassName)}>
        <Input
          type={type}
          onChange={handleChange}
          value={value}
          onBlur={handleBlur}
          ref={inputRef}
          onKeyDown={handleKeyDown}
        />
      </div>
    );
  }

  if (!value || value === "") {
    return (
      <h1 className="text-muted-foreground" onClick={handleLabelClick}>
        {placeholder}
      </h1>
    );
  }

  return (
    <h1 className={cn(className, labelClassName)} onClick={handleLabelClick}>
      {value}
    </h1>
  );
}
