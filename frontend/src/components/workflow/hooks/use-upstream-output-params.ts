import { useWorkflowStore } from "@/components/workflow/context/workflow-context.tsx";
import { useShallow } from "zustand/react/shallow";
import { ParamType } from "@/components/param-editor/param-plugin";

export function useUpstreamOutputParams(nodeId: string): ParamType[] {
  const { nodes, edges } = useWorkflowStore(
    useShallow((s) => ({
      nodes: s.nodes,
      edges: s.edges,
    })),
  );

  // 根据节点和边查询
  const params: ParamType[] = [];
  let targetNodeId = nodeId;
  while (targetNodeId) {
    const sourceNodeId = edges.find((e) => e.target === targetNodeId)?.source;
    if (!sourceNodeId) {
      break;
    }
    const sourceNode = nodes.find((n) => n.id === sourceNodeId);
    if (!sourceNode) {
      break;
    }
    console.log(
      targetNodeId,
      "->",
      sourceNodeId,
      sourceNode,
    );
    if (sourceNode.data && sourceNode.data.output_params) {
      for (const outputParam of sourceNode.data.output_params) {
        params.push({
          nodeId: sourceNodeId,
          nodeName: sourceNode.data.name,
          paramId: outputParam.id,
          paramName: outputParam.label,
        });
      }
    }
    targetNodeId = sourceNodeId;
  }
  return params;
}
