import { NodeSchema } from "../types/workflow.ts";
import { DragEventHand<PERSON>, useCallback } from "react";
import { TRANSFER_DATA_TYPE } from "../constants.ts";
import { useNodeOperations } from "../hooks/use-node-operations.tsx";
import { useReactFlow } from "@xyflow/react";

export function useNodeSelectorDrag() {
  const { handleAddNode } = useNodeOperations();
  const { screenToFlowPosition } = useReactFlow();

  const handleDragStart = useCallback<
    (nodeDef: NodeSchema) => DragEventHandler
  >((nodeDef) => {
    return (event) => {
      event.dataTransfer.setData(TRANSFER_DATA_TYPE, JSON.stringify(nodeDef));
    };
  }, []);

  const handleDragOver = useCallback<DragEventHandler>((event) => {
    event.preventDefault();
  }, []);

  const handleDrop = useCallback<DragEventHandler>(
    (event) => {
      let data = event.dataTransfer.getData(TRANSFER_DATA_TYPE);
      if (!data) return;
      data = JSON.parse(data);
      const nodePosition = screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });
      handleAddNode(data as unknown as NodeSchema, nodePosition);
    },
    [handleAddNode, screenToFlowPosition],
  );

  return {
    handleDragStart,
    handleDragOver,
    handleDrop,
  };
}
