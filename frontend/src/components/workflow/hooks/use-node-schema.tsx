import { useEffect, useState } from "react";
import { NodeSchema } from "@/components/workflow/types/workflow.ts";
import { useGetNodeDefinitionByTypeAndVersion } from "@/api/node-definition.ts";

export function useNodeSchema(type: string, version: string) {
  const [requestNodeSchema] = useGetNodeDefinitionByTypeAndVersion();
  const [nodeSchema, setNodeSchema] = useState<NodeSchema | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      const schema = await requestNodeSchema(type, version);
      console.log(schema);
      setNodeSchema(schema);
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, version]);

  return {
    nodeSchema,
  };
}
