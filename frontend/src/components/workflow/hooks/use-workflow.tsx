import { useWorkflowStore } from "@/components/workflow/context/workflow-context.tsx";
import { useCallback } from "react";
import { useShallow } from "zustand/react/shallow";
import { Viewport } from "@xyflow/react";

export function useWorkflow() {
  const { setViewport } = useWorkflowStore(
    useShallow((s) => ({
      setViewport: s.setViewport,
    })),
  );

  const handleViewportChange = useCallback<(viewport: Viewport) => void>(
    (viewport) => {
      setViewport(viewport);
    },
    [setViewport],
  );

  return {
    handleViewportChange,
  };
}
