import NodeTypes from "./nodes";
import {
  Background,
  BackgroundVariant,
  Controls,
  Edge,
  MiniMap,
  ReactFlow,
  ReactFlowProps,
  ReactFlowProvider,
  Viewport,
} from "@xyflow/react";
import { useTheme } from "@/contexts/theme-context.tsx";
import { useNodeOperations } from "./hooks/use-node-operations.tsx";
import { useShallow } from "zustand/react/shallow";
import {
  useGetWorkflowStore,
  useWorkflowStore,
  WorkflowProvider,
} from "./context/workflow-context";
import { WorkflowPanels } from "./workflow-panels.tsx";
import { WorkflowStore } from "./store";
import { CustomEdge } from "./custom-edge.tsx";
import { useEdgeOperations } from "./hooks/use-edge-operations.tsx";
import { useNodeSelectorDrag } from "./hooks/use-node-selector-drag.tsx";
import { PropsWithChildren, useImperativeHandle } from "react";
import "@xyflow/react/dist/style.css";
import "./assets/flow.css";
import {
  NodeSchema,
  WorkflowNode,
  WorkflowRef,
} from "@/components/workflow/types/workflow.ts";
import { useWorkflow } from "@/components/workflow/hooks/use-workflow.tsx";

const edgeTypes = { CustomEdge: CustomEdge };

const selector = (state: WorkflowStore) => ({
  nodes: state.nodes,
  setNodes: state.setNodes,
  edges: state.edges,
  setEdges: state.setEdges,
  getSelectedNode: state.getSelectedNode,
  viewport: state.viewport,
});

interface WorkflowInnerProps {
  ref: WorkflowRef;
}

function WorkflowInner({
  children,
  ref,
  ...props
}: WorkflowInnerProps & ReactFlowProps) {
  const { theme } = useTheme();

  const { nodes, edges, viewport } = useWorkflowStore(useShallow(selector));

  const { handleViewportChange } = useWorkflow();

  const { handleNodeClick, handleNodesChange } = useNodeOperations();

  const {
    handleEdgesChange,
    handleConnect,
    handleEdgeMouseEnter,
    handleEdgeMouseLeave,
  } = useEdgeOperations();

  const { handleDragOver, handleDrop } = useNodeSelectorDrag();

  const store = useGetWorkflowStore();

  useImperativeHandle(ref, () => ({
    getNodes() {
      return store.getState().nodes;
    },
    getEdges() {
      return store.getState().edges;
    },
    getViewport() {
      return store.getState().viewport;
    },
  }));

  return (
    <div className="relative w-full h-full">
      <WorkflowPanels></WorkflowPanels>
      <ReactFlow
        {...props}
        colorMode={theme}
        nodeTypes={NodeTypes}
        edgeTypes={edgeTypes}
        nodes={nodes}
        edges={edges}
        maxZoom={3}
        elevateEdgesOnSelect
        viewport={viewport}
        onViewportChange={handleViewportChange}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onConnect={handleConnect}
        onNodeClick={handleNodeClick}
        onEdgeMouseEnter={handleEdgeMouseEnter}
        onEdgeMouseLeave={handleEdgeMouseLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        connectionLineStyle={{
          strokeWidth: 2,
          stroke: "black",
        }}
      >
        <Controls
          showInteractive={false}
          orientation="horizontal"
          position="bottom-right"
        />
        <MiniMap
          style={{
            width: 102,
            height: 72,
          }}
          className="bottom-14! rounded-lg! shadow-md! shadow-shadow-shadow-5!"
          pannable
          zoomable
          position="bottom-right"
        />
        <Background
          className="bg-slate-50! dark:bg-zinc-900!"
          variant={BackgroundVariant.Dots}
          gap={12}
          size={1}
        />
        {children}
      </ReactFlow>
    </div>
  );
}

interface WorkflowProps {
  initialNodes: WorkflowNode[];
  initialEdges: Edge[];
  availableNodes: NodeSchema[];
  fitView?: boolean;
  initialViewport: Viewport;
  ref: WorkflowRef;
}

export function Workflow({
  initialNodes,
  initialEdges,
  availableNodes,
  fitView,
  initialViewport,
  children,
  ref,
}: PropsWithChildren<WorkflowProps>) {
  return (
    <div className="bg-white w-full h-full">
      <WorkflowProvider
        nodes={initialNodes}
        edges={initialEdges}
        viewport={initialViewport}
        availableNodes={availableNodes}
      >
        <ReactFlowProvider>
          <WorkflowInner fitView={fitView} ref={ref}>
            {children}
          </WorkflowInner>
        </ReactFlowProvider>
      </WorkflowProvider>
    </div>
  );
}
