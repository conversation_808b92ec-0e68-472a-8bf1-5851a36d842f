import { memo } from "react";
import { PanelProps } from "@/components/workflow/types/workflow.ts";
import {
  CronTriggerInputData,
  CronTriggerOutputData,
} from "@/components/workflow/nodes/cron-trigger/type.ts";
import { useNodeOperations } from "@/components/workflow/hooks/use-node-operations.tsx";
import "react-js-cron/dist/styles.css";
import { CronPicker } from "@/components/cron-picker";
import { NodeSchemaUndefined } from "@/components/workflow/node/node-schema-undefined.tsx";
import { Divider } from "@/components/divider.tsx";
import { Label } from "@/components/ui/label.tsx";

export function Panel({
  id,
  schema,
  ...props
}: PanelProps<CronTriggerInputData, CronTriggerOutputData>) {
  const {
    data: { input_values },
  } = props;
  const { handleUpdateNodeData } = useNodeOperations();

  const handleCronExpressionChange = (cron_expression: string) => {
    handleUpdateNodeData<CronTriggerInputData>(id, {
      input_values: {
        cron_expression,
      },
    });
  };

  if (!schema) {
    return <NodeSchemaUndefined />;
  }

  return (
    <>
      <div className="grid w-full items-center gap-3 flex-wrap">
        <Label>Cron表达式</Label>
        <CronPicker
          value={input_values.cron_expression}
          onValueChange={handleCronExpressionChange}
        />
      </div>
      <Divider />
    </>
  );
}

export default memo(Panel);
