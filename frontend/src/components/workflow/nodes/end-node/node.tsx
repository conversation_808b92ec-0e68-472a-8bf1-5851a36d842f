import { WorkflowNodeProps } from "@/components/workflow/types/workflow.ts";
import { BaseNode } from "@/components/workflow/node/base-node.tsx";
import {
  EndNodeInputData,
  EndNodeOutputData,
} from "@/components/workflow/nodes/end-node/type.ts";

export function Node(
  props: WorkflowNodeProps<EndNodeInputData, EndNodeOutputData>,
) {
  return <BaseNode className="p-4" {...props}></BaseNode>;
}
