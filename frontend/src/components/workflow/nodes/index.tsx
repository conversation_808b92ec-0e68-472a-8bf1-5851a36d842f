import { NodeTypes } from "@xyflow/react";
import { NodeTypeEnum } from "@/components/workflow/nodes/constants.ts";
// Nodes
import { CronTrigger } from "./cron-trigger";
import { EventTrigger } from "@/components/workflow/nodes/event-trigger";
import { WebhookTrigger } from "@/components/workflow/nodes/webhook-trigger";
import { EndNode } from "./end-node/index.ts";
import { CustomNode } from "../node/custom-node.tsx";

export default {
  [NodeTypeEnum.CronTrigger]: CronTrigger,
  [NodeTypeEnum.EventTrigger]: EventTrigger,
  [NodeTypeEnum.WebhookTrigger]: WebhookTrigger,
  [NodeTypeEnum.EndNode]: EndNode,
  [NodeTypeEnum.CustomNode]: CustomNode,
} satisfies NodeTypes;
