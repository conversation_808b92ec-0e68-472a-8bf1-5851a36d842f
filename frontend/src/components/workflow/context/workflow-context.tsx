import {
  createWorkflowStore,
  WorkflowStore,
  WorkflowStoreProps,
} from "../store";
import { createContext, PropsWithChildren, useContext, useRef } from "react";
import { useStore as useZustandStore } from "zustand";

type WorkflowContextType = ReturnType<typeof createWorkflowStore>;

const WorkflowContext = createContext<WorkflowContextType | undefined>(
  undefined,
);

type WorkflowProviderProps = PropsWithChildren<WorkflowStoreProps>;

export function WorkflowProvider({
  children,
  ...props
}: WorkflowProviderProps) {
  const store = useRef(createWorkflowStore(props)).current;
  return <WorkflowContext value={store}>{children}</WorkflowContext>;
}

export function useWorkflowStore<T>(selector: (state: WorkflowStore) => T): T {
  const store = useContext(WorkflowContext);
  if (!store) {
    throw new Error("useWorkflowStore must be used within a WorkflowProvider");
  }
  return useZustandStore(store, selector);
}

export function useGetWorkflowStore(): WorkflowContextType {
  const store = useContext(WorkflowContext);
  if (!store) {
    throw new Error("useWorkflowStore must be used within a WorkflowProvider");
  }
  return store;
}
