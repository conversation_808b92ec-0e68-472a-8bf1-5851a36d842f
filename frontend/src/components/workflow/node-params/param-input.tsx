import { useCallback, useState } from "react";
import { ParamEditor } from "@/components/param-editor/index.tsx";
import { parseToLexicalEditorState } from "@/components/param-editor/parser.ts";
import { ParamType } from "@/components/param-editor/param-plugin";

interface Props {
  value: string;
  onValueChange?: (v: string) => void;
  params?: ParamType[];
}

export function ParamInput({ value, onValueChange, params = [] }: Props) {
  const [editorState, setEditorState] = useState(
    parseToLexicalEditorState(value),
  );

  const handleChange = useCallback(
    (v: string) => {
      setEditorState(v);
      onValueChange?.(v);
    },
    [onValueChange],
  );

  return (
    <div className="border rounded-lg p-2">
      <ParamEditor
        editorState={editorState}
        onChange={handleChange}
        params={params}
      ></ParamEditor>
    </div>
  );
}
