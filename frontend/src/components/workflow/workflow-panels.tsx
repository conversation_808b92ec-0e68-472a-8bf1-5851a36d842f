import { NodePanel } from "@/components/workflow/panel/node-panel.tsx";
import { useWorkflowStore } from "./context/workflow-context.tsx";
import { useShallow } from "zustand/react/shallow";
import { useMemo } from "react";
import { PropsWithChildren } from "react";
import { WorkflowStore } from "./store";

interface WorkflowPanelProps extends PropsWithChildren {}

const selector = (state: WorkflowStore) => ({
  nodes: state.nodes,
  getSelectedNode: state.getSelectedNode,
});

export function WorkflowPanels({ children }: WorkflowPanelProps) {
  const { nodes, getSelectedNode } = useWorkflowStore(useShallow(selector));

  const selectedNode = useMemo(() => getSelectedNode(), [nodes]);

  return (
    <div className="absolute top-0 w-full h-full bg-transparent">
      {!!selectedNode && <NodePanel {...selectedNode}></NodePanel>}
      {children}
    </div>
  );
}
