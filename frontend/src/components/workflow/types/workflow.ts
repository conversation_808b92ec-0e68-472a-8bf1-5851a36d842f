import type {
  Edge as React<PERSON><PERSON>Edge,
  HandleType,
  Node as <PERSON>act<PERSON>lowNode,
  NodeProps as ReactFlowNodeProps,
  Position,
  Viewport,
} from "@xyflow/react";
import { ComponentType, Ref } from "react";
import { Nullish } from "utility-types";

export type NodeData = Record<string, any>;
export type EdgeData = Record<string, any>;

export type NodeBase<T extends NodeData = NodeData> = ReactFlowNode<T, string>;

export interface CommonNodeData<
  InputData extends NodeData = NodeData,
  OutputData extends NodeData = NodeData,
> extends NodeData {
  icon: string;
  name: string;
  type: string;
  version: string;
  description?: string;
  plugin_name?: string;
  plugin_version?: string;
  input_params?: NodeParam[];
  input_values: InputData;
  output_params?: NodeParam[];
  output_values: OutputData;
  input_ports?: NodeHandle[];
  output_ports?: NodeHandle[];
}

export type WorkflowNode<
  InputData extends NodeData = NodeData,
  OutputData extends NodeData = NodeData,
> = NodeBase<CommonNodeData<InputData, OutputData>>;

export type WorkflowNodeProps<
  InputData extends NodeData = NodeData,
  OutputData extends NodeData = NodeData,
> = ReactFlowNodeProps<WorkflowNode<InputData, OutputData>>;

export type PanelProps<
  InputData extends NodeData = NodeData,
  OutputData extends NodeData = NodeData,
> = WorkflowNode<InputData, OutputData> & {
  schema: NodeSchema | Nullish;
};

export type WorkflowEdge<T extends EdgeData = EdgeData> = ReactFlowEdge<
  T,
  string
> & {
  hover?: boolean;
};

export type EdgeType = WorkflowEdge<EdgeData>;

export type NodeParamType =
  | "String"
  | "Number"
  | "Boolean"
  | "Object"
  | "Array";

export type NodeParam = {
  id: string;
  type: NodeParamType;
  label: string;
  description?: string;
  group?: string;
  order?: number;
  value?: unknown;
  required?: boolean;
  schema?: NodeParam;
};

export type NodeHandle = {
  id: string;
  type: HandleType;
  position: Position;
};

export type IconType = string | ComponentType;

export type NodeSchema = {
  id: string;
  name: string;
  author: string;
  description: string;
  icon: string;
  type: string;
  version: string;
  category: string;
  input_params: NodeParam[];
  output_params: NodeParam[];
  input_ports: NodeHandle[];
  output_ports: NodeHandle[];
  exception?: boolean;
};

export type WorkflowRefObject = {
  getNodes: () => WorkflowNode[];
  getEdges: () => EdgeType[];
  getViewport: () => Viewport;
};

export type WorkflowRef = Ref<WorkflowRefObject>;
