import { describe, expect, test } from "vitest";
import { parseToLexicalEditorState } from "./parser";

describe("parseToLexicalEditorState", () => {
  test("空字符串", () => {
    const input = "";
    const result = parseToLexicalEditorState(input);
    expect(result).toStrictEqual(
      JSON.stringify({
        root: {
          children: [
            {
              children: [],
              direction: null,
              format: "",
              indent: 0,
              type: "paragraph",
              version: 1,
              textFormat: 0,
              textStyle: "",
            },
          ],
          direction: null,
          format: "",
          indent: 0,
          type: "root",
          version: 1,
        },
      }),
    );
  });

  test("普通文本", () => {
    const input = "Hello World!";
    const result = parseToLexicalEditorState(input);
    expect(result).toStrictEqual(
      JSON.stringify({
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "Hello World!",
                  type: "text",
                  version: 1,
                },
              ],
              direction: null,
              format: "",
              indent: 0,
              type: "paragraph",
              version: 1,
              textFormat: 0,
              textStyle: "",
            },
          ],
          direction: null,
          format: "",
          indent: 0,
          type: "root",
          version: 1,
        },
      }),
    );
  });

  test("包含 Mustache 表达式", () => {
    const input = "Hello {{ user.name }}!";
    const result = parseToLexicalEditorState(input);
    expect(result).toStrictEqual(
      JSON.stringify({
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "Hello ",
                  type: "text",
                  version: 1,
                },
                {
                  type: "param",
                  version: 1,
                  nodeId: "user",
                  nodeName: "",
                  paramId: "name",
                  paramName: "",
                },
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "!",
                  type: "text",
                  version: 1,
                },
              ],
              direction: null,
              format: "",
              indent: 0,
              type: "paragraph",
              version: 1,
              textFormat: 0,
              textStyle: "",
            },
          ],
          direction: null,
          format: "",
          indent: 0,
          type: "root",
          version: 1,
        },
      }),
    );
  });

  test("多行文本", () => {
    const input = "Hello\nWorld!";
    const result = parseToLexicalEditorState(input);
    expect(result).toStrictEqual(
      JSON.stringify({
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "Hello",
                  type: "text",
                  version: 1,
                },
              ],
              direction: null,
              format: "",
              indent: 0,
              type: "paragraph",
              version: 1,
              textFormat: 0,
              textStyle: "",
            },
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "World!",
                  type: "text",
                  version: 1,
                },
              ],
              direction: null,
              format: "",
              indent: 0,
              type: "paragraph",
              version: 1,
              textFormat: 0,
              textStyle: "",
            },
          ],
          direction: null,
          format: "",
          indent: 0,
          type: "root",
          version: 1,
        },
      }),
    );
  });

  test("多行文本 + Mustache 表达式", () => {
    const input = "Hello {{ user.name }}!\nWorld!";
    const result = parseToLexicalEditorState(input);
    expect(result).toStrictEqual(
      JSON.stringify({
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "Hello ",
                  type: "text",
                  version: 1,
                },
                {
                  type: "param",
                  version: 1,
                  nodeId: "user",
                  nodeName: "",
                  paramId: "name",
                  paramName: "",
                },
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "!",
                  type: "text",
                  version: 1,
                },
              ],
              direction: null,
              format: "",
              indent: 0,
              type: "paragraph",
              version: 1,
              textFormat: 0,
              textStyle: "",
            },
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "World!",
                  type: "text",
                  version: 1,
                },
              ],
              direction: null,
              format: "",
              indent: 0,
              type: "paragraph",
              version: 1,
              textFormat: 0,
              textStyle: "",
            },
          ],
          direction: null,
          format: "",
          indent: 0,
          type: "root",
          version: 1,
        },
      }),
    );
  });

  test("Mustache 表达式未闭合忽略", () => {
    const input = "Hello {{ user.name";
    const result = parseToLexicalEditorState(input);
    expect(result).toStrictEqual(
      JSON.stringify({
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "Hello ",
                  type: "text",
                  version: 1,
                },
              ],
              direction: null,
              format: "",
              indent: 0,
              type: "paragraph",
              version: 1,
              textFormat: 0,
              textStyle: "",
            },
          ],
          direction: null,
          format: "",
          indent: 0,
          type: "root",
          version: 1,
        },
      }),
    );
  });

  test("Mustache 表达式错误忽略", () => {
    const input = "Hello {{ invalid }}";
    const result = parseToLexicalEditorState(input);
    expect(result).toStrictEqual(
      JSON.stringify({
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "Hello ",
                  type: "text",
                  version: 1,
                },
              ],
              direction: null,
              format: "",
              indent: 0,
              type: "paragraph",
              version: 1,
              textFormat: 0,
              textStyle: "",
            },
          ],
          direction: null,
          format: "",
          indent: 0,
          type: "root",
          version: 1,
        },
      }),
    );
  });
});
