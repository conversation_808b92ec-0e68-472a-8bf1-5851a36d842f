import { ComponentType } from "react";

export interface SyntaxRule<T> {
  startToken: string;
  endToken: string;
  parse?: (v: string) => T | Error;
}

export type SyntaxRuleSet<T> = SyntaxRule<T>[];

export type ParsedFragment<T> =
  | { type: "text"; value: string }
  | { type: "param"; value: T; Component?: ComponentType<T> }
  | {
      type: "error";
      message: string;
    };

export function parseExpression<T>(
  expression: string,
  rules: SyntaxRuleSet<T>,
) {
  const results: ParsedFragment<T>[] = [];
  let currentIndex = 0;

  while (currentIndex < expression.length) {
    let matched = false;

    for (const rule of rules) {
      const { startToken, endToken, parse } = rule;
      if (!startToken || !endToken) {
        throw new Error("startToken and endToken must be non-empty");
      }
      if (expression.startsWith(startToken, currentIndex)) {
        const endIndex = expression.indexOf(
          endToken,
          currentIndex + startToken.length,
        );
        if (endIndex === -1) {
          results.push({
            type: "error",
            message: `variable not closed at ${expression.slice(currentIndex)}`,
          });
          return results;
        }

        const inner = expression
          .slice(currentIndex + startToken.length, endIndex)
          .trim();

        if (parse) {
          const parsed = parse(inner);
          if (parsed instanceof Error) {
            results.push({
              type: "error",
              message: `variable parse error: ${parsed.message}`,
            });
          } else {
            results.push({
              type: "param",
              value: parsed,
            });
          }
        }

        currentIndex = endIndex + endToken.length;
        matched = true;
        break;
      }
    }

    if (!matched) {
      let nextTokenIndex = expression.length;
      for (const rule of rules) {
        const index = expression.indexOf(rule.startToken, currentIndex);
        if (index !== -1 && index < nextTokenIndex) {
          nextTokenIndex = index;
        }
      }
      results.push({
        type: "text",
        value: expression.slice(currentIndex, nextTokenIndex),
      });
      currentIndex = nextTokenIndex;
    }
  }

  return results;
}
