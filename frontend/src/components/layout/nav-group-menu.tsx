import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar.tsx";
import { RemixiconComponentType } from "@remixicon/react";
import { LucideIcon } from "lucide-react";
import { Link } from "react-router";
import { HugeiconsIcon } from "@hugeicons/react";

export interface MenuItem {
  title: string;
  url: string;
  icon?: LucideIcon | RemixiconComponentType | HugeiconsIcon;
}

export function NavGroupMenu({
  title,
  items,
}: {
  title: string;
  items: MenuItem[];
}) {
  return (
    <SidebarGroup>
      <SidebarGroupLabel>{title}</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => (
          <SidebarMenuItem key={item.title}>
            <SidebarMenuButton
              tooltip={item.title}
              asChild
              isActive={location.pathname === item.url}
            >
              <Link to={item.url}>
                {item.icon && <item.icon />}
                <span>{item.title}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
