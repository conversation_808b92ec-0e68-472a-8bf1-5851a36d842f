import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar.tsx";
import { HugeiconsIcon } from "@hugeicons/react";
import { WorkflowSquare03Icon } from "@hugeicons/core-free-icons";
import { <PERSON> } from "react-router";

export function AppLogo() {
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton
          asChild
          className="data-[slot=sidebar-menu-button]:!p-1.5"
        >
          <Link to="/">
            <HugeiconsIcon icon={WorkflowSquare03Icon} />
            <span className="text-base font-semibold">Resflow</span>
          </Link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
