import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar.tsx";
import { AppSidebar } from "./app-sidebar";
import { Separator } from "@/components/ui/separator.tsx";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb.tsx";
import { NavLink, Outlet, useMatches } from "react-router";
import { useSidebarOpenState } from "@/hooks/use-sidebar-open-state.ts";
import { HandleType } from "@/router.tsx";
import { Fragment } from "react";
import { HeaderSlotProvider } from "@/contexts/header-slot-context.tsx";
import { HeaderSlot } from "@/components/layout/header-slot.tsx";

export function Layout() {
  const [sidebarOpenState, setSidebarOpenState] = useSidebarOpenState();
  const matches = useMatches();
  const breadcrumbs = matches.filter(
    (match) =>
      match.handle !== undefined && (match.handle as HandleType).breadcrumb,
  );
  return (
    <HeaderSlotProvider>
      <SidebarProvider
        open={sidebarOpenState}
        onOpenChange={setSidebarOpenState}
      >
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 sticky top-0 bg-background">
            <div className="flex w-full items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  {breadcrumbs.map((match, index) => (
                    <Fragment key={match.id}>
                      {index !== 0 && (
                        <BreadcrumbSeparator className="hidden md:block" />
                      )}
                      <BreadcrumbItem className="hidden md:block">
                        {index !== breadcrumbs.length - 1 ? (
                          (match.handle as HandleType).breadcrumbDisabled ? (
                            <span>{(match.handle as HandleType).title}</span>
                          ) : (
                            <BreadcrumbLink asChild>
                              <NavLink to={match.pathname}>
                                {(match.handle as HandleType).title}
                              </NavLink>
                            </BreadcrumbLink>
                          )
                        ) : (
                          <BreadcrumbPage>
                            {(match.handle as HandleType).title}
                          </BreadcrumbPage>
                        )}
                      </BreadcrumbItem>
                    </Fragment>
                  ))}
                </BreadcrumbList>
              </Breadcrumb>
              <HeaderSlot />
            </div>
          </header>
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            <Outlet />
          </div>
        </SidebarInset>
      </SidebarProvider>
    </HeaderSlotProvider>
  );
}
