import * as React from "react";
import { NavUser } from "@/components/layout/nav-user";
import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useAuth } from "@/contexts/auth-context.tsx";
import { NavWorkflow } from "@/components/layout/nav-workflow.tsx";
import { AppLogo } from "@/components/layout/app-logo.tsx";
import { NavMain } from "@/components/layout/nav-main.tsx";
import { RiDashboard3Line, RiMovie2Line, RiPagesLine } from "@remixicon/react";
import {
  AppWindowMac,
  Blocks,
  Book,
  BookHeadphones,
  Container,
  HardDrive,
  Music,
  SettingsIcon,
  TvMinimalPlay,
  Workflow,
} from "lucide-react";
import { NavGroupMenu } from "@/components/layout/nav-group-menu.tsx";
import { NavFixed } from "@/components/layout/nav-fixed.tsx";

const menu = {
  main: [
    {
      title: "首页",
      url: "/",
      icon: RiDashboard3Line,
    },
  ],
  workflow: {
    title: "工作流",
    items: [
      {
        title: "所有工作流",
        url: "/workflow/index",
        icon: Workflow,
      },
      {
        title: "正在执行",
        url: "/workflow/running",
        icon: AppWindowMac,
      },
    ],
  },
  media: {
    title: "媒体",
    items: [
      {
        title: "电影",
        url: "#",
        icon: RiMovie2Line,
      },
      {
        title: "电视节目",
        url: "#",
        icon: TvMinimalPlay,
      },
      {
        title: "音乐",
        url: "#",
        icon: Music,
      },
      {
        title: "电子书",
        url: "#",
        icon: Book,
      },
      {
        title: "有声书",
        url: "#",
        icon: BookHeadphones,
      },
    ],
  },
  resource: {
    title: "资源",
    items: [
      {
        title: "容器",
        url: "/resource/container",
        icon: Container,
      },
      {
        title: "存储",
        url: "/resource/storage",
        icon: HardDrive,
      },
      {
        title: "站点",
        url: "/resource/site",
        icon: RiPagesLine,
      },
    ],
  },
  other: {
    items: [
      {
        title: "插件",
        url: "/plugin/index",
        icon: Blocks,
      },
      {
        title: "设置",
        url: "/settings",
        icon: SettingsIcon,
      },
    ],
  },
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = useAuth();

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <AppLogo />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={menu.main} />
        <NavWorkflow />
        <NavGroupMenu {...menu.media} />
        <NavGroupMenu {...menu.resource} />
      </SidebarContent>
      <NavFixed items={menu.other.items} />
      <SidebarFooter>
        <NavUser user={user!} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
