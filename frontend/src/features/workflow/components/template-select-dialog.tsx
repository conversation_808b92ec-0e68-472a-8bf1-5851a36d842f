import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils.ts";
import { File, Plus, Search } from "lucide-react";
import { Label } from "@/components/ui/label.tsx";
import { Tag } from "@/types/workflow.ts";
import { Workflow } from "@/proto/v1/workflow_service_pb.ts";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { useCallback, useRef } from "react";
import {
  CreateDialog,
  CreateDialogRefObject,
} from "@/features/workflow/components/create-dialog.tsx";

interface TemplateSelectDialogProps {}

export function TemplateSelectDialog(props: TemplateSelectDialogProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size="sm">创建</Button>
      </DialogTrigger>
      <DialogContent className="max-w-[90vw]! h-[90vh] p-0 overflow-hidden">
        <VisuallyHidden>
          <DialogTitle>创建工作流</DialogTitle>
          <DialogDescription>根据模板创建工作流</DialogDescription>
        </VisuallyHidden>
        <Content />
      </DialogContent>
    </Dialog>
  );
}

function Content({ className }: React.ComponentProps<"div">) {
  const createDialogRef = useRef<CreateDialogRefObject>(null);
  const tags: Tag[] = [];

  const workflows: Workflow[] = [];

  const handleWorkflowClick = useCallback((workflow: Workflow) => {
    createDialogRef.current?.open(workflow);
  }, []);

  const handleCreateEmptyWorkflow = useCallback(() => {
    createDialogRef.current?.open();
  }, []);

  return (
    <div
      className={cn(
        "flex h-full min-h-0 select-none gap-2 relative",
        className,
      )}
    >
      <Sidebar className="relative! h-full w-48">
        <SidebarHeader>
          <h1 className="leading-none text-lg font-semibold p-2">创建工作流</h1>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                className="cursor-pointer"
                asChild
                onClick={handleCreateEmptyWorkflow}
              >
                <div>
                  <File />
                  <span>空白工作流</span>
                </div>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <Tags tags={tags} />
        </SidebarContent>
      </Sidebar>
      <div className="flex flex-col flex-1 py-3">
        <SearchForm />
        <WorkflowList
          className="pl-2 pr-8 pt-2 overflow-y-scroll"
          workflows={workflows}
          onWorkflowClick={handleWorkflowClick}
        />
      </div>
      <CreateDialog ref={createDialogRef} />
    </div>
  );
}

function Tags({ tags }: { tags: Tag[] }) {
  return (
    <SidebarGroup className="flex">
      <SidebarGroupLabel>标签</SidebarGroupLabel>
      <SidebarMenu>
        <SidebarMenuItem key="all">
          <SidebarMenuButton className="cursor-pointer" asChild>
            <span>全部</span>
          </SidebarMenuButton>
        </SidebarMenuItem>
        {tags.map((item) => (
          <SidebarMenuItem key={item.id}>
            <SidebarMenuButton className="cursor-pointer" asChild>
              <span>{item.name}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}

function SearchForm() {
  return (
    <SidebarGroup className="w-96">
      <SidebarGroupContent className="relative">
        <Label htmlFor="search" className="sr-only">
          搜索
        </Label>
        <SidebarInput
          id="search"
          placeholder="搜索工作流"
          className="pl-8 h-10"
        />
        <Search className="pointer-events-none absolute top-1/2 left-2 size-4 -translate-y-1/2 opacity-50 select-none" />
      </SidebarGroupContent>
    </SidebarGroup>
  );
}

function WorkflowList({
  workflows,
  className,
  onWorkflowClick,
}: React.ComponentProps<"div"> & {
  workflows: Workflow[];
  onWorkflowClick?: (workflow: Workflow) => void;
}) {
  return (
    <div
      className={cn(
        "grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4",
        className,
      )}
    >
      {workflows.map((workflow) => (
        <WorkflowItem
          workflow={workflow}
          onWorkflowClick={onWorkflowClick}
          key={workflow.id}
        />
      ))}
    </div>
  );
}

function WorkflowItem({
  workflow,
  onWorkflowClick,
}: {
  workflow: Workflow;
  onWorkflowClick?: (workflow: Workflow) => void;
}) {
  return (
    <Card
      className="group hover:shadow-lg transition-all duration-200 gap-2"
      onClick={() => onWorkflowClick?.(workflow)}
    >
      <CardHeader className="flex items-center">
        <img className="w-10 h-10 rounded-lg bg-blue-300" src="" alt="" />
        <CardTitle>{workflow.name}</CardTitle>
      </CardHeader>
      <CardContent>
        <CardDescription className="text-xs line-clamp-2">
          {workflow.description}
        </CardDescription>
      </CardContent>
      <CardFooter>
        <Button
          size="sm"
          variant="default"
          className="w-full opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-200"
        >
          <Plus className="h-4 w-4" />
          使用此工作流
        </Button>
      </CardFooter>
    </Card>
  );
}
