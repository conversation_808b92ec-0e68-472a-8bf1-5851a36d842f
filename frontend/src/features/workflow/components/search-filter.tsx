import {
  Command,
  CommandEmpty,
  Command<PERSON>roup,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ist,
  CommandSeparator,
} from "@/components/ui/command";
import * as React from "react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Command as CommandPrimitive } from "cmdk";
import { cn } from "@/lib/utils.ts";
import { Badge } from "@/components/ui/badge.tsx";
import { X } from "lucide-react";

export interface Option {
  label: string;
  value: string;
  color: string;
  group?: string;
}

interface GroupOption {
  [key: string]: Option[];
}

interface SearchFilterProps {
  defaultOptions: Option[];
  onSelectedChange?: (options: Option[]) => void;
  onKeywordChange?: (keyword: string) => void;
}

function transToGroupOption(options: Option[]) {
  const groupOption: GroupOption = {};
  for (const option of options) {
    let groupKey = "";
    if (option.group !== undefined) {
      groupKey = option.group;
    }
    if (groupOption[groupKey] === undefined) {
      groupOption[groupKey] = [];
    }
    groupOption[groupKey].push(option);
  }
  return groupOption;
}

function removePickedOption(groupOption: GroupOption, selected: Option[]) {
  const newGroupOption = JSON.parse(JSON.stringify(groupOption)) as GroupOption;

  for (const [key, options] of Object.entries(newGroupOption)) {
    newGroupOption[key] = options.filter(
      (o) => !selected.find((s) => s.value === o.value),
    );
  }
  return newGroupOption;
}

export function SearchFilter({
  defaultOptions = [],
  className,
  onSelectedChange,
  onKeywordChange,
  ...props
}: SearchFilterProps & React.ComponentProps<typeof Command>) {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [open, setOpen] = useState(false);
  const filterRef = useRef<HTMLDivElement>(null);
  const [inputValue, setInputValue] = React.useState("");
  const [selected, setSelected] = React.useState<Option[]>([]);
  const [groupOption, setGroupOption] = React.useState<GroupOption>(
    transToGroupOption(defaultOptions),
  );

  function handleClickOutside(e: MouseEvent | TouchEvent) {
    if (filterRef.current && !filterRef.current.contains(e.target as Element)) {
      setOpen(false);
    }
  }

  function handleInputChange(value: string) {
    setInputValue(value);
    onKeywordChange?.(value);
  }

  const handleSelect = useCallback(
    (option: Option) => {
      if (selected.find((s) => s.value === option.value)) {
        return;
      }
      const newSelected = [...selected, option];
      setSelected(newSelected);
      onSelectedChange?.(newSelected);
      inputRef.current?.focus();
    },
    [onSelectedChange, selected],
  );

  const handleSearchClick = useCallback(() => {
    handleSelect({
      label: inputValue,
      value: inputValue,
      color: "oklch(0.60 0 0)",
      group: "关键词",
    });
    setInputValue("");
  }, [handleSelect, inputValue]);

  const handleUnselect = useCallback(
    (option: Option) => {
      const newOptions = selected.filter((s) => s.value !== option.value);
      setSelected(newOptions);
    },
    [selected],
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Delete" || e.key === "Backspace") {
        if (inputValue === "" && selected.length > 0) {
          handleUnselect(selected[selected.length - 1]);
        }
      }
    },
    [handleUnselect, inputValue, selected],
  );

  function handleInputFocus() {
    setOpen(true);
  }

  useEffect(() => {
    if (open) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("touchend", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("touchend", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("touchend", handleClickOutside);
    };
  }, [open]);

  useEffect(() => {
    setGroupOption(transToGroupOption(defaultOptions));
  }, [defaultOptions]);

  const selectable = useMemo(
    () => removePickedOption(groupOption, selected),
    [selected, groupOption],
  );

  return (
    <>
      <Command
        className={cn(
          "rounded-lg border h-auto overflow-visible ring-offset-background focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-ring",
          className,
        )}
        {...props}
        ref={filterRef}
      >
        <div
          data-slot="command-input-wrapper"
          className={cn("flex min-h-9 items-center gap-2 px-2", {
            "py-2": selected.length !== 0,
          })}
          onClick={() => {
            inputRef.current?.focus();
          }}
        >
          <div className="flex flex-wrap gap-1">
            {selected.length > 0 &&
              selected.map((option: Option) => (
                <Badge
                  className="hover:opacity-70 rounded-full inline-flex"
                  key={option.value}
                  style={{
                    background: option.color,
                  }}
                >
                  {option.group}：{option.label}
                  <button
                    type="button"
                    className="ml-1 rounded-full outline-none ring-offset-background cursor-pointer"
                    onClick={() => handleUnselect(option)}
                  >
                    <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                  </button>
                </Badge>
              ))}
            <CommandPrimitive.Input
              ref={inputRef}
              onFocus={handleInputFocus}
              data-slot="command-input"
              className={cn(
                "placeholder:text-muted-foreground flex flex-1 bg-transparent text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",
                {
                  "ml-1": selected.length !== 0,
                },
              )}
              value={inputValue}
              onValueChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder="搜索或筛选工作流"
            />
          </div>
        </div>
        <div className="relative">
          {open && (
            <CommandList className="absolute top-1 z-10 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in">
              <CommandEmpty>没有任何内容</CommandEmpty>
              {inputValue !== "" && (
                <CommandGroup heading="搜索">
                  <CommandItem onSelect={handleSearchClick}>
                    <span>{inputValue}</span>
                  </CommandItem>
                </CommandGroup>
              )}
              <CommandSeparator />
              {Object.entries(selectable).map(
                ([key, options]) =>
                  options.length > 0 && (
                    <CommandGroup heading={key} key={key}>
                      {options.map((option) => (
                        <CommandItem
                          key={option.value}
                          onSelect={() => handleSelect(option)}
                        >
                          <Badge style={{ background: option.color }}>
                            {option.label}
                          </Badge>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  ),
              )}
            </CommandList>
          )}
        </div>
      </Command>
    </>
  );
}
