import { useEffect, useState } from "react";
import { NodeSchema } from "@/components/workflow/types/workflow.ts";
import { useListNodeDefinitions } from "@/api/node-definition.ts";

export function useAvailableNodes() {
  const [availableNodes, setAvailableNodes] = useState<NodeSchema[]>([]);
  const [requestNodeDefinitions] = useListNodeDefinitions();

  useEffect(() => {
    const fetchData = async () => {
      const nodeSchemas = await requestNodeDefinitions();
      setAvailableNodes(nodeSchemas);
    };
    fetchData();
    return () => {};
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { availableNodes };
}
