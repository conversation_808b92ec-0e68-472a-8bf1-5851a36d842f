import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { EditableLabel } from "@/components/editable-label.tsx";
import { But<PERSON> } from "@/components/ui/button.tsx";
import { RiHistoryLine, RiPlayLine } from "@remixicon/react";
import { ChevronLeft, Loader2, Loader2Icon } from "lucide-react";
import { Link, useParams } from "react-router";
import {
  NodeHandle,
  NodeParam,
  NodeParamType,
  WorkflowEdge,
  WorkflowNode,
  WorkflowRefObject,
} from "@/components/workflow/types/workflow.ts";
import { Workflow as WorkflowComp } from "@/components/workflow/workflow.tsx";
import { useClient } from "@/hooks/use-client.ts";
import {
  UpdateWorkflowRequest,
  Workflow,
  WorkflowLink as WorkflowLinkPb,
  WorkflowLinkRequest,
  WorkflowLinkRequestSchema,
  WorkflowNode as WorkflowNodePb,
  WorkflowNodePosition,
  WorkflowNodeRequest,
  WorkflowNodeRequestSchema,
  WorkflowResponse,
  WorkflowService,
  WorkflowViewport,
} from "@/proto/v1/workflow_service_pb";
import { useGrpcRequestWithToken } from "@/hooks/use-grpc-request";
import { OnlyIdRequest } from "@/proto/v1/common_pb.ts";
import { create } from "@bufbuild/protobuf";
import { HandleType, Position, Viewport } from "@xyflow/react";
import {
  DEFAULT_VIEWPORT,
  NodeTypeEnum,
} from "@/components/workflow/nodes/constants.ts";
import { successToast } from "@/lib/toast.ts";
import {
  NodeParam as WorkflowNodeParam,
  NodeParamSchema,
  NodePort,
} from "@/proto/v1/node_pb";
import { useAvailableNodes } from "@/features/workflow/hooks/use-available-nodes.ts";
import { NodeSelector } from "@/features/workflow/components/node-selector.tsx";

export function WorkflowEditor() {
  const routeParams = useParams();
  const workflowClient = useClient(WorkflowService);
  const [getWorkflowRequest, loading] = useGrpcRequestWithToken<
    OnlyIdRequest,
    WorkflowResponse
  >(workflowClient.getById);
  const [updateWorkflowRequest, saveLoading] = useGrpcRequestWithToken<
    UpdateWorkflowRequest,
    WorkflowResponse
  >(workflowClient.update);
  const [name, setName] = useState("");
  const [workflow, setWorkflow] = useState<Workflow>();
  const [nodes, setNodes] = useState<WorkflowNode[]>([]);
  const [edges, setEdges] = useState<WorkflowEdge[]>([]);
  const [viewport, setViewport] = useState<Viewport>(DEFAULT_VIEWPORT);

  const { availableNodes } = useAvailableNodes();

  const workflowRef = useRef<WorkflowRefObject>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const { workflow } = await getWorkflowRequest({
          id: routeParams.workflowId!,
        });
        console.log(workflow);
        setName(workflow?.name || "");
        setWorkflow(workflow);
        setNodes(
          transformWorkflowNodeDTOsToWorkflowNodes(workflow?.nodes || []),
        );
        setEdges(
          transformWorkflowEdgeDTOsToWorkflowEdges(workflow?.links || []),
        );
        setViewport(workflow?.viewport || DEFAULT_VIEWPORT);
      } catch (err) {
        console.log(err);
      }
    };
    fetchData();
    return () => {};
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSave = useCallback(async () => {
    const nodes = workflowRef.current?.getNodes();
    const edges = workflowRef.current?.getEdges();
    const viewport = workflowRef.current?.getViewport();
    console.log(nodes, edges, viewport);
    if (workflow) {
      console.log({
        id: workflow.id,
        name: name,
        nodes: transformWorkflowNodesToWorkflowNodeDTOs(nodes || []),
        links: transformWorkflowEdges2WorkflowEdgeDTOs(edges || []),
        viewport: viewport as unknown as WorkflowViewport,
      });
      const res = await updateWorkflowRequest({
        id: workflow.id,
        name: name,
        nodes: transformWorkflowNodesToWorkflowNodeDTOs(nodes || []),
        links: transformWorkflowEdges2WorkflowEdgeDTOs(edges || []),
        viewport: viewport as unknown as WorkflowViewport,
      });
      console.log(res);
      successToast("已保存");
    }
  }, [name, updateWorkflowRequest, workflow]);

  return (
    <div className="flex flex-col h-dvh">
      <header className="flex px-4 items-center shadow h-16 z-10">
        <Link to="/workflow/index">
          <Button variant="ghost" size="sm">
            <ChevronLeft />
          </Button>
        </Link>
        <EditableLabel
          className="mx-2"
          value={name}
          onChange={(v) => setName(v)}
          placeholder="名称"
        />
        {!loading && (
          <div className="flex flex-1 justify-end gap-2">
            <Button variant="outline" size="sm">
              <RiHistoryLine />
            </Button>
            <Button variant="outline" size="sm">
              <RiPlayLine />
              运行
            </Button>
            <Button size="sm" onClick={handleSave} disabled={saveLoading}>
              {saveLoading && <Loader2Icon className="animate-spin" />}
              保存
            </Button>
          </div>
        )}
      </header>
      <section className="flex-1 relative">
        {loading ? (
          <div className="flex w-full h-full justify-center items-center">
            <Loader2 className="animate-spin" />
          </div>
        ) : (
          <WorkflowComp
            initialNodes={nodes}
            initialEdges={edges}
            initialViewport={viewport}
            availableNodes={availableNodes}
            ref={workflowRef}
          >
            <NodeSelector
              className="absolute left-4 top-4 z-10"
              availableNodes={availableNodes}
            />
          </WorkflowComp>
        )}
      </section>
    </div>
  );
}

function transformWorkflowNodesToWorkflowNodeDTOs(
  nodes: WorkflowNode[],
): WorkflowNodeRequest[] {
  const newNodes: WorkflowNodeRequest[] = [];
  for (const node of nodes) {
    newNodes.push(
      create(WorkflowNodeRequestSchema, {
        id: node.id,
        name: node.data.name,
        description: node.data.description || "",
        icon: node.data.icon || "",
        type: node.data.type!,
        version: node.data.version,
        data: { input: node.data.input, output: node.data.output },
        pluginName: node.data.plugin_name || "",
        pluginVersion: node.data.plugin_version || "",
        inputParams: transformNodeParamsToNodeParamDTOs(
          node.data.input_params || [],
        ),
        inputValues: node.data.input_values || {},
        outputParams: transformNodeParamsToNodeParamDTOs(
          node.data.output_params || [],
        ),
        outputValues: node.data.output_values || {},
        inputPorts: node.data.input_ports,
        outputPorts: node.data.output_ports,
        position: node.position as WorkflowNodePosition,
      }),
    );
  }
  return newNodes;
}

function transformWorkflowEdges2WorkflowEdgeDTOs(
  edges: WorkflowEdge[],
): WorkflowLinkRequest[] {
  const newEdges: WorkflowLinkRequest[] = [];
  for (const edge of edges) {
    newEdges.push(
      create(WorkflowLinkRequestSchema, {
        id: edge.id,
        fromNodeId: edge.source,
        toNodeId: edge.target,
        fromPortId: edge.sourceHandle || "",
        toPortId: edge.targetHandle || "",
        type: edge.type!,
      }),
    );
  }
  return newEdges;
}

function transformNodeParamsToNodeParamDTOs(params: NodeParam[]) {
  const nodeParams: WorkflowNodeParam[] = [];
  for (const param of params) {
    nodeParams.push(
      create(NodeParamSchema, {
        id: param.id,
        label: param.label,
        description: param.description || "",
        type: param.type!,
        value: param.value as string,
        required: true,
      }),
    );
  }
  return nodeParams;
}

function transformWorkflowNodeDTOsToWorkflowNodes(
  nodes: WorkflowNodePb[],
): WorkflowNode[] {
  const newNodes: WorkflowNode[] = [];
  for (const node of nodes) {
    newNodes.push({
      id: node.id,
      type: NodeTypeEnum.CustomNode,
      data: {
        name: node.name,
        description: node.description,
        icon: node.icon,
        type: node.type,
        version: node.version,
        input_params: transformNodeParamDTOsToNodeParams(node.inputParams),
        input_values: node.inputValues || {},
        output_params: transformNodeParamDTOsToNodeParams(node.outputParams),
        output_values: node.outputValues || {},
        input_ports: transformNodePortDTOsToNodeHandles(node.inputPorts),
        output_ports: transformNodePortDTOsToNodeHandles(node.outputPorts),
      },
      position: node.position as WorkflowNodePosition,
    });
  }
  return newNodes;
}

function transformNodeParamDTOsToNodeParams(params: WorkflowNodeParam[]) {
  const nodeParams: NodeParam[] = [];
  for (const param of params) {
    nodeParams.push({
      id: param.id,
      label: param.label,
      description: param.description,
      type: param.type as NodeParamType,
      value: param.value,
      required: param.required,
    });
  }
  return nodeParams;
}

function transformWorkflowEdgeDTOsToWorkflowEdges(
  edges: WorkflowLinkPb[],
): WorkflowEdge[] {
  const newEdges: WorkflowEdge[] = [];
  for (const edge of edges) {
    newEdges.push({
      id: edge.id,
      source: edge.fromNodeId,
      target: edge.toNodeId,
      sourceHandle: edge.fromPortId,
      targetHandle: edge.toPortId,
      type: edge.type,
    });
  }
  return newEdges;
}

function transformNodePortDTOsToNodeHandles(ports: NodePort[]) {
  const handles: NodeHandle[] = [];
  for (const port of ports) {
    handles.push({
      id: port.id,
      type: port.type as HandleType,
      position: port.position as Position,
    });
  }
  return handles;
}
