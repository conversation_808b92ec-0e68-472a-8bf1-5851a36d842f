import { createGrpcWebTransport } from "@connectrpc/connect-web";
import type { DescService } from "@bufbuild/protobuf";
import { createClient } from "@connectrpc/connect";

const transport = createGrpcWebTransport({
  baseUrl: import.meta.env.VITE_GRPC_SERVICE_URL,
  useBinaryFormat: true,
});

export function CreateClient<T extends DescService>(service: T) {
  return createClient(service, transport);
}
