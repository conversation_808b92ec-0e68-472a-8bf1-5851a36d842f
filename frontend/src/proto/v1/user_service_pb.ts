// @generated by protoc-gen-es v2.6.2 with parameter "target=ts"
// @generated from file v1/user_service.proto (package v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Status } from "./common_pb";
import { file_v1_common } from "./common_pb";
import type { EmptySchema } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file v1/user_service.proto.
 */
export const file_v1_user_service: GenFile = /*@__PURE__*/
  fileDesc("ChV2MS91c2VyX3NlcnZpY2UucHJvdG8SAnYxInoKBFVzZXISCgoCaWQYASABKAkSEAoIdXNlcm5hbWUYAiABKAkSEAoIbmlja25hbWUYAyABKAkSGgoGc3RhdHVzGAQgASgOMgoudjEuU3RhdHVzEhIKCmNyZWF0ZWRfYXQYFCABKAkSEgoKdXBkYXRlZF9hdBgVIAEoCSJJChFDcmVhdGVVc2VyUmVxdWVzdBIQCgh1c2VybmFtZRgBIAEoCRIQCghwYXNzd29yZBgCIAEoCRIQCghuaWNrbmFtZRgDIAEoCSIsChJDcmVhdGVVc2VyUmVzcG9uc2USFgoEdXNlchgBIAEoCzIILnYxLlVzZXIyegoLVXNlclNlcnZpY2USNwoGQ3JlYXRlEhUudjEuQ3JlYXRlVXNlclJlcXVlc3QaFi52MS5DcmVhdGVVc2VyUmVzcG9uc2USMgoOR2V0Q3VycmVudFVzZXISFi5nb29nbGUucHJvdG9idWYuRW1wdHkaCC52MS5Vc2VyQh9aHXJlc2Zsb3cvcHJvdG8vZ2VuZXJhdGVkX2dvL3YxYgZwcm90bzM", [file_v1_common, file_google_protobuf_empty]);

/**
 * @generated from message v1.User
 */
export type User = Message<"v1.User"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string username = 2;
   */
  username: string;

  /**
   * @generated from field: string nickname = 3;
   */
  nickname: string;

  /**
   * @generated from field: v1.Status status = 4;
   */
  status: Status;

  /**
   * @generated from field: string created_at = 20;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 21;
   */
  updatedAt: string;
};

/**
 * Describes the message v1.User.
 * Use `create(UserSchema)` to create a new message.
 */
export const UserSchema: GenMessage<User> = /*@__PURE__*/
  messageDesc(file_v1_user_service, 0);

/**
 * @generated from message v1.CreateUserRequest
 */
export type CreateUserRequest = Message<"v1.CreateUserRequest"> & {
  /**
   * @generated from field: string username = 1;
   */
  username: string;

  /**
   * @generated from field: string password = 2;
   */
  password: string;

  /**
   * @generated from field: string nickname = 3;
   */
  nickname: string;
};

/**
 * Describes the message v1.CreateUserRequest.
 * Use `create(CreateUserRequestSchema)` to create a new message.
 */
export const CreateUserRequestSchema: GenMessage<CreateUserRequest> = /*@__PURE__*/
  messageDesc(file_v1_user_service, 1);

/**
 * @generated from message v1.CreateUserResponse
 */
export type CreateUserResponse = Message<"v1.CreateUserResponse"> & {
  /**
   * @generated from field: v1.User user = 1;
   */
  user?: User;
};

/**
 * Describes the message v1.CreateUserResponse.
 * Use `create(CreateUserResponseSchema)` to create a new message.
 */
export const CreateUserResponseSchema: GenMessage<CreateUserResponse> = /*@__PURE__*/
  messageDesc(file_v1_user_service, 2);

/**
 * @generated from service v1.UserService
 */
export const UserService: GenService<{
  /**
   * @generated from rpc v1.UserService.Create
   */
  create: {
    methodKind: "unary";
    input: typeof CreateUserRequestSchema;
    output: typeof CreateUserResponseSchema;
  },
  /**
   * @generated from rpc v1.UserService.GetCurrentUser
   */
  getCurrentUser: {
    methodKind: "unary";
    input: typeof EmptySchema;
    output: typeof UserSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_v1_user_service, 0);

