// @generated by protoc-gen-es v2.6.2 with parameter "target=ts"
// @generated from file v1/node.proto (package v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file v1/node.proto.
 */
export const file_v1_node: GenFile = /*@__PURE__*/
  fileDesc("Cg12MS9ub2RlLnByb3RvEgJ2MSJqCglOb2RlUGFyYW0SCgoCaWQYASABKAkSDAoEdHlwZRgCIAEoCRINCgVsYWJlbBgDIAEoCRITCgtkZXNjcmlwdGlvbhgEIAEoCRINCgV2YWx1ZRgFIAEoCRIQCghyZXF1aXJlZBgKIAEoCCI2CghOb2RlUG9ydBIKCgJpZBgBIAEoCRIMCgR0eXBlGAIgASgJEhAKCHBvc2l0aW9uGAMgASgJQh9aHXJlc2Zsb3cvcHJvdG8vZ2VuZXJhdGVkX2dvL3YxYgZwcm90bzM");

/**
 * @generated from message v1.NodeParam
 */
export type NodeParam = Message<"v1.NodeParam"> & {
  /**
   * @gotags: validate:"required" msg_required:"参数ID不能为空"
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @gotags: validate:"required" msg_required:"参数类型不能为空"
   *
   * @generated from field: string type = 2;
   */
  type: string;

  /**
   * @gotags: validate:"required" msg_required:"参数名称不能为空"
   *
   * @generated from field: string label = 3;
   */
  label: string;

  /**
   * @gotags: validate:"omitempty"
   *
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @gotags: validate:"omitempty"
   *
   * @generated from field: string value = 5;
   */
  value: string;

  /**
   * @generated from field: bool required = 10;
   */
  required: boolean;
};

/**
 * Describes the message v1.NodeParam.
 * Use `create(NodeParamSchema)` to create a new message.
 */
export const NodeParamSchema: GenMessage<NodeParam> = /*@__PURE__*/
  messageDesc(file_v1_node, 0);

/**
 * @generated from message v1.NodePort
 */
export type NodePort = Message<"v1.NodePort"> & {
  /**
   * @gotags: validate:"required" msg_required:"端口ID不能为空"
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string type = 2;
   */
  type: string;

  /**
   * @generated from field: string position = 3;
   */
  position: string;
};

/**
 * Describes the message v1.NodePort.
 * Use `create(NodePortSchema)` to create a new message.
 */
export const NodePortSchema: GenMessage<NodePort> = /*@__PURE__*/
  messageDesc(file_v1_node, 1);

