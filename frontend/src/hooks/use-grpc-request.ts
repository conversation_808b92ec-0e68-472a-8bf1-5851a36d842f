import { useCallback, useState } from "react";
import { CallOptions, Code, ConnectError } from "@connectrpc/connect";
import { useToken } from "@/hooks/use-token.ts";
import { useAuth } from "@/contexts/auth-context.tsx";

type GrpcRequestOrResponse<T> = Omit<T, "$typeName" | "$unknown">;

type GrpcMethod<Req, Res> = (
  req: GrpcRequestOrResponse<Req>,
  options?: CallOptions,
) => Promise<GrpcRequestOrResponse<Res>>;

export interface GrpcError {
  message: string;
}

export function useGrpcRequest<Req, Res>(method: GrpcMethod<Req, Res>) {
  const [loading, setLoading] = useState(false);

  const request = useCallback(
    async (req: GrpcRequestOrResponse<Req>, options?: CallOptions) => {
      try {
        setLoading(true);
        return await method(req, options);
      } catch (err) {
        const error: GrpcError = { message: "" };
        if (err instanceof ConnectError) {
          error.message = err.rawMessage;
        } else {
          error.message = (err as Error).message;
        }
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [method],
  );

  return [request, loading] as const;
}

export function useGrpcRequestWithToken<Req, Res>(
  method: GrpcMethod<Req, Res>,
) {
  const [loading, setLoading] = useState(false);
  const [token] = useToken();
  const { logout } = useAuth();

  const request = useCallback(
    async (req: GrpcRequestOrResponse<Req>, options?: CallOptions) => {
      try {
        setLoading(true);
        return await method(req, {
          ...options,
          headers: { ...options?.headers, Authorization: `Bearer ${token}` },
        });
      } catch (err) {
        const error: GrpcError = { message: "" };
        if (err instanceof ConnectError) {
          if (err.code == Code.Unauthenticated) {
            logout();
          }
          error.message = err.rawMessage;
        } else {
          error.message = (err as Error).message;
        }
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [logout, method, token],
  );

  return [request, loading] as const;
}
