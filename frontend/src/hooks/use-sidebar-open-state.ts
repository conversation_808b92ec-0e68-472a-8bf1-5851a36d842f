import { useLocalStorage } from "react-use";
import { useState } from "react";

const SIDEBAR_STORAGE_NAME = "sidebar_open_state";

export function useSidebarOpenState() {
  const [storageValue, setStorageValue] = useLocalStorage(
    SIDEBAR_STORAGE_NAME,
    true,
  );
  const [openState, setOpenState] = useState(storageValue ?? true);

  function updateOpenState(state: boolean) {
    setStorageValue(state);
    setOpenState(state);
  }

  return [openState, updateOpenState] as const;
}
