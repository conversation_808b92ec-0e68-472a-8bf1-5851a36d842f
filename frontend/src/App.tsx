import { StrictMode } from "react";
import "./App.css";
import { RouterProvider } from "react-router";
import router from "@/router.tsx";
import { Toaster } from "@/components/ui/sonner.tsx";
import { ThemeProvider } from "@/contexts/theme-context.tsx";

function App() {
  return (
    <>
      <StrictMode>
        <ThemeProvider defaultTheme="system" storageKey="inflow-theme">
          <RouterProvider router={router} />
          <Toaster />
        </ThemeProvider>
      </StrictMode>
    </>
  );
}

export default App;
