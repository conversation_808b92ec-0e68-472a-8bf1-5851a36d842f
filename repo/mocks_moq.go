// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: matryer

package repo

import (
	"context"
	"resflow/ent"
	"sync"

	"github.com/google/uuid"
)

// Ensure that MoqUser does implement User.
// If this is not the case, regenerate this file with mockery.
var _ User = &MoqUser{}

// MoqUser is a mock implementation of User.
//
//	func TestSomethingThatUsesUser(t *testing.T) {
//
//		// make and configure a mocked User
//		mockedUser := &MoqUser{
//			CreateFunc: func(ctx context.Context, user *ent.User) (*ent.User, error) {
//				panic("mock out the Create method")
//			},
//			GetByIdFunc: func(ctx context.Context, id uuid.UUID) (*ent.User, error) {
//				panic("mock out the GetById method")
//			},
//			GetByUsernameFunc: func(ctx context.Context, username string) (*ent.User, error) {
//				panic("mock out the GetByUsername method")
//			},
//		}
//
//		// use mockedUser in code that requires User
//		// and then make assertions.
//
//	}
type MoqUser struct {
	// CreateFunc mocks the Create method.
	CreateFunc func(ctx context.Context, user *ent.User) (*ent.User, error)

	// GetByIdFunc mocks the GetById method.
	GetByIdFunc func(ctx context.Context, id uuid.UUID) (*ent.User, error)

	// GetByUsernameFunc mocks the GetByUsername method.
	GetByUsernameFunc func(ctx context.Context, username string) (*ent.User, error)

	// calls tracks calls to the methods.
	calls struct {
		// Create holds details about calls to the Create method.
		Create []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// User is the user argument value.
			User *ent.User
		}
		// GetById holds details about calls to the GetById method.
		GetById []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// ID is the id argument value.
			ID uuid.UUID
		}
		// GetByUsername holds details about calls to the GetByUsername method.
		GetByUsername []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Username is the username argument value.
			Username string
		}
	}
	lockCreate        sync.RWMutex
	lockGetById       sync.RWMutex
	lockGetByUsername sync.RWMutex
}

// Create calls CreateFunc.
func (mock *MoqUser) Create(ctx context.Context, user *ent.User) (*ent.User, error) {
	if mock.CreateFunc == nil {
		panic("MoqUser.CreateFunc: method is nil but User.Create was just called")
	}
	callInfo := struct {
		Ctx  context.Context
		User *ent.User
	}{
		Ctx:  ctx,
		User: user,
	}
	mock.lockCreate.Lock()
	mock.calls.Create = append(mock.calls.Create, callInfo)
	mock.lockCreate.Unlock()
	return mock.CreateFunc(ctx, user)
}

// CreateCalls gets all the calls that were made to Create.
// Check the length with:
//
//	len(mockedUser.CreateCalls())
func (mock *MoqUser) CreateCalls() []struct {
	Ctx  context.Context
	User *ent.User
} {
	var calls []struct {
		Ctx  context.Context
		User *ent.User
	}
	mock.lockCreate.RLock()
	calls = mock.calls.Create
	mock.lockCreate.RUnlock()
	return calls
}

// GetById calls GetByIdFunc.
func (mock *MoqUser) GetById(ctx context.Context, id uuid.UUID) (*ent.User, error) {
	if mock.GetByIdFunc == nil {
		panic("MoqUser.GetByIdFunc: method is nil but User.GetById was just called")
	}
	callInfo := struct {
		Ctx context.Context
		ID  uuid.UUID
	}{
		Ctx: ctx,
		ID:  id,
	}
	mock.lockGetById.Lock()
	mock.calls.GetById = append(mock.calls.GetById, callInfo)
	mock.lockGetById.Unlock()
	return mock.GetByIdFunc(ctx, id)
}

// GetByIdCalls gets all the calls that were made to GetById.
// Check the length with:
//
//	len(mockedUser.GetByIdCalls())
func (mock *MoqUser) GetByIdCalls() []struct {
	Ctx context.Context
	ID  uuid.UUID
} {
	var calls []struct {
		Ctx context.Context
		ID  uuid.UUID
	}
	mock.lockGetById.RLock()
	calls = mock.calls.GetById
	mock.lockGetById.RUnlock()
	return calls
}

// GetByUsername calls GetByUsernameFunc.
func (mock *MoqUser) GetByUsername(ctx context.Context, username string) (*ent.User, error) {
	if mock.GetByUsernameFunc == nil {
		panic("MoqUser.GetByUsernameFunc: method is nil but User.GetByUsername was just called")
	}
	callInfo := struct {
		Ctx      context.Context
		Username string
	}{
		Ctx:      ctx,
		Username: username,
	}
	mock.lockGetByUsername.Lock()
	mock.calls.GetByUsername = append(mock.calls.GetByUsername, callInfo)
	mock.lockGetByUsername.Unlock()
	return mock.GetByUsernameFunc(ctx, username)
}

// GetByUsernameCalls gets all the calls that were made to GetByUsername.
// Check the length with:
//
//	len(mockedUser.GetByUsernameCalls())
func (mock *MoqUser) GetByUsernameCalls() []struct {
	Ctx      context.Context
	Username string
} {
	var calls []struct {
		Ctx      context.Context
		Username string
	}
	mock.lockGetByUsername.RLock()
	calls = mock.calls.GetByUsername
	mock.lockGetByUsername.RUnlock()
	return calls
}

// Ensure that MoqWorkflow does implement Workflow.
// If this is not the case, regenerate this file with mockery.
var _ Workflow = &MoqWorkflow{}

// MoqWorkflow is a mock implementation of Workflow.
//
//	func TestSomethingThatUsesWorkflow(t *testing.T) {
//
//		// make and configure a mocked Workflow
//		mockedWorkflow := &MoqWorkflow{
//			CreateFunc: func(ctx context.Context, user *ent.User, w *ent.Workflow, nodes []*ent.WorkflowNode, links []*ent.WorkflowLink) (*ent.Workflow, error) {
//				panic("mock out the Create method")
//			},
//			CreateWithTxFunc: func(ctx context.Context, userId uuid.UUID, w *ent.Workflow, createCall func(tx *ent.Tx, workflow *ent.Workflow) error) (*ent.Workflow, error) {
//				panic("mock out the CreateWithTx method")
//			},
//			DeleteByIdFunc: func(ctx context.Context, id uuid.UUID, deleteCall func(tx *ent.Tx) error) error {
//				panic("mock out the DeleteById method")
//			},
//			GetByIdFunc: func(ctx context.Context, id uuid.UUID) (*ent.Workflow, error) {
//				panic("mock out the GetById method")
//			},
//			ListFunc: func(ctx context.Context) ([]*ent.Workflow, error) {
//				panic("mock out the List method")
//			},
//			UpdateWithTxFunc: func(ctx context.Context, w *ent.Workflow, updateCall func(tx *ent.Tx) error) (*ent.Workflow, error) {
//				panic("mock out the UpdateWithTx method")
//			},
//		}
//
//		// use mockedWorkflow in code that requires Workflow
//		// and then make assertions.
//
//	}
type MoqWorkflow struct {
	// CreateFunc mocks the Create method.
	CreateFunc func(ctx context.Context, user *ent.User, w *ent.Workflow, nodes []*ent.WorkflowNode, links []*ent.WorkflowLink) (*ent.Workflow, error)

	// CreateWithTxFunc mocks the CreateWithTx method.
	CreateWithTxFunc func(ctx context.Context, userId uuid.UUID, w *ent.Workflow, createCall func(tx *ent.Tx, workflow *ent.Workflow) error) (*ent.Workflow, error)

	// DeleteByIdFunc mocks the DeleteById method.
	DeleteByIdFunc func(ctx context.Context, id uuid.UUID, deleteCall func(tx *ent.Tx) error) error

	// GetByIdFunc mocks the GetById method.
	GetByIdFunc func(ctx context.Context, id uuid.UUID) (*ent.Workflow, error)

	// ListFunc mocks the List method.
	ListFunc func(ctx context.Context) ([]*ent.Workflow, error)

	// UpdateWithTxFunc mocks the UpdateWithTx method.
	UpdateWithTxFunc func(ctx context.Context, w *ent.Workflow, updateCall func(tx *ent.Tx) error) (*ent.Workflow, error)

	// calls tracks calls to the methods.
	calls struct {
		// Create holds details about calls to the Create method.
		Create []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// User is the user argument value.
			User *ent.User
			// W is the w argument value.
			W *ent.Workflow
			// Nodes is the nodes argument value.
			Nodes []*ent.WorkflowNode
			// Links is the links argument value.
			Links []*ent.WorkflowLink
		}
		// CreateWithTx holds details about calls to the CreateWithTx method.
		CreateWithTx []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// UserId is the userId argument value.
			UserId uuid.UUID
			// W is the w argument value.
			W *ent.Workflow
			// CreateCall is the createCall argument value.
			CreateCall func(tx *ent.Tx, workflow *ent.Workflow) error
		}
		// DeleteById holds details about calls to the DeleteById method.
		DeleteById []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// ID is the id argument value.
			ID uuid.UUID
			// DeleteCall is the deleteCall argument value.
			DeleteCall func(tx *ent.Tx) error
		}
		// GetById holds details about calls to the GetById method.
		GetById []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// ID is the id argument value.
			ID uuid.UUID
		}
		// List holds details about calls to the List method.
		List []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
		}
		// UpdateWithTx holds details about calls to the UpdateWithTx method.
		UpdateWithTx []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// W is the w argument value.
			W *ent.Workflow
			// UpdateCall is the updateCall argument value.
			UpdateCall func(tx *ent.Tx) error
		}
	}
	lockCreate       sync.RWMutex
	lockCreateWithTx sync.RWMutex
	lockDeleteById   sync.RWMutex
	lockGetById      sync.RWMutex
	lockList         sync.RWMutex
	lockUpdateWithTx sync.RWMutex
}

// Create calls CreateFunc.
func (mock *MoqWorkflow) Create(ctx context.Context, user *ent.User, w *ent.Workflow, nodes []*ent.WorkflowNode, links []*ent.WorkflowLink) (*ent.Workflow, error) {
	if mock.CreateFunc == nil {
		panic("MoqWorkflow.CreateFunc: method is nil but Workflow.Create was just called")
	}
	callInfo := struct {
		Ctx   context.Context
		User  *ent.User
		W     *ent.Workflow
		Nodes []*ent.WorkflowNode
		Links []*ent.WorkflowLink
	}{
		Ctx:   ctx,
		User:  user,
		W:     w,
		Nodes: nodes,
		Links: links,
	}
	mock.lockCreate.Lock()
	mock.calls.Create = append(mock.calls.Create, callInfo)
	mock.lockCreate.Unlock()
	return mock.CreateFunc(ctx, user, w, nodes, links)
}

// CreateCalls gets all the calls that were made to Create.
// Check the length with:
//
//	len(mockedWorkflow.CreateCalls())
func (mock *MoqWorkflow) CreateCalls() []struct {
	Ctx   context.Context
	User  *ent.User
	W     *ent.Workflow
	Nodes []*ent.WorkflowNode
	Links []*ent.WorkflowLink
} {
	var calls []struct {
		Ctx   context.Context
		User  *ent.User
		W     *ent.Workflow
		Nodes []*ent.WorkflowNode
		Links []*ent.WorkflowLink
	}
	mock.lockCreate.RLock()
	calls = mock.calls.Create
	mock.lockCreate.RUnlock()
	return calls
}

// CreateWithTx calls CreateWithTxFunc.
func (mock *MoqWorkflow) CreateWithTx(ctx context.Context, userId uuid.UUID, w *ent.Workflow, createCall func(tx *ent.Tx, workflow *ent.Workflow) error) (*ent.Workflow, error) {
	if mock.CreateWithTxFunc == nil {
		panic("MoqWorkflow.CreateWithTxFunc: method is nil but Workflow.CreateWithTx was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		UserId     uuid.UUID
		W          *ent.Workflow
		CreateCall func(tx *ent.Tx, workflow *ent.Workflow) error
	}{
		Ctx:        ctx,
		UserId:     userId,
		W:          w,
		CreateCall: createCall,
	}
	mock.lockCreateWithTx.Lock()
	mock.calls.CreateWithTx = append(mock.calls.CreateWithTx, callInfo)
	mock.lockCreateWithTx.Unlock()
	return mock.CreateWithTxFunc(ctx, userId, w, createCall)
}

// CreateWithTxCalls gets all the calls that were made to CreateWithTx.
// Check the length with:
//
//	len(mockedWorkflow.CreateWithTxCalls())
func (mock *MoqWorkflow) CreateWithTxCalls() []struct {
	Ctx        context.Context
	UserId     uuid.UUID
	W          *ent.Workflow
	CreateCall func(tx *ent.Tx, workflow *ent.Workflow) error
} {
	var calls []struct {
		Ctx        context.Context
		UserId     uuid.UUID
		W          *ent.Workflow
		CreateCall func(tx *ent.Tx, workflow *ent.Workflow) error
	}
	mock.lockCreateWithTx.RLock()
	calls = mock.calls.CreateWithTx
	mock.lockCreateWithTx.RUnlock()
	return calls
}

// DeleteById calls DeleteByIdFunc.
func (mock *MoqWorkflow) DeleteById(ctx context.Context, id uuid.UUID, deleteCall func(tx *ent.Tx) error) error {
	if mock.DeleteByIdFunc == nil {
		panic("MoqWorkflow.DeleteByIdFunc: method is nil but Workflow.DeleteById was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		ID         uuid.UUID
		DeleteCall func(tx *ent.Tx) error
	}{
		Ctx:        ctx,
		ID:         id,
		DeleteCall: deleteCall,
	}
	mock.lockDeleteById.Lock()
	mock.calls.DeleteById = append(mock.calls.DeleteById, callInfo)
	mock.lockDeleteById.Unlock()
	return mock.DeleteByIdFunc(ctx, id, deleteCall)
}

// DeleteByIdCalls gets all the calls that were made to DeleteById.
// Check the length with:
//
//	len(mockedWorkflow.DeleteByIdCalls())
func (mock *MoqWorkflow) DeleteByIdCalls() []struct {
	Ctx        context.Context
	ID         uuid.UUID
	DeleteCall func(tx *ent.Tx) error
} {
	var calls []struct {
		Ctx        context.Context
		ID         uuid.UUID
		DeleteCall func(tx *ent.Tx) error
	}
	mock.lockDeleteById.RLock()
	calls = mock.calls.DeleteById
	mock.lockDeleteById.RUnlock()
	return calls
}

// GetById calls GetByIdFunc.
func (mock *MoqWorkflow) GetById(ctx context.Context, id uuid.UUID) (*ent.Workflow, error) {
	if mock.GetByIdFunc == nil {
		panic("MoqWorkflow.GetByIdFunc: method is nil but Workflow.GetById was just called")
	}
	callInfo := struct {
		Ctx context.Context
		ID  uuid.UUID
	}{
		Ctx: ctx,
		ID:  id,
	}
	mock.lockGetById.Lock()
	mock.calls.GetById = append(mock.calls.GetById, callInfo)
	mock.lockGetById.Unlock()
	return mock.GetByIdFunc(ctx, id)
}

// GetByIdCalls gets all the calls that were made to GetById.
// Check the length with:
//
//	len(mockedWorkflow.GetByIdCalls())
func (mock *MoqWorkflow) GetByIdCalls() []struct {
	Ctx context.Context
	ID  uuid.UUID
} {
	var calls []struct {
		Ctx context.Context
		ID  uuid.UUID
	}
	mock.lockGetById.RLock()
	calls = mock.calls.GetById
	mock.lockGetById.RUnlock()
	return calls
}

// List calls ListFunc.
func (mock *MoqWorkflow) List(ctx context.Context) ([]*ent.Workflow, error) {
	if mock.ListFunc == nil {
		panic("MoqWorkflow.ListFunc: method is nil but Workflow.List was just called")
	}
	callInfo := struct {
		Ctx context.Context
	}{
		Ctx: ctx,
	}
	mock.lockList.Lock()
	mock.calls.List = append(mock.calls.List, callInfo)
	mock.lockList.Unlock()
	return mock.ListFunc(ctx)
}

// ListCalls gets all the calls that were made to List.
// Check the length with:
//
//	len(mockedWorkflow.ListCalls())
func (mock *MoqWorkflow) ListCalls() []struct {
	Ctx context.Context
} {
	var calls []struct {
		Ctx context.Context
	}
	mock.lockList.RLock()
	calls = mock.calls.List
	mock.lockList.RUnlock()
	return calls
}

// UpdateWithTx calls UpdateWithTxFunc.
func (mock *MoqWorkflow) UpdateWithTx(ctx context.Context, w *ent.Workflow, updateCall func(tx *ent.Tx) error) (*ent.Workflow, error) {
	if mock.UpdateWithTxFunc == nil {
		panic("MoqWorkflow.UpdateWithTxFunc: method is nil but Workflow.UpdateWithTx was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		W          *ent.Workflow
		UpdateCall func(tx *ent.Tx) error
	}{
		Ctx:        ctx,
		W:          w,
		UpdateCall: updateCall,
	}
	mock.lockUpdateWithTx.Lock()
	mock.calls.UpdateWithTx = append(mock.calls.UpdateWithTx, callInfo)
	mock.lockUpdateWithTx.Unlock()
	return mock.UpdateWithTxFunc(ctx, w, updateCall)
}

// UpdateWithTxCalls gets all the calls that were made to UpdateWithTx.
// Check the length with:
//
//	len(mockedWorkflow.UpdateWithTxCalls())
func (mock *MoqWorkflow) UpdateWithTxCalls() []struct {
	Ctx        context.Context
	W          *ent.Workflow
	UpdateCall func(tx *ent.Tx) error
} {
	var calls []struct {
		Ctx        context.Context
		W          *ent.Workflow
		UpdateCall func(tx *ent.Tx) error
	}
	mock.lockUpdateWithTx.RLock()
	calls = mock.calls.UpdateWithTx
	mock.lockUpdateWithTx.RUnlock()
	return calls
}

// Ensure that MoqWorkflowLink does implement WorkflowLink.
// If this is not the case, regenerate this file with mockery.
var _ WorkflowLink = &MoqWorkflowLink{}

// MoqWorkflowLink is a mock implementation of WorkflowLink.
//
//	func TestSomethingThatUsesWorkflowLink(t *testing.T) {
//
//		// make and configure a mocked WorkflowLink
//		mockedWorkflowLink := &MoqWorkflowLink{
//			CreateWithTxFunc: func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, link *ent.WorkflowLink) (*ent.WorkflowLink, error) {
//				panic("mock out the CreateWithTx method")
//			},
//			DeleteByWorkflowIdAndIdsNotInWithTxFunc: func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, ids []uuid.UUID) error {
//				panic("mock out the DeleteByWorkflowIdAndIdsNotInWithTx method")
//			},
//			DeleteByWorkflowIdWithTxFunc: func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID) (int, error) {
//				panic("mock out the DeleteByWorkflowIdWithTx method")
//			},
//			GetByIdFunc: func(ctx context.Context, id uuid.UUID) (*ent.WorkflowLink, error) {
//				panic("mock out the GetById method")
//			},
//			UpdateWithTxFunc: func(ctx context.Context, tx *ent.Tx, link *ent.WorkflowLink) (*ent.WorkflowLink, error) {
//				panic("mock out the UpdateWithTx method")
//			},
//		}
//
//		// use mockedWorkflowLink in code that requires WorkflowLink
//		// and then make assertions.
//
//	}
type MoqWorkflowLink struct {
	// CreateWithTxFunc mocks the CreateWithTx method.
	CreateWithTxFunc func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, link *ent.WorkflowLink) (*ent.WorkflowLink, error)

	// DeleteByWorkflowIdAndIdsNotInWithTxFunc mocks the DeleteByWorkflowIdAndIdsNotInWithTx method.
	DeleteByWorkflowIdAndIdsNotInWithTxFunc func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, ids []uuid.UUID) error

	// DeleteByWorkflowIdWithTxFunc mocks the DeleteByWorkflowIdWithTx method.
	DeleteByWorkflowIdWithTxFunc func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID) (int, error)

	// GetByIdFunc mocks the GetById method.
	GetByIdFunc func(ctx context.Context, id uuid.UUID) (*ent.WorkflowLink, error)

	// UpdateWithTxFunc mocks the UpdateWithTx method.
	UpdateWithTxFunc func(ctx context.Context, tx *ent.Tx, link *ent.WorkflowLink) (*ent.WorkflowLink, error)

	// calls tracks calls to the methods.
	calls struct {
		// CreateWithTx holds details about calls to the CreateWithTx method.
		CreateWithTx []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// WorkflowId is the workflowId argument value.
			WorkflowId uuid.UUID
			// Link is the link argument value.
			Link *ent.WorkflowLink
		}
		// DeleteByWorkflowIdAndIdsNotInWithTx holds details about calls to the DeleteByWorkflowIdAndIdsNotInWithTx method.
		DeleteByWorkflowIdAndIdsNotInWithTx []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// WorkflowId is the workflowId argument value.
			WorkflowId uuid.UUID
			// Ids is the ids argument value.
			Ids []uuid.UUID
		}
		// DeleteByWorkflowIdWithTx holds details about calls to the DeleteByWorkflowIdWithTx method.
		DeleteByWorkflowIdWithTx []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// WorkflowId is the workflowId argument value.
			WorkflowId uuid.UUID
		}
		// GetById holds details about calls to the GetById method.
		GetById []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// ID is the id argument value.
			ID uuid.UUID
		}
		// UpdateWithTx holds details about calls to the UpdateWithTx method.
		UpdateWithTx []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// Link is the link argument value.
			Link *ent.WorkflowLink
		}
	}
	lockCreateWithTx                        sync.RWMutex
	lockDeleteByWorkflowIdAndIdsNotInWithTx sync.RWMutex
	lockDeleteByWorkflowIdWithTx            sync.RWMutex
	lockGetById                             sync.RWMutex
	lockUpdateWithTx                        sync.RWMutex
}

// CreateWithTx calls CreateWithTxFunc.
func (mock *MoqWorkflowLink) CreateWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, link *ent.WorkflowLink) (*ent.WorkflowLink, error) {
	if mock.CreateWithTxFunc == nil {
		panic("MoqWorkflowLink.CreateWithTxFunc: method is nil but WorkflowLink.CreateWithTx was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
		Link       *ent.WorkflowLink
	}{
		Ctx:        ctx,
		Tx:         tx,
		WorkflowId: workflowId,
		Link:       link,
	}
	mock.lockCreateWithTx.Lock()
	mock.calls.CreateWithTx = append(mock.calls.CreateWithTx, callInfo)
	mock.lockCreateWithTx.Unlock()
	return mock.CreateWithTxFunc(ctx, tx, workflowId, link)
}

// CreateWithTxCalls gets all the calls that were made to CreateWithTx.
// Check the length with:
//
//	len(mockedWorkflowLink.CreateWithTxCalls())
func (mock *MoqWorkflowLink) CreateWithTxCalls() []struct {
	Ctx        context.Context
	Tx         *ent.Tx
	WorkflowId uuid.UUID
	Link       *ent.WorkflowLink
} {
	var calls []struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
		Link       *ent.WorkflowLink
	}
	mock.lockCreateWithTx.RLock()
	calls = mock.calls.CreateWithTx
	mock.lockCreateWithTx.RUnlock()
	return calls
}

// DeleteByWorkflowIdAndIdsNotInWithTx calls DeleteByWorkflowIdAndIdsNotInWithTxFunc.
func (mock *MoqWorkflowLink) DeleteByWorkflowIdAndIdsNotInWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, ids []uuid.UUID) error {
	if mock.DeleteByWorkflowIdAndIdsNotInWithTxFunc == nil {
		panic("MoqWorkflowLink.DeleteByWorkflowIdAndIdsNotInWithTxFunc: method is nil but WorkflowLink.DeleteByWorkflowIdAndIdsNotInWithTx was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
		Ids        []uuid.UUID
	}{
		Ctx:        ctx,
		Tx:         tx,
		WorkflowId: workflowId,
		Ids:        ids,
	}
	mock.lockDeleteByWorkflowIdAndIdsNotInWithTx.Lock()
	mock.calls.DeleteByWorkflowIdAndIdsNotInWithTx = append(mock.calls.DeleteByWorkflowIdAndIdsNotInWithTx, callInfo)
	mock.lockDeleteByWorkflowIdAndIdsNotInWithTx.Unlock()
	return mock.DeleteByWorkflowIdAndIdsNotInWithTxFunc(ctx, tx, workflowId, ids)
}

// DeleteByWorkflowIdAndIdsNotInWithTxCalls gets all the calls that were made to DeleteByWorkflowIdAndIdsNotInWithTx.
// Check the length with:
//
//	len(mockedWorkflowLink.DeleteByWorkflowIdAndIdsNotInWithTxCalls())
func (mock *MoqWorkflowLink) DeleteByWorkflowIdAndIdsNotInWithTxCalls() []struct {
	Ctx        context.Context
	Tx         *ent.Tx
	WorkflowId uuid.UUID
	Ids        []uuid.UUID
} {
	var calls []struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
		Ids        []uuid.UUID
	}
	mock.lockDeleteByWorkflowIdAndIdsNotInWithTx.RLock()
	calls = mock.calls.DeleteByWorkflowIdAndIdsNotInWithTx
	mock.lockDeleteByWorkflowIdAndIdsNotInWithTx.RUnlock()
	return calls
}

// DeleteByWorkflowIdWithTx calls DeleteByWorkflowIdWithTxFunc.
func (mock *MoqWorkflowLink) DeleteByWorkflowIdWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID) (int, error) {
	if mock.DeleteByWorkflowIdWithTxFunc == nil {
		panic("MoqWorkflowLink.DeleteByWorkflowIdWithTxFunc: method is nil but WorkflowLink.DeleteByWorkflowIdWithTx was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
	}{
		Ctx:        ctx,
		Tx:         tx,
		WorkflowId: workflowId,
	}
	mock.lockDeleteByWorkflowIdWithTx.Lock()
	mock.calls.DeleteByWorkflowIdWithTx = append(mock.calls.DeleteByWorkflowIdWithTx, callInfo)
	mock.lockDeleteByWorkflowIdWithTx.Unlock()
	return mock.DeleteByWorkflowIdWithTxFunc(ctx, tx, workflowId)
}

// DeleteByWorkflowIdWithTxCalls gets all the calls that were made to DeleteByWorkflowIdWithTx.
// Check the length with:
//
//	len(mockedWorkflowLink.DeleteByWorkflowIdWithTxCalls())
func (mock *MoqWorkflowLink) DeleteByWorkflowIdWithTxCalls() []struct {
	Ctx        context.Context
	Tx         *ent.Tx
	WorkflowId uuid.UUID
} {
	var calls []struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
	}
	mock.lockDeleteByWorkflowIdWithTx.RLock()
	calls = mock.calls.DeleteByWorkflowIdWithTx
	mock.lockDeleteByWorkflowIdWithTx.RUnlock()
	return calls
}

// GetById calls GetByIdFunc.
func (mock *MoqWorkflowLink) GetById(ctx context.Context, id uuid.UUID) (*ent.WorkflowLink, error) {
	if mock.GetByIdFunc == nil {
		panic("MoqWorkflowLink.GetByIdFunc: method is nil but WorkflowLink.GetById was just called")
	}
	callInfo := struct {
		Ctx context.Context
		ID  uuid.UUID
	}{
		Ctx: ctx,
		ID:  id,
	}
	mock.lockGetById.Lock()
	mock.calls.GetById = append(mock.calls.GetById, callInfo)
	mock.lockGetById.Unlock()
	return mock.GetByIdFunc(ctx, id)
}

// GetByIdCalls gets all the calls that were made to GetById.
// Check the length with:
//
//	len(mockedWorkflowLink.GetByIdCalls())
func (mock *MoqWorkflowLink) GetByIdCalls() []struct {
	Ctx context.Context
	ID  uuid.UUID
} {
	var calls []struct {
		Ctx context.Context
		ID  uuid.UUID
	}
	mock.lockGetById.RLock()
	calls = mock.calls.GetById
	mock.lockGetById.RUnlock()
	return calls
}

// UpdateWithTx calls UpdateWithTxFunc.
func (mock *MoqWorkflowLink) UpdateWithTx(ctx context.Context, tx *ent.Tx, link *ent.WorkflowLink) (*ent.WorkflowLink, error) {
	if mock.UpdateWithTxFunc == nil {
		panic("MoqWorkflowLink.UpdateWithTxFunc: method is nil but WorkflowLink.UpdateWithTx was just called")
	}
	callInfo := struct {
		Ctx  context.Context
		Tx   *ent.Tx
		Link *ent.WorkflowLink
	}{
		Ctx:  ctx,
		Tx:   tx,
		Link: link,
	}
	mock.lockUpdateWithTx.Lock()
	mock.calls.UpdateWithTx = append(mock.calls.UpdateWithTx, callInfo)
	mock.lockUpdateWithTx.Unlock()
	return mock.UpdateWithTxFunc(ctx, tx, link)
}

// UpdateWithTxCalls gets all the calls that were made to UpdateWithTx.
// Check the length with:
//
//	len(mockedWorkflowLink.UpdateWithTxCalls())
func (mock *MoqWorkflowLink) UpdateWithTxCalls() []struct {
	Ctx  context.Context
	Tx   *ent.Tx
	Link *ent.WorkflowLink
} {
	var calls []struct {
		Ctx  context.Context
		Tx   *ent.Tx
		Link *ent.WorkflowLink
	}
	mock.lockUpdateWithTx.RLock()
	calls = mock.calls.UpdateWithTx
	mock.lockUpdateWithTx.RUnlock()
	return calls
}

// Ensure that MoqWorkflowNode does implement WorkflowNode.
// If this is not the case, regenerate this file with mockery.
var _ WorkflowNode = &MoqWorkflowNode{}

// MoqWorkflowNode is a mock implementation of WorkflowNode.
//
//	func TestSomethingThatUsesWorkflowNode(t *testing.T) {
//
//		// make and configure a mocked WorkflowNode
//		mockedWorkflowNode := &MoqWorkflowNode{
//			CreateWithTxFunc: func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, node *ent.WorkflowNode) (*ent.WorkflowNode, error) {
//				panic("mock out the CreateWithTx method")
//			},
//			DeleteByWorkflowIdAndIdsNotInWithTxFunc: func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, ids []uuid.UUID) error {
//				panic("mock out the DeleteByWorkflowIdAndIdsNotInWithTx method")
//			},
//			DeleteByWorkflowIdWithTxFunc: func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID) (int, error) {
//				panic("mock out the DeleteByWorkflowIdWithTx method")
//			},
//			GetByIdFunc: func(ctx context.Context, workflowId uuid.UUID, id uuid.UUID) (*ent.WorkflowNode, error) {
//				panic("mock out the GetById method")
//			},
//			UpdateWithTxFunc: func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, node *ent.WorkflowNode) (*ent.WorkflowNode, error) {
//				panic("mock out the UpdateWithTx method")
//			},
//		}
//
//		// use mockedWorkflowNode in code that requires WorkflowNode
//		// and then make assertions.
//
//	}
type MoqWorkflowNode struct {
	// CreateWithTxFunc mocks the CreateWithTx method.
	CreateWithTxFunc func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, node *ent.WorkflowNode) (*ent.WorkflowNode, error)

	// DeleteByWorkflowIdAndIdsNotInWithTxFunc mocks the DeleteByWorkflowIdAndIdsNotInWithTx method.
	DeleteByWorkflowIdAndIdsNotInWithTxFunc func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, ids []uuid.UUID) error

	// DeleteByWorkflowIdWithTxFunc mocks the DeleteByWorkflowIdWithTx method.
	DeleteByWorkflowIdWithTxFunc func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID) (int, error)

	// GetByIdFunc mocks the GetById method.
	GetByIdFunc func(ctx context.Context, workflowId uuid.UUID, id uuid.UUID) (*ent.WorkflowNode, error)

	// UpdateWithTxFunc mocks the UpdateWithTx method.
	UpdateWithTxFunc func(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, node *ent.WorkflowNode) (*ent.WorkflowNode, error)

	// calls tracks calls to the methods.
	calls struct {
		// CreateWithTx holds details about calls to the CreateWithTx method.
		CreateWithTx []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// WorkflowId is the workflowId argument value.
			WorkflowId uuid.UUID
			// Node is the node argument value.
			Node *ent.WorkflowNode
		}
		// DeleteByWorkflowIdAndIdsNotInWithTx holds details about calls to the DeleteByWorkflowIdAndIdsNotInWithTx method.
		DeleteByWorkflowIdAndIdsNotInWithTx []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// WorkflowId is the workflowId argument value.
			WorkflowId uuid.UUID
			// Ids is the ids argument value.
			Ids []uuid.UUID
		}
		// DeleteByWorkflowIdWithTx holds details about calls to the DeleteByWorkflowIdWithTx method.
		DeleteByWorkflowIdWithTx []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// WorkflowId is the workflowId argument value.
			WorkflowId uuid.UUID
		}
		// GetById holds details about calls to the GetById method.
		GetById []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// WorkflowId is the workflowId argument value.
			WorkflowId uuid.UUID
			// ID is the id argument value.
			ID uuid.UUID
		}
		// UpdateWithTx holds details about calls to the UpdateWithTx method.
		UpdateWithTx []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// WorkflowId is the workflowId argument value.
			WorkflowId uuid.UUID
			// Node is the node argument value.
			Node *ent.WorkflowNode
		}
	}
	lockCreateWithTx                        sync.RWMutex
	lockDeleteByWorkflowIdAndIdsNotInWithTx sync.RWMutex
	lockDeleteByWorkflowIdWithTx            sync.RWMutex
	lockGetById                             sync.RWMutex
	lockUpdateWithTx                        sync.RWMutex
}

// CreateWithTx calls CreateWithTxFunc.
func (mock *MoqWorkflowNode) CreateWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, node *ent.WorkflowNode) (*ent.WorkflowNode, error) {
	if mock.CreateWithTxFunc == nil {
		panic("MoqWorkflowNode.CreateWithTxFunc: method is nil but WorkflowNode.CreateWithTx was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
		Node       *ent.WorkflowNode
	}{
		Ctx:        ctx,
		Tx:         tx,
		WorkflowId: workflowId,
		Node:       node,
	}
	mock.lockCreateWithTx.Lock()
	mock.calls.CreateWithTx = append(mock.calls.CreateWithTx, callInfo)
	mock.lockCreateWithTx.Unlock()
	return mock.CreateWithTxFunc(ctx, tx, workflowId, node)
}

// CreateWithTxCalls gets all the calls that were made to CreateWithTx.
// Check the length with:
//
//	len(mockedWorkflowNode.CreateWithTxCalls())
func (mock *MoqWorkflowNode) CreateWithTxCalls() []struct {
	Ctx        context.Context
	Tx         *ent.Tx
	WorkflowId uuid.UUID
	Node       *ent.WorkflowNode
} {
	var calls []struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
		Node       *ent.WorkflowNode
	}
	mock.lockCreateWithTx.RLock()
	calls = mock.calls.CreateWithTx
	mock.lockCreateWithTx.RUnlock()
	return calls
}

// DeleteByWorkflowIdAndIdsNotInWithTx calls DeleteByWorkflowIdAndIdsNotInWithTxFunc.
func (mock *MoqWorkflowNode) DeleteByWorkflowIdAndIdsNotInWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, ids []uuid.UUID) error {
	if mock.DeleteByWorkflowIdAndIdsNotInWithTxFunc == nil {
		panic("MoqWorkflowNode.DeleteByWorkflowIdAndIdsNotInWithTxFunc: method is nil but WorkflowNode.DeleteByWorkflowIdAndIdsNotInWithTx was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
		Ids        []uuid.UUID
	}{
		Ctx:        ctx,
		Tx:         tx,
		WorkflowId: workflowId,
		Ids:        ids,
	}
	mock.lockDeleteByWorkflowIdAndIdsNotInWithTx.Lock()
	mock.calls.DeleteByWorkflowIdAndIdsNotInWithTx = append(mock.calls.DeleteByWorkflowIdAndIdsNotInWithTx, callInfo)
	mock.lockDeleteByWorkflowIdAndIdsNotInWithTx.Unlock()
	return mock.DeleteByWorkflowIdAndIdsNotInWithTxFunc(ctx, tx, workflowId, ids)
}

// DeleteByWorkflowIdAndIdsNotInWithTxCalls gets all the calls that were made to DeleteByWorkflowIdAndIdsNotInWithTx.
// Check the length with:
//
//	len(mockedWorkflowNode.DeleteByWorkflowIdAndIdsNotInWithTxCalls())
func (mock *MoqWorkflowNode) DeleteByWorkflowIdAndIdsNotInWithTxCalls() []struct {
	Ctx        context.Context
	Tx         *ent.Tx
	WorkflowId uuid.UUID
	Ids        []uuid.UUID
} {
	var calls []struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
		Ids        []uuid.UUID
	}
	mock.lockDeleteByWorkflowIdAndIdsNotInWithTx.RLock()
	calls = mock.calls.DeleteByWorkflowIdAndIdsNotInWithTx
	mock.lockDeleteByWorkflowIdAndIdsNotInWithTx.RUnlock()
	return calls
}

// DeleteByWorkflowIdWithTx calls DeleteByWorkflowIdWithTxFunc.
func (mock *MoqWorkflowNode) DeleteByWorkflowIdWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID) (int, error) {
	if mock.DeleteByWorkflowIdWithTxFunc == nil {
		panic("MoqWorkflowNode.DeleteByWorkflowIdWithTxFunc: method is nil but WorkflowNode.DeleteByWorkflowIdWithTx was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
	}{
		Ctx:        ctx,
		Tx:         tx,
		WorkflowId: workflowId,
	}
	mock.lockDeleteByWorkflowIdWithTx.Lock()
	mock.calls.DeleteByWorkflowIdWithTx = append(mock.calls.DeleteByWorkflowIdWithTx, callInfo)
	mock.lockDeleteByWorkflowIdWithTx.Unlock()
	return mock.DeleteByWorkflowIdWithTxFunc(ctx, tx, workflowId)
}

// DeleteByWorkflowIdWithTxCalls gets all the calls that were made to DeleteByWorkflowIdWithTx.
// Check the length with:
//
//	len(mockedWorkflowNode.DeleteByWorkflowIdWithTxCalls())
func (mock *MoqWorkflowNode) DeleteByWorkflowIdWithTxCalls() []struct {
	Ctx        context.Context
	Tx         *ent.Tx
	WorkflowId uuid.UUID
} {
	var calls []struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
	}
	mock.lockDeleteByWorkflowIdWithTx.RLock()
	calls = mock.calls.DeleteByWorkflowIdWithTx
	mock.lockDeleteByWorkflowIdWithTx.RUnlock()
	return calls
}

// GetById calls GetByIdFunc.
func (mock *MoqWorkflowNode) GetById(ctx context.Context, workflowId uuid.UUID, id uuid.UUID) (*ent.WorkflowNode, error) {
	if mock.GetByIdFunc == nil {
		panic("MoqWorkflowNode.GetByIdFunc: method is nil but WorkflowNode.GetById was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		WorkflowId uuid.UUID
		ID         uuid.UUID
	}{
		Ctx:        ctx,
		WorkflowId: workflowId,
		ID:         id,
	}
	mock.lockGetById.Lock()
	mock.calls.GetById = append(mock.calls.GetById, callInfo)
	mock.lockGetById.Unlock()
	return mock.GetByIdFunc(ctx, workflowId, id)
}

// GetByIdCalls gets all the calls that were made to GetById.
// Check the length with:
//
//	len(mockedWorkflowNode.GetByIdCalls())
func (mock *MoqWorkflowNode) GetByIdCalls() []struct {
	Ctx        context.Context
	WorkflowId uuid.UUID
	ID         uuid.UUID
} {
	var calls []struct {
		Ctx        context.Context
		WorkflowId uuid.UUID
		ID         uuid.UUID
	}
	mock.lockGetById.RLock()
	calls = mock.calls.GetById
	mock.lockGetById.RUnlock()
	return calls
}

// UpdateWithTx calls UpdateWithTxFunc.
func (mock *MoqWorkflowNode) UpdateWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, node *ent.WorkflowNode) (*ent.WorkflowNode, error) {
	if mock.UpdateWithTxFunc == nil {
		panic("MoqWorkflowNode.UpdateWithTxFunc: method is nil but WorkflowNode.UpdateWithTx was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
		Node       *ent.WorkflowNode
	}{
		Ctx:        ctx,
		Tx:         tx,
		WorkflowId: workflowId,
		Node:       node,
	}
	mock.lockUpdateWithTx.Lock()
	mock.calls.UpdateWithTx = append(mock.calls.UpdateWithTx, callInfo)
	mock.lockUpdateWithTx.Unlock()
	return mock.UpdateWithTxFunc(ctx, tx, workflowId, node)
}

// UpdateWithTxCalls gets all the calls that were made to UpdateWithTx.
// Check the length with:
//
//	len(mockedWorkflowNode.UpdateWithTxCalls())
func (mock *MoqWorkflowNode) UpdateWithTxCalls() []struct {
	Ctx        context.Context
	Tx         *ent.Tx
	WorkflowId uuid.UUID
	Node       *ent.WorkflowNode
} {
	var calls []struct {
		Ctx        context.Context
		Tx         *ent.Tx
		WorkflowId uuid.UUID
		Node       *ent.WorkflowNode
	}
	mock.lockUpdateWithTx.RLock()
	calls = mock.calls.UpdateWithTx
	mock.lockUpdateWithTx.RUnlock()
	return calls
}
