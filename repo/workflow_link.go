package repo

import (
	"context"
	"github.com/google/uuid"
	"resflow/ent"
)

type WorkflowLink interface {
	CreateWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, link *ent.WorkflowLink) (*ent.WorkflowLink, error)
	UpdateWithTx(ctx context.Context, tx *ent.Tx, link *ent.WorkflowLink) (*ent.WorkflowLink, error)
	DeleteByWorkflowIdWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID) (int, error)
	DeleteByWorkflowIdAndIdsNotInWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, ids []uuid.UUID) error
	GetById(ctx context.Context, id uuid.UUID) (*ent.WorkflowLink, error)
}
