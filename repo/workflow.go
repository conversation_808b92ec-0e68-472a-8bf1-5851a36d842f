package repo

import (
	"context"
	"github.com/google/uuid"
	"resflow/ent"
)

type Workflow interface {
	GetById(ctx context.Context, id uuid.UUID) (*ent.Workflow, error)
	CreateWithTx(ctx context.Context, userId uuid.UUID, w *ent.Workflow, createCall func(tx *ent.Tx, workflow *ent.Workflow) error) (*ent.Workflow, error)
	Create(ctx context.Context, user *ent.User, w *ent.Workflow, nodes []*ent.WorkflowNode, links []*ent.WorkflowLink) (*ent.Workflow, error)
	UpdateWithTx(ctx context.Context, w *ent.Workflow, updateCall func(tx *ent.Tx) error) (*ent.Workflow, error)
	DeleteById(ctx context.Context, id uuid.UUID, deleteCall func(tx *ent.Tx) error) error
	List(ctx context.Context) ([]*ent.Workflow, error)
}
