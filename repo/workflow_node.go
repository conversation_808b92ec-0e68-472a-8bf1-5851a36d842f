package repo

import (
	"context"
	"github.com/google/uuid"
	"resflow/ent"
)

type WorkflowNode interface {
	CreateWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, node *ent.WorkflowNode) (*ent.WorkflowNode, error)
	UpdateWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, node *ent.WorkflowNode) (*ent.WorkflowNode, error)
	DeleteByWorkflowIdWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID) (int, error)
	DeleteByWorkflowIdAndIdsNotInWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, ids []uuid.UUID) error
	GetById(ctx context.Context, workflowId uuid.UUID, id uuid.UUID) (*ent.WorkflowNode, error)
}
