package node

import (
	"encoding/json"
	"errors"
	"io/fs"
	"os"
	"path/filepath"
	"resflow/utils"
)

var NodesDirNotExistsError = errors.New("nodes directory does not exist")

var NodeManager Manager

func init() {
	utils.Logger.Info("init node manager")
	NodeManager = NewNodeManager(os.DirFS("."))
}

type Manager interface {
	Discover(dir string) (map[Identifier]INode, error)
	GetNodeByDir(dir string) INode
}

type nodeManager struct {
	Nodes map[Identifier]INode
	fsys  fs.FS
}

func NewNodeManager(fsys fs.FS) Manager {
	return &nodeManager{Nodes: nil, fsys: fsys}
}

// Discover 发现节点
func (n nodeManager) Discover(dir string) (map[Identifier]INode, error) {
	nodes := make(map[Identifier]INode)
	err := fs.WalkDir(n.fsys, dir, func(path string, d fs.DirEntry, err error) error {
		utils.Logger.Debug("访问节点目录", path)
		if err != nil {
			utils.Logger.Errorf("访问节点目录%s失败：%s", path, err)
			return NodesDirNotExistsError
		}
		if d.IsDir() {
			node := n.GetNodeByDir(path)
			if node != nil {
				nodes[node.Identifier()] = node
			}
		}
		return nil
	})
	if err != nil {
		utils.Logger.Error("节点加载失败：", err)
		return nil, err
	}
	return nodes, nil
}

// GetNodeByDir 根据目录获取某个节点
func (n nodeManager) GetNodeByDir(dir string) INode {
	// 判断目录是否存在node.json
	utils.Logger.Debug("检查节点目录：", dir)
	nodeJsonPath := filepath.Join(dir, "node.json")
	utils.Logger.Debug("node.json: ", nodeJsonPath)
	nodeJsonContent, err := fs.ReadFile(n.fsys, nodeJsonPath)
	if err != nil {
		utils.Logger.Error("读取节点配置文件失败：", nodeJsonPath, err)
		return nil
	}
	var nodeConfig *Node
	if err := json.Unmarshal(nodeJsonContent, &nodeConfig); err != nil {
		utils.Logger.Error("解析节点配置文件失败：", nodeJsonPath, err)
		return nil
	}
	if nodeConfig.Name == "" || nodeConfig.Version == "" || nodeConfig.Author == "" || nodeConfig.Entrance == "" || nodeConfig.Type == "" {
		utils.Logger.Error("name/version/author/entrance/type不存在")
		return nil
	}
	nodeConfig.Dir = dir
	return nodeConfig
}
