package node

import (
	"github.com/stretchr/testify/assert"
	"io/fs"
	"testing"
	"testing/fstest"
)

// TestDiscover 测试发现节点
func TestDiscover(t *testing.T) {
	cases := []struct {
		caseDoc string
		fs      fs.FS
		giveDir string
		want    map[Identifier]INode
		wantErr error
	}{
		{
			caseDoc: "测试加载多个节点",
			fs: fstest.MapFS{
				"test_nodes": &fstest.MapFile{
					Mode: fs.ModeDir | 0755,
				},
				"test_nodes/node1": &fstest.MapFile{
					Mode: fs.ModeDir | 0755,
				},
				"test_nodes/node2": &fstest.MapFile{
					Mode: fs.ModeDir | 0755,
				},
				"test_nodes/node1/node.json": &fstest.MapFile{
					Data: []byte(`{"name":"node1","version":"1.0.0","author":"fmw","entrance":"node1","type":"executable"}`),
				},
				"test_nodes/node2/node.json": &fstest.MapFile{
					Data: []byte(`{"name":"node2","version":"1.0.0","author":"fmw","entrance":"node2","type":"executable"}`),
				},
			},
			giveDir: "test_nodes",
			want: map[Identifier]INode{
				"fmw/node1@1.0.0": NewNode("node1", "fmw", "", "", "node1", "test_nodes/node1", "1.0.0", "executable"),
				"fmw/node2@1.0.0": NewNode("node2", "fmw", "", "", "node2", "test_nodes/node2", "1.0.0", "executable"),
			},
			wantErr: nil,
		}, {
			caseDoc: "节点目录不存在",
			fs:      fstest.MapFS{},
			giveDir: "test_nodes",
			want:    nil,
			wantErr: NodesDirNotExistsError,
		},
	}

	for _, tc := range cases {
		t.Run(tc.caseDoc, func(t *testing.T) {
			manager := NewNodeManager(tc.fs)
			nodes, err := manager.Discover(tc.giveDir)
			assert.Equal(t, tc.want, nodes)
			assert.Equal(t, tc.wantErr, err)
		})
	}
}

func TestGetNodeByDir(t *testing.T) {
	cases := []struct {
		caseDoc string
		fs      fs.FS
		giveDir string
		want    INode
	}{
		{
			caseDoc: "获取test节点",
			fs: fstest.MapFS{
				"test_nodes/test/node.json": &fstest.MapFile{
					Data: []byte(`{ "name": "test", "version": "0.0.1", "author": "fmw", "description": "this is a test node", "email": "", "type": "executable", "entrance": "test" }`),
				},
			},
			giveDir: "test_nodes/test",
			want:    NewNode("test", "fmw", "this is a test node", "", "test", "test_nodes/test", "0.0.1", "executable"),
		}, {
			caseDoc: "node.json不存在",
			fs: fstest.MapFS{
				"test_nodes/test/node.txt": &fstest.MapFile{
					Data: []byte(`{ "name": "test", "version": "0.0.1", "author": "fmw", "description": "this is a test node", "email": "", "type": "executable", "entrance": "test" }`),
				},
			},
			giveDir: "test_nodes/test",
			want:    nil,
		}, {
			caseDoc: "node.json name不存在",
			fs: fstest.MapFS{
				"test_nodes/test/node.json": &fstest.MapFile{
					Data: []byte(`{ "version": "0.0.1", "author": "fmw", "description": "this is a test node", "email": "", "type": "executable", "entrance": "test" }`),
				},
			},
			giveDir: "test_nodes/test",
			want:    nil,
		}, {
			caseDoc: "node.json version不存在",
			fs: fstest.MapFS{
				"test_nodes/test/node.json": &fstest.MapFile{
					Data: []byte(`{ "name": "test", "author": "fmw", "description": "this is a test node", "email": "", "type": "executable", "entrance": "test" }`),
				},
			},
			giveDir: "test_nodes/test",
			want:    nil,
		}, {
			caseDoc: "node.json author不存在",
			fs: fstest.MapFS{
				"test_nodes/test/node.json": &fstest.MapFile{
					Data: []byte(`{ "name": "test", "version": "0.0.1", "description": "this is a test node", "email": "", "type": "executable", "entrance": "test" }`),
				},
			},
			giveDir: "test_nodes/test",
			want:    nil,
		}, {
			caseDoc: "node.json entrance不存在",
			fs: fstest.MapFS{
				"test_nodes/test/node.json": &fstest.MapFile{
					Data: []byte(`{ "name": "test", "version": "0.0.1", "author": "fmw", "description": "this is a test node", "email": "", "type": "executable" }`),
				},
			},
			giveDir: "test_nodes/test",
			want:    nil,
		}, {
			caseDoc: "node.json entrance不存在",
			fs: fstest.MapFS{
				"test_nodes/test/node.json": &fstest.MapFile{
					Data: []byte(`{ "name": "test", "version": "0.0.1", "author": "fmw", "description": "this is a test node", "email": "", "entrance": "test" }`),
				},
			},
			giveDir: "test_nodes/test",
			want:    nil,
		}, {
			caseDoc: "node.json格式错误",
			fs: fstest.MapFS{
				"test_nodes/test/node.json": &fstest.MapFile{
					Data: []byte(`{ name": "test", "version": "0.0.1", "author": "fmw", "description": "this is a test node", "email": "", "entrance": "test" }`),
				},
			},
			giveDir: "test_nodes/test",
			want:    nil,
		},
	}

	for _, tc := range cases {
		t.Run(tc.caseDoc, func(t *testing.T) {
			manager := NewNodeManager(tc.fs)
			n := manager.GetNodeByDir(tc.giveDir)
			assert.Equal(t, tc.want, n)
		})
	}
}
