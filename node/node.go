package node

import "fmt"

type Identifier string
type Version string

type Type string

type INode interface {
	Run(config interface{}) interface{}
	Identifier() Identifier
}

type Node struct {
	Name        string  `json:"name"`
	Version     Version `json:"version"`
	Description string  `json:"description"`
	Author      string  `json:"author"`
	Email       string  `json:"email"`
	Type        Type    `json:"type"`
	Entrance    string  `json:"entrance"`
	Dir         string  `json:"dir"`
}

const Executable Type = "executable"
const External Type = "external"

func NewNode(name, author, description, email, entrance, dir string, version Version, t Type) INode {
	return &Node{
		Name:        name,
		Version:     version,
		Description: description,
		Author:      author,
		Email:       email,
		Type:        t,
		Entrance:    entrance,
		Dir:         dir,
	}
}

func (n Node) Run(config interface{}) interface{} {
	return nil
}

func (n Node) Identifier() Identifier {
	return Identifier(fmt.Sprintf("%s/%s@%s", n.Author, n.Name, n.Version))
}
