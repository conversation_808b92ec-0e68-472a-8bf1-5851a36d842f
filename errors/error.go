package errors

var Unauthenticated = NewError(401, "未登录")
var ParamsError = NewError(400, "参数错误")

type Error struct {
	Code    int    `json:"code"`
	Message string `json:"msg"`
}

func (e Error) Error() string {
	return e.Message
}

func NewError(code int, msg string) *Error {
	return &Error{Code: code, Message: msg}
}

func ValidationError(msg string) *Error {
	return &Error{Code: 40019, Message: msg}
}
