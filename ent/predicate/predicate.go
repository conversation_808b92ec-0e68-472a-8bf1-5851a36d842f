// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// NodeDefinition is the predicate function for nodedefinition builders.
type NodeDefinition func(*sql.Selector)

// Plugin is the predicate function for plugin builders.
type Plugin func(*sql.Selector)

// User is the predicate function for user builders.
type User func(*sql.Selector)

// Workflow is the predicate function for workflow builders.
type Workflow func(*sql.Selector)

// WorkflowLink is the predicate function for workflowlink builders.
type WorkflowLink func(*sql.Selector)

// WorkflowNode is the predicate function for workflownode builders.
type WorkflowNode func(*sql.Selector)
