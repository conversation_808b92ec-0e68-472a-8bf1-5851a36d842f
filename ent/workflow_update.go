// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"resflow/ent/predicate"
	"resflow/ent/user"
	"resflow/ent/workflow"
	"resflow/ent/workflowlink"
	"resflow/ent/workflownode"
	"resflow/enums"
	v1 "resflow/proto/generated_go/v1"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowUpdate is the builder for updating Workflow entities.
type WorkflowUpdate struct {
	config
	hooks    []Hook
	mutation *WorkflowMutation
}

// Where appends a list predicates to the WorkflowUpdate builder.
func (wu *WorkflowUpdate) Where(ps ...predicate.Workflow) *WorkflowUpdate {
	wu.mutation.Where(ps...)
	return wu
}

// SetUserID sets the "user_id" field.
func (wu *WorkflowUpdate) SetUserID(u uuid.UUID) *WorkflowUpdate {
	wu.mutation.SetUserID(u)
	return wu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableUserID(u *uuid.UUID) *WorkflowUpdate {
	if u != nil {
		wu.SetUserID(*u)
	}
	return wu
}

// ClearUserID clears the value of the "user_id" field.
func (wu *WorkflowUpdate) ClearUserID() *WorkflowUpdate {
	wu.mutation.ClearUserID()
	return wu
}

// SetName sets the "name" field.
func (wu *WorkflowUpdate) SetName(s string) *WorkflowUpdate {
	wu.mutation.SetName(s)
	return wu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableName(s *string) *WorkflowUpdate {
	if s != nil {
		wu.SetName(*s)
	}
	return wu
}

// SetIcon sets the "icon" field.
func (wu *WorkflowUpdate) SetIcon(s string) *WorkflowUpdate {
	wu.mutation.SetIcon(s)
	return wu
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableIcon(s *string) *WorkflowUpdate {
	if s != nil {
		wu.SetIcon(*s)
	}
	return wu
}

// SetDescription sets the "description" field.
func (wu *WorkflowUpdate) SetDescription(s string) *WorkflowUpdate {
	wu.mutation.SetDescription(s)
	return wu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableDescription(s *string) *WorkflowUpdate {
	if s != nil {
		wu.SetDescription(*s)
	}
	return wu
}

// SetStatus sets the "status" field.
func (wu *WorkflowUpdate) SetStatus(e enums.Status) *WorkflowUpdate {
	wu.mutation.SetStatus(e)
	return wu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableStatus(e *enums.Status) *WorkflowUpdate {
	if e != nil {
		wu.SetStatus(*e)
	}
	return wu
}

// SetViewport sets the "viewport" field.
func (wu *WorkflowUpdate) SetViewport(vv *v1.WorkflowViewport) *WorkflowUpdate {
	wu.mutation.SetViewport(vv)
	return wu
}

// SetUpdatedAt sets the "updated_at" field.
func (wu *WorkflowUpdate) SetUpdatedAt(t time.Time) *WorkflowUpdate {
	wu.mutation.SetUpdatedAt(t)
	return wu
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableUpdatedAt(t *time.Time) *WorkflowUpdate {
	if t != nil {
		wu.SetUpdatedAt(*t)
	}
	return wu
}

// SetUser sets the "user" edge to the User entity.
func (wu *WorkflowUpdate) SetUser(u *User) *WorkflowUpdate {
	return wu.SetUserID(u.ID)
}

// AddNodeIDs adds the "nodes" edge to the WorkflowNode entity by IDs.
func (wu *WorkflowUpdate) AddNodeIDs(ids ...uuid.UUID) *WorkflowUpdate {
	wu.mutation.AddNodeIDs(ids...)
	return wu
}

// AddNodes adds the "nodes" edges to the WorkflowNode entity.
func (wu *WorkflowUpdate) AddNodes(w ...*WorkflowNode) *WorkflowUpdate {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wu.AddNodeIDs(ids...)
}

// AddLinkIDs adds the "links" edge to the WorkflowLink entity by IDs.
func (wu *WorkflowUpdate) AddLinkIDs(ids ...uuid.UUID) *WorkflowUpdate {
	wu.mutation.AddLinkIDs(ids...)
	return wu
}

// AddLinks adds the "links" edges to the WorkflowLink entity.
func (wu *WorkflowUpdate) AddLinks(w ...*WorkflowLink) *WorkflowUpdate {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wu.AddLinkIDs(ids...)
}

// Mutation returns the WorkflowMutation object of the builder.
func (wu *WorkflowUpdate) Mutation() *WorkflowMutation {
	return wu.mutation
}

// ClearUser clears the "user" edge to the User entity.
func (wu *WorkflowUpdate) ClearUser() *WorkflowUpdate {
	wu.mutation.ClearUser()
	return wu
}

// ClearNodes clears all "nodes" edges to the WorkflowNode entity.
func (wu *WorkflowUpdate) ClearNodes() *WorkflowUpdate {
	wu.mutation.ClearNodes()
	return wu
}

// RemoveNodeIDs removes the "nodes" edge to WorkflowNode entities by IDs.
func (wu *WorkflowUpdate) RemoveNodeIDs(ids ...uuid.UUID) *WorkflowUpdate {
	wu.mutation.RemoveNodeIDs(ids...)
	return wu
}

// RemoveNodes removes "nodes" edges to WorkflowNode entities.
func (wu *WorkflowUpdate) RemoveNodes(w ...*WorkflowNode) *WorkflowUpdate {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wu.RemoveNodeIDs(ids...)
}

// ClearLinks clears all "links" edges to the WorkflowLink entity.
func (wu *WorkflowUpdate) ClearLinks() *WorkflowUpdate {
	wu.mutation.ClearLinks()
	return wu
}

// RemoveLinkIDs removes the "links" edge to WorkflowLink entities by IDs.
func (wu *WorkflowUpdate) RemoveLinkIDs(ids ...uuid.UUID) *WorkflowUpdate {
	wu.mutation.RemoveLinkIDs(ids...)
	return wu
}

// RemoveLinks removes "links" edges to WorkflowLink entities.
func (wu *WorkflowUpdate) RemoveLinks(w ...*WorkflowLink) *WorkflowUpdate {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wu.RemoveLinkIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (wu *WorkflowUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, wu.sqlSave, wu.mutation, wu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wu *WorkflowUpdate) SaveX(ctx context.Context) int {
	affected, err := wu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (wu *WorkflowUpdate) Exec(ctx context.Context) error {
	_, err := wu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wu *WorkflowUpdate) ExecX(ctx context.Context) {
	if err := wu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wu *WorkflowUpdate) check() error {
	if v, ok := wu.mutation.Status(); ok {
		if err := workflow.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Workflow.status": %w`, err)}
		}
	}
	return nil
}

func (wu *WorkflowUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := wu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(workflow.Table, workflow.Columns, sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID))
	if ps := wu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wu.mutation.Name(); ok {
		_spec.SetField(workflow.FieldName, field.TypeString, value)
	}
	if value, ok := wu.mutation.Icon(); ok {
		_spec.SetField(workflow.FieldIcon, field.TypeString, value)
	}
	if value, ok := wu.mutation.Description(); ok {
		_spec.SetField(workflow.FieldDescription, field.TypeString, value)
	}
	if value, ok := wu.mutation.Status(); ok {
		_spec.SetField(workflow.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := wu.mutation.Viewport(); ok {
		_spec.SetField(workflow.FieldViewport, field.TypeJSON, value)
	}
	if value, ok := wu.mutation.UpdatedAt(); ok {
		_spec.SetField(workflow.FieldUpdatedAt, field.TypeTime, value)
	}
	if wu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflow.UserTable,
			Columns: []string{workflow.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflow.UserTable,
			Columns: []string{workflow.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wu.mutation.NodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.RemovedNodesIDs(); len(nodes) > 0 && !wu.mutation.NodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.NodesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wu.mutation.LinksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.LinksTable,
			Columns: []string{workflow.LinksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowlink.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.RemovedLinksIDs(); len(nodes) > 0 && !wu.mutation.LinksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.LinksTable,
			Columns: []string{workflow.LinksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowlink.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.LinksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.LinksTable,
			Columns: []string{workflow.LinksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowlink.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, wu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workflow.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	wu.mutation.done = true
	return n, nil
}

// WorkflowUpdateOne is the builder for updating a single Workflow entity.
type WorkflowUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *WorkflowMutation
}

// SetUserID sets the "user_id" field.
func (wuo *WorkflowUpdateOne) SetUserID(u uuid.UUID) *WorkflowUpdateOne {
	wuo.mutation.SetUserID(u)
	return wuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableUserID(u *uuid.UUID) *WorkflowUpdateOne {
	if u != nil {
		wuo.SetUserID(*u)
	}
	return wuo
}

// ClearUserID clears the value of the "user_id" field.
func (wuo *WorkflowUpdateOne) ClearUserID() *WorkflowUpdateOne {
	wuo.mutation.ClearUserID()
	return wuo
}

// SetName sets the "name" field.
func (wuo *WorkflowUpdateOne) SetName(s string) *WorkflowUpdateOne {
	wuo.mutation.SetName(s)
	return wuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableName(s *string) *WorkflowUpdateOne {
	if s != nil {
		wuo.SetName(*s)
	}
	return wuo
}

// SetIcon sets the "icon" field.
func (wuo *WorkflowUpdateOne) SetIcon(s string) *WorkflowUpdateOne {
	wuo.mutation.SetIcon(s)
	return wuo
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableIcon(s *string) *WorkflowUpdateOne {
	if s != nil {
		wuo.SetIcon(*s)
	}
	return wuo
}

// SetDescription sets the "description" field.
func (wuo *WorkflowUpdateOne) SetDescription(s string) *WorkflowUpdateOne {
	wuo.mutation.SetDescription(s)
	return wuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableDescription(s *string) *WorkflowUpdateOne {
	if s != nil {
		wuo.SetDescription(*s)
	}
	return wuo
}

// SetStatus sets the "status" field.
func (wuo *WorkflowUpdateOne) SetStatus(e enums.Status) *WorkflowUpdateOne {
	wuo.mutation.SetStatus(e)
	return wuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableStatus(e *enums.Status) *WorkflowUpdateOne {
	if e != nil {
		wuo.SetStatus(*e)
	}
	return wuo
}

// SetViewport sets the "viewport" field.
func (wuo *WorkflowUpdateOne) SetViewport(vv *v1.WorkflowViewport) *WorkflowUpdateOne {
	wuo.mutation.SetViewport(vv)
	return wuo
}

// SetUpdatedAt sets the "updated_at" field.
func (wuo *WorkflowUpdateOne) SetUpdatedAt(t time.Time) *WorkflowUpdateOne {
	wuo.mutation.SetUpdatedAt(t)
	return wuo
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableUpdatedAt(t *time.Time) *WorkflowUpdateOne {
	if t != nil {
		wuo.SetUpdatedAt(*t)
	}
	return wuo
}

// SetUser sets the "user" edge to the User entity.
func (wuo *WorkflowUpdateOne) SetUser(u *User) *WorkflowUpdateOne {
	return wuo.SetUserID(u.ID)
}

// AddNodeIDs adds the "nodes" edge to the WorkflowNode entity by IDs.
func (wuo *WorkflowUpdateOne) AddNodeIDs(ids ...uuid.UUID) *WorkflowUpdateOne {
	wuo.mutation.AddNodeIDs(ids...)
	return wuo
}

// AddNodes adds the "nodes" edges to the WorkflowNode entity.
func (wuo *WorkflowUpdateOne) AddNodes(w ...*WorkflowNode) *WorkflowUpdateOne {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wuo.AddNodeIDs(ids...)
}

// AddLinkIDs adds the "links" edge to the WorkflowLink entity by IDs.
func (wuo *WorkflowUpdateOne) AddLinkIDs(ids ...uuid.UUID) *WorkflowUpdateOne {
	wuo.mutation.AddLinkIDs(ids...)
	return wuo
}

// AddLinks adds the "links" edges to the WorkflowLink entity.
func (wuo *WorkflowUpdateOne) AddLinks(w ...*WorkflowLink) *WorkflowUpdateOne {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wuo.AddLinkIDs(ids...)
}

// Mutation returns the WorkflowMutation object of the builder.
func (wuo *WorkflowUpdateOne) Mutation() *WorkflowMutation {
	return wuo.mutation
}

// ClearUser clears the "user" edge to the User entity.
func (wuo *WorkflowUpdateOne) ClearUser() *WorkflowUpdateOne {
	wuo.mutation.ClearUser()
	return wuo
}

// ClearNodes clears all "nodes" edges to the WorkflowNode entity.
func (wuo *WorkflowUpdateOne) ClearNodes() *WorkflowUpdateOne {
	wuo.mutation.ClearNodes()
	return wuo
}

// RemoveNodeIDs removes the "nodes" edge to WorkflowNode entities by IDs.
func (wuo *WorkflowUpdateOne) RemoveNodeIDs(ids ...uuid.UUID) *WorkflowUpdateOne {
	wuo.mutation.RemoveNodeIDs(ids...)
	return wuo
}

// RemoveNodes removes "nodes" edges to WorkflowNode entities.
func (wuo *WorkflowUpdateOne) RemoveNodes(w ...*WorkflowNode) *WorkflowUpdateOne {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wuo.RemoveNodeIDs(ids...)
}

// ClearLinks clears all "links" edges to the WorkflowLink entity.
func (wuo *WorkflowUpdateOne) ClearLinks() *WorkflowUpdateOne {
	wuo.mutation.ClearLinks()
	return wuo
}

// RemoveLinkIDs removes the "links" edge to WorkflowLink entities by IDs.
func (wuo *WorkflowUpdateOne) RemoveLinkIDs(ids ...uuid.UUID) *WorkflowUpdateOne {
	wuo.mutation.RemoveLinkIDs(ids...)
	return wuo
}

// RemoveLinks removes "links" edges to WorkflowLink entities.
func (wuo *WorkflowUpdateOne) RemoveLinks(w ...*WorkflowLink) *WorkflowUpdateOne {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wuo.RemoveLinkIDs(ids...)
}

// Where appends a list predicates to the WorkflowUpdate builder.
func (wuo *WorkflowUpdateOne) Where(ps ...predicate.Workflow) *WorkflowUpdateOne {
	wuo.mutation.Where(ps...)
	return wuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (wuo *WorkflowUpdateOne) Select(field string, fields ...string) *WorkflowUpdateOne {
	wuo.fields = append([]string{field}, fields...)
	return wuo
}

// Save executes the query and returns the updated Workflow entity.
func (wuo *WorkflowUpdateOne) Save(ctx context.Context) (*Workflow, error) {
	return withHooks(ctx, wuo.sqlSave, wuo.mutation, wuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wuo *WorkflowUpdateOne) SaveX(ctx context.Context) *Workflow {
	node, err := wuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (wuo *WorkflowUpdateOne) Exec(ctx context.Context) error {
	_, err := wuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wuo *WorkflowUpdateOne) ExecX(ctx context.Context) {
	if err := wuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wuo *WorkflowUpdateOne) check() error {
	if v, ok := wuo.mutation.Status(); ok {
		if err := workflow.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Workflow.status": %w`, err)}
		}
	}
	return nil
}

func (wuo *WorkflowUpdateOne) sqlSave(ctx context.Context) (_node *Workflow, err error) {
	if err := wuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(workflow.Table, workflow.Columns, sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID))
	id, ok := wuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Workflow.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := wuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, workflow.FieldID)
		for _, f := range fields {
			if !workflow.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != workflow.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := wuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wuo.mutation.Name(); ok {
		_spec.SetField(workflow.FieldName, field.TypeString, value)
	}
	if value, ok := wuo.mutation.Icon(); ok {
		_spec.SetField(workflow.FieldIcon, field.TypeString, value)
	}
	if value, ok := wuo.mutation.Description(); ok {
		_spec.SetField(workflow.FieldDescription, field.TypeString, value)
	}
	if value, ok := wuo.mutation.Status(); ok {
		_spec.SetField(workflow.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := wuo.mutation.Viewport(); ok {
		_spec.SetField(workflow.FieldViewport, field.TypeJSON, value)
	}
	if value, ok := wuo.mutation.UpdatedAt(); ok {
		_spec.SetField(workflow.FieldUpdatedAt, field.TypeTime, value)
	}
	if wuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflow.UserTable,
			Columns: []string{workflow.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflow.UserTable,
			Columns: []string{workflow.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wuo.mutation.NodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.RemovedNodesIDs(); len(nodes) > 0 && !wuo.mutation.NodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.NodesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wuo.mutation.LinksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.LinksTable,
			Columns: []string{workflow.LinksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowlink.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.RemovedLinksIDs(); len(nodes) > 0 && !wuo.mutation.LinksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.LinksTable,
			Columns: []string{workflow.LinksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowlink.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.LinksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.LinksTable,
			Columns: []string{workflow.LinksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowlink.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Workflow{config: wuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, wuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workflow.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	wuo.mutation.done = true
	return _node, nil
}
