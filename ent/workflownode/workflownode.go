// Code generated by ent, DO NOT EDIT.

package workflownode

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the workflownode type in the database.
	Label = "workflow_node"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldWorkflowID holds the string denoting the workflow_id field in the database.
	FieldWorkflowID = "workflow_id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldIcon holds the string denoting the icon field in the database.
	FieldIcon = "icon"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldPluginName holds the string denoting the plugin_name field in the database.
	FieldPluginName = "plugin_name"
	// FieldPluginVersion holds the string denoting the plugin_version field in the database.
	FieldPluginVersion = "plugin_version"
	// FieldInputParams holds the string denoting the input_params field in the database.
	FieldInputParams = "input_params"
	// FieldInputValues holds the string denoting the input_values field in the database.
	FieldInputValues = "input_values"
	// FieldOutputParams holds the string denoting the output_params field in the database.
	FieldOutputParams = "output_params"
	// FieldOutputValues holds the string denoting the output_values field in the database.
	FieldOutputValues = "output_values"
	// FieldInputPorts holds the string denoting the input_ports field in the database.
	FieldInputPorts = "input_ports"
	// FieldOutputPorts holds the string denoting the output_ports field in the database.
	FieldOutputPorts = "output_ports"
	// FieldPosition holds the string denoting the position field in the database.
	FieldPosition = "position"
	// FieldData holds the string denoting the data field in the database.
	FieldData = "data"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// EdgeWorkflow holds the string denoting the workflow edge name in mutations.
	EdgeWorkflow = "workflow"
	// Table holds the table name of the workflownode in the database.
	Table = "workflow_nodes"
	// WorkflowTable is the table that holds the workflow relation/edge.
	WorkflowTable = "workflow_nodes"
	// WorkflowInverseTable is the table name for the Workflow entity.
	// It exists in this package in order to avoid circular dependency with the "workflow" package.
	WorkflowInverseTable = "workflows"
	// WorkflowColumn is the table column denoting the workflow relation/edge.
	WorkflowColumn = "workflow_id"
)

// Columns holds all SQL columns for workflownode fields.
var Columns = []string{
	FieldID,
	FieldWorkflowID,
	FieldName,
	FieldDescription,
	FieldIcon,
	FieldType,
	FieldVersion,
	FieldPluginName,
	FieldPluginVersion,
	FieldInputParams,
	FieldInputValues,
	FieldOutputParams,
	FieldOutputValues,
	FieldInputPorts,
	FieldOutputPorts,
	FieldPosition,
	FieldData,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the WorkflowNode queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByWorkflowID orders the results by the workflow_id field.
func ByWorkflowID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWorkflowID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByIcon orders the results by the icon field.
func ByIcon(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIcon, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByPluginName orders the results by the plugin_name field.
func ByPluginName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPluginName, opts...).ToFunc()
}

// ByPluginVersion orders the results by the plugin_version field.
func ByPluginVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPluginVersion, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByWorkflowField orders the results by workflow field.
func ByWorkflowField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newWorkflowStep(), sql.OrderByField(field, opts...))
	}
}
func newWorkflowStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(WorkflowInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, WorkflowTable, WorkflowColumn),
	)
}
