// Code generated by ent, DO NOT EDIT.

package workflownode

import (
	"resflow/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldID, id))
}

// WorkflowID applies equality check predicate on the "workflow_id" field. It's identical to WorkflowIDEQ.
func WorkflowID(v uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldWorkflowID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldName, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldDescription, v))
}

// Icon applies equality check predicate on the "icon" field. It's identical to IconEQ.
func Icon(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldIcon, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldType, v))
}

// Version applies equality check predicate on the "version" field. It's identical to VersionEQ.
func Version(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldVersion, v))
}

// PluginName applies equality check predicate on the "plugin_name" field. It's identical to PluginNameEQ.
func PluginName(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldPluginName, v))
}

// PluginVersion applies equality check predicate on the "plugin_version" field. It's identical to PluginVersionEQ.
func PluginVersion(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldPluginVersion, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldUpdatedAt, v))
}

// WorkflowIDEQ applies the EQ predicate on the "workflow_id" field.
func WorkflowIDEQ(v uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldWorkflowID, v))
}

// WorkflowIDNEQ applies the NEQ predicate on the "workflow_id" field.
func WorkflowIDNEQ(v uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldWorkflowID, v))
}

// WorkflowIDIn applies the In predicate on the "workflow_id" field.
func WorkflowIDIn(vs ...uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldWorkflowID, vs...))
}

// WorkflowIDNotIn applies the NotIn predicate on the "workflow_id" field.
func WorkflowIDNotIn(vs ...uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldWorkflowID, vs...))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldName, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldDescription, v))
}

// IconEQ applies the EQ predicate on the "icon" field.
func IconEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldIcon, v))
}

// IconNEQ applies the NEQ predicate on the "icon" field.
func IconNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldIcon, v))
}

// IconIn applies the In predicate on the "icon" field.
func IconIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldIcon, vs...))
}

// IconNotIn applies the NotIn predicate on the "icon" field.
func IconNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldIcon, vs...))
}

// IconGT applies the GT predicate on the "icon" field.
func IconGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldIcon, v))
}

// IconGTE applies the GTE predicate on the "icon" field.
func IconGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldIcon, v))
}

// IconLT applies the LT predicate on the "icon" field.
func IconLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldIcon, v))
}

// IconLTE applies the LTE predicate on the "icon" field.
func IconLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldIcon, v))
}

// IconContains applies the Contains predicate on the "icon" field.
func IconContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldIcon, v))
}

// IconHasPrefix applies the HasPrefix predicate on the "icon" field.
func IconHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldIcon, v))
}

// IconHasSuffix applies the HasSuffix predicate on the "icon" field.
func IconHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldIcon, v))
}

// IconEqualFold applies the EqualFold predicate on the "icon" field.
func IconEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldIcon, v))
}

// IconContainsFold applies the ContainsFold predicate on the "icon" field.
func IconContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldIcon, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldType, v))
}

// VersionEQ applies the EQ predicate on the "version" field.
func VersionEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldVersion, v))
}

// VersionNEQ applies the NEQ predicate on the "version" field.
func VersionNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldVersion, v))
}

// VersionIn applies the In predicate on the "version" field.
func VersionIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldVersion, vs...))
}

// VersionNotIn applies the NotIn predicate on the "version" field.
func VersionNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldVersion, vs...))
}

// VersionGT applies the GT predicate on the "version" field.
func VersionGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldVersion, v))
}

// VersionGTE applies the GTE predicate on the "version" field.
func VersionGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldVersion, v))
}

// VersionLT applies the LT predicate on the "version" field.
func VersionLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldVersion, v))
}

// VersionLTE applies the LTE predicate on the "version" field.
func VersionLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldVersion, v))
}

// VersionContains applies the Contains predicate on the "version" field.
func VersionContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldVersion, v))
}

// VersionHasPrefix applies the HasPrefix predicate on the "version" field.
func VersionHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldVersion, v))
}

// VersionHasSuffix applies the HasSuffix predicate on the "version" field.
func VersionHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldVersion, v))
}

// VersionEqualFold applies the EqualFold predicate on the "version" field.
func VersionEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldVersion, v))
}

// VersionContainsFold applies the ContainsFold predicate on the "version" field.
func VersionContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldVersion, v))
}

// PluginNameEQ applies the EQ predicate on the "plugin_name" field.
func PluginNameEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldPluginName, v))
}

// PluginNameNEQ applies the NEQ predicate on the "plugin_name" field.
func PluginNameNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldPluginName, v))
}

// PluginNameIn applies the In predicate on the "plugin_name" field.
func PluginNameIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldPluginName, vs...))
}

// PluginNameNotIn applies the NotIn predicate on the "plugin_name" field.
func PluginNameNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldPluginName, vs...))
}

// PluginNameGT applies the GT predicate on the "plugin_name" field.
func PluginNameGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldPluginName, v))
}

// PluginNameGTE applies the GTE predicate on the "plugin_name" field.
func PluginNameGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldPluginName, v))
}

// PluginNameLT applies the LT predicate on the "plugin_name" field.
func PluginNameLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldPluginName, v))
}

// PluginNameLTE applies the LTE predicate on the "plugin_name" field.
func PluginNameLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldPluginName, v))
}

// PluginNameContains applies the Contains predicate on the "plugin_name" field.
func PluginNameContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldPluginName, v))
}

// PluginNameHasPrefix applies the HasPrefix predicate on the "plugin_name" field.
func PluginNameHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldPluginName, v))
}

// PluginNameHasSuffix applies the HasSuffix predicate on the "plugin_name" field.
func PluginNameHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldPluginName, v))
}

// PluginNameEqualFold applies the EqualFold predicate on the "plugin_name" field.
func PluginNameEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldPluginName, v))
}

// PluginNameContainsFold applies the ContainsFold predicate on the "plugin_name" field.
func PluginNameContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldPluginName, v))
}

// PluginVersionEQ applies the EQ predicate on the "plugin_version" field.
func PluginVersionEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldPluginVersion, v))
}

// PluginVersionNEQ applies the NEQ predicate on the "plugin_version" field.
func PluginVersionNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldPluginVersion, v))
}

// PluginVersionIn applies the In predicate on the "plugin_version" field.
func PluginVersionIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldPluginVersion, vs...))
}

// PluginVersionNotIn applies the NotIn predicate on the "plugin_version" field.
func PluginVersionNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldPluginVersion, vs...))
}

// PluginVersionGT applies the GT predicate on the "plugin_version" field.
func PluginVersionGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldPluginVersion, v))
}

// PluginVersionGTE applies the GTE predicate on the "plugin_version" field.
func PluginVersionGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldPluginVersion, v))
}

// PluginVersionLT applies the LT predicate on the "plugin_version" field.
func PluginVersionLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldPluginVersion, v))
}

// PluginVersionLTE applies the LTE predicate on the "plugin_version" field.
func PluginVersionLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldPluginVersion, v))
}

// PluginVersionContains applies the Contains predicate on the "plugin_version" field.
func PluginVersionContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldPluginVersion, v))
}

// PluginVersionHasPrefix applies the HasPrefix predicate on the "plugin_version" field.
func PluginVersionHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldPluginVersion, v))
}

// PluginVersionHasSuffix applies the HasSuffix predicate on the "plugin_version" field.
func PluginVersionHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldPluginVersion, v))
}

// PluginVersionEqualFold applies the EqualFold predicate on the "plugin_version" field.
func PluginVersionEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldPluginVersion, v))
}

// PluginVersionContainsFold applies the ContainsFold predicate on the "plugin_version" field.
func PluginVersionContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldPluginVersion, v))
}

// DataIsNil applies the IsNil predicate on the "data" field.
func DataIsNil() predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIsNull(FieldData))
}

// DataNotNil applies the NotNil predicate on the "data" field.
func DataNotNil() predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotNull(FieldData))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldUpdatedAt, v))
}

// HasWorkflow applies the HasEdge predicate on the "workflow" edge.
func HasWorkflow() predicate.WorkflowNode {
	return predicate.WorkflowNode(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, WorkflowTable, WorkflowColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasWorkflowWith applies the HasEdge predicate on the "workflow" edge with a given conditions (other predicates).
func HasWorkflowWith(preds ...predicate.Workflow) predicate.WorkflowNode {
	return predicate.WorkflowNode(func(s *sql.Selector) {
		step := newWorkflowStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WorkflowNode) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WorkflowNode) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WorkflowNode) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.NotPredicates(p))
}
