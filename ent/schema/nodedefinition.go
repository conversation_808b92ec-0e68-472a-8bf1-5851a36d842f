package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
	v1 "resflow/proto/generated_go/v1"
	"time"
)

// NodeDefinition holds the schema definition for the NodeDefinition entity.
type NodeDefinition struct {
	ent.Schema
}

// Fields of the NodeDefinition.
func (NodeDefinition) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).Default(uuid.New),
		field.String("plugin_name"),
		field.String("plugin_version"),
		field.String("name"),
		field.String("author"),
		field.String("description"),
		field.String("icon"),
		field.String("type"),
		field.String("version"),
		field.String("category"),
		field.JSON("input_params", []*v1.NodeParam{}),
		field.JSON("output_params", []*v1.NodeParam{}),
		field.JSON("input_ports", []*v1.NodePort{}),
		field.JSON("output_ports", []*v1.NodePort{}),
		field.Bool("exception"),
		field.String("path"),
		field.Bool("builtin"),
		field.Bool("enabled"),
		field.Time("created_at").Default(time.Now).Immutable(),
		field.Time("updated_at").Default(time.Now),
	}
}

// Edges of the NodeDefinition.
func (NodeDefinition) Edges() []ent.Edge {
	return nil
}

func (NodeDefinition) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("type", "version", "plugin_name", "plugin_version").Unique(),
	}
}
