package schema

import (
	"encoding/json"
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	v1 "resflow/proto/generated_go/v1"
	"time"
)

// WorkflowNode holds the schema definition for the WorkflowNode entity.
type WorkflowNode struct {
	ent.Schema
}

// Fields of the WorkflowNode.
func (WorkflowNode) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).Default(uuid.New),
		field.UUID("workflow_id", uuid.UUID{}),
		field.String("name"),
		field.String("description"),
		field.String("icon"),
		field.String("type"),
		field.String("version"),
		field.String("plugin_name"),
		field.String("plugin_version"),
		field.JSON("input_params", []*v1.NodeParam{}),
		field.JSON("input_values", json.RawMessage{}),
		field.JSON("output_params", []*v1.NodeParam{}),
		field.JSON("output_values", json.RawMessage{}),
		field.JSON("input_ports", []*v1.NodePort{}),
		field.JSON("output_ports", []*v1.NodePort{}),
		field.JSON("position", &v1.WorkflowNodePosition{}),
		field.JSON("data", json.RawMessage{}).Optional(),
		field.Time("created_at").Default(time.Now).Immutable(),
		field.Time("updated_at").Default(time.Now),
	}
}

// Edges of the WorkflowNode.
func (WorkflowNode) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("workflow", Workflow.Type).Ref("nodes").Unique().Field("workflow_id").Required(),
	}
}
