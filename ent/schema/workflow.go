package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"resflow/enums"
	v1 "resflow/proto/generated_go/v1"
	"time"
)

// Workflow holds the schema definition for the Workflow entity.
type Workflow struct {
	ent.Schema
}

// Fields of the Workflow.
func (Workflow) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).Default(uuid.New),
		field.UUID("user_id", uuid.UUID{}).Optional(),
		field.String("name"),
		field.String("icon"),
		field.String("description"),
		field.Enum("status").GoType(enums.Status(1)),
		field.JSON("viewport", &v1.WorkflowViewport{}),
		field.Time("created_at").Default(time.Now).Immutable(),
		field.Time("updated_at").Default(time.Now),
	}
}

// Edges of the Workflow.
func (Workflow) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("user", User.Type).Ref("workflows").Unique().Field("user_id"),
		edge.To("nodes", WorkflowNode.Type),
		edge.To("links", WorkflowLink.Type),
	}
}
