package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"time"
)

// WorkflowLink holds the schema definition for the WorkflowLink entity.
type WorkflowLink struct {
	ent.Schema
}

// Fields of the WorkflowLink.
func (WorkflowLink) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).Default(uuid.New),
		field.UUID("workflow_id", uuid.UUID{}),
		field.UUID("from_node_id", uuid.UUID{}),
		field.UUID("to_node_id", uuid.UUID{}),
		field.String("from_port_id"),
		field.String("to_port_id"),
		field.String("type"),
		field.Time("created_at").Default(time.Now).Immutable(),
		field.Time("updated_at").Default(time.Now),
	}
}

// Edges of the WorkflowLink.
func (WorkflowLink) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("workflow", Workflow.Type).Ref("links").Unique().Field("workflow_id").Required(),
	}
}
