// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"resflow/ent/workflow"
	"resflow/ent/workflownode"
	v1 "resflow/proto/generated_go/v1"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

// WorkflowNode is the model entity for the WorkflowNode schema.
type WorkflowNode struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// WorkflowID holds the value of the "workflow_id" field.
	WorkflowID uuid.UUID `json:"workflow_id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Icon holds the value of the "icon" field.
	Icon string `json:"icon,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// Version holds the value of the "version" field.
	Version string `json:"version,omitempty"`
	// PluginName holds the value of the "plugin_name" field.
	PluginName string `json:"plugin_name,omitempty"`
	// PluginVersion holds the value of the "plugin_version" field.
	PluginVersion string `json:"plugin_version,omitempty"`
	// InputParams holds the value of the "input_params" field.
	InputParams []*v1.NodeParam `json:"input_params,omitempty"`
	// InputValues holds the value of the "input_values" field.
	InputValues json.RawMessage `json:"input_values,omitempty"`
	// OutputParams holds the value of the "output_params" field.
	OutputParams []*v1.NodeParam `json:"output_params,omitempty"`
	// OutputValues holds the value of the "output_values" field.
	OutputValues json.RawMessage `json:"output_values,omitempty"`
	// InputPorts holds the value of the "input_ports" field.
	InputPorts []*v1.NodePort `json:"input_ports,omitempty"`
	// OutputPorts holds the value of the "output_ports" field.
	OutputPorts []*v1.NodePort `json:"output_ports,omitempty"`
	// Position holds the value of the "position" field.
	Position *v1.WorkflowNodePosition `json:"position,omitempty"`
	// Data holds the value of the "data" field.
	Data json.RawMessage `json:"data,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the WorkflowNodeQuery when eager-loading is set.
	Edges        WorkflowNodeEdges `json:"edges"`
	selectValues sql.SelectValues
}

// WorkflowNodeEdges holds the relations/edges for other nodes in the graph.
type WorkflowNodeEdges struct {
	// Workflow holds the value of the workflow edge.
	Workflow *Workflow `json:"workflow,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// WorkflowOrErr returns the Workflow value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e WorkflowNodeEdges) WorkflowOrErr() (*Workflow, error) {
	if e.Workflow != nil {
		return e.Workflow, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: workflow.Label}
	}
	return nil, &NotLoadedError{edge: "workflow"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*WorkflowNode) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case workflownode.FieldInputParams, workflownode.FieldInputValues, workflownode.FieldOutputParams, workflownode.FieldOutputValues, workflownode.FieldInputPorts, workflownode.FieldOutputPorts, workflownode.FieldPosition, workflownode.FieldData:
			values[i] = new([]byte)
		case workflownode.FieldName, workflownode.FieldDescription, workflownode.FieldIcon, workflownode.FieldType, workflownode.FieldVersion, workflownode.FieldPluginName, workflownode.FieldPluginVersion:
			values[i] = new(sql.NullString)
		case workflownode.FieldCreatedAt, workflownode.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case workflownode.FieldID, workflownode.FieldWorkflowID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the WorkflowNode fields.
func (wn *WorkflowNode) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case workflownode.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				wn.ID = *value
			}
		case workflownode.FieldWorkflowID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field workflow_id", values[i])
			} else if value != nil {
				wn.WorkflowID = *value
			}
		case workflownode.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				wn.Name = value.String
			}
		case workflownode.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				wn.Description = value.String
			}
		case workflownode.FieldIcon:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field icon", values[i])
			} else if value.Valid {
				wn.Icon = value.String
			}
		case workflownode.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				wn.Type = value.String
			}
		case workflownode.FieldVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				wn.Version = value.String
			}
		case workflownode.FieldPluginName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field plugin_name", values[i])
			} else if value.Valid {
				wn.PluginName = value.String
			}
		case workflownode.FieldPluginVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field plugin_version", values[i])
			} else if value.Valid {
				wn.PluginVersion = value.String
			}
		case workflownode.FieldInputParams:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field input_params", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &wn.InputParams); err != nil {
					return fmt.Errorf("unmarshal field input_params: %w", err)
				}
			}
		case workflownode.FieldInputValues:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field input_values", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &wn.InputValues); err != nil {
					return fmt.Errorf("unmarshal field input_values: %w", err)
				}
			}
		case workflownode.FieldOutputParams:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field output_params", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &wn.OutputParams); err != nil {
					return fmt.Errorf("unmarshal field output_params: %w", err)
				}
			}
		case workflownode.FieldOutputValues:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field output_values", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &wn.OutputValues); err != nil {
					return fmt.Errorf("unmarshal field output_values: %w", err)
				}
			}
		case workflownode.FieldInputPorts:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field input_ports", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &wn.InputPorts); err != nil {
					return fmt.Errorf("unmarshal field input_ports: %w", err)
				}
			}
		case workflownode.FieldOutputPorts:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field output_ports", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &wn.OutputPorts); err != nil {
					return fmt.Errorf("unmarshal field output_ports: %w", err)
				}
			}
		case workflownode.FieldPosition:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field position", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &wn.Position); err != nil {
					return fmt.Errorf("unmarshal field position: %w", err)
				}
			}
		case workflownode.FieldData:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field data", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &wn.Data); err != nil {
					return fmt.Errorf("unmarshal field data: %w", err)
				}
			}
		case workflownode.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				wn.CreatedAt = value.Time
			}
		case workflownode.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				wn.UpdatedAt = value.Time
			}
		default:
			wn.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the WorkflowNode.
// This includes values selected through modifiers, order, etc.
func (wn *WorkflowNode) Value(name string) (ent.Value, error) {
	return wn.selectValues.Get(name)
}

// QueryWorkflow queries the "workflow" edge of the WorkflowNode entity.
func (wn *WorkflowNode) QueryWorkflow() *WorkflowQuery {
	return NewWorkflowNodeClient(wn.config).QueryWorkflow(wn)
}

// Update returns a builder for updating this WorkflowNode.
// Note that you need to call WorkflowNode.Unwrap() before calling this method if this WorkflowNode
// was returned from a transaction, and the transaction was committed or rolled back.
func (wn *WorkflowNode) Update() *WorkflowNodeUpdateOne {
	return NewWorkflowNodeClient(wn.config).UpdateOne(wn)
}

// Unwrap unwraps the WorkflowNode entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (wn *WorkflowNode) Unwrap() *WorkflowNode {
	_tx, ok := wn.config.driver.(*txDriver)
	if !ok {
		panic("ent: WorkflowNode is not a transactional entity")
	}
	wn.config.driver = _tx.drv
	return wn
}

// String implements the fmt.Stringer.
func (wn *WorkflowNode) String() string {
	var builder strings.Builder
	builder.WriteString("WorkflowNode(")
	builder.WriteString(fmt.Sprintf("id=%v, ", wn.ID))
	builder.WriteString("workflow_id=")
	builder.WriteString(fmt.Sprintf("%v", wn.WorkflowID))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(wn.Name)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(wn.Description)
	builder.WriteString(", ")
	builder.WriteString("icon=")
	builder.WriteString(wn.Icon)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(wn.Type)
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(wn.Version)
	builder.WriteString(", ")
	builder.WriteString("plugin_name=")
	builder.WriteString(wn.PluginName)
	builder.WriteString(", ")
	builder.WriteString("plugin_version=")
	builder.WriteString(wn.PluginVersion)
	builder.WriteString(", ")
	builder.WriteString("input_params=")
	builder.WriteString(fmt.Sprintf("%v", wn.InputParams))
	builder.WriteString(", ")
	builder.WriteString("input_values=")
	builder.WriteString(fmt.Sprintf("%v", wn.InputValues))
	builder.WriteString(", ")
	builder.WriteString("output_params=")
	builder.WriteString(fmt.Sprintf("%v", wn.OutputParams))
	builder.WriteString(", ")
	builder.WriteString("output_values=")
	builder.WriteString(fmt.Sprintf("%v", wn.OutputValues))
	builder.WriteString(", ")
	builder.WriteString("input_ports=")
	builder.WriteString(fmt.Sprintf("%v", wn.InputPorts))
	builder.WriteString(", ")
	builder.WriteString("output_ports=")
	builder.WriteString(fmt.Sprintf("%v", wn.OutputPorts))
	builder.WriteString(", ")
	builder.WriteString("position=")
	builder.WriteString(fmt.Sprintf("%v", wn.Position))
	builder.WriteString(", ")
	builder.WriteString("data=")
	builder.WriteString(fmt.Sprintf("%v", wn.Data))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(wn.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(wn.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// WorkflowNodes is a parsable slice of WorkflowNode.
type WorkflowNodes []*WorkflowNode
