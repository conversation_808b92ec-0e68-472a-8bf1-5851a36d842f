// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"resflow/ent/predicate"
	"resflow/ent/workflow"
	"resflow/ent/workflowlink"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowLinkQuery is the builder for querying WorkflowLink entities.
type WorkflowLinkQuery struct {
	config
	ctx          *QueryContext
	order        []workflowlink.OrderOption
	inters       []Interceptor
	predicates   []predicate.WorkflowLink
	withWorkflow *WorkflowQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the WorkflowLinkQuery builder.
func (wlq *WorkflowLinkQuery) Where(ps ...predicate.WorkflowLink) *WorkflowLinkQuery {
	wlq.predicates = append(wlq.predicates, ps...)
	return wlq
}

// Limit the number of records to be returned by this query.
func (wlq *WorkflowLinkQuery) Limit(limit int) *WorkflowLinkQuery {
	wlq.ctx.Limit = &limit
	return wlq
}

// Offset to start from.
func (wlq *WorkflowLinkQuery) Offset(offset int) *WorkflowLinkQuery {
	wlq.ctx.Offset = &offset
	return wlq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (wlq *WorkflowLinkQuery) Unique(unique bool) *WorkflowLinkQuery {
	wlq.ctx.Unique = &unique
	return wlq
}

// Order specifies how the records should be ordered.
func (wlq *WorkflowLinkQuery) Order(o ...workflowlink.OrderOption) *WorkflowLinkQuery {
	wlq.order = append(wlq.order, o...)
	return wlq
}

// QueryWorkflow chains the current query on the "workflow" edge.
func (wlq *WorkflowLinkQuery) QueryWorkflow() *WorkflowQuery {
	query := (&WorkflowClient{config: wlq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := wlq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := wlq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(workflowlink.Table, workflowlink.FieldID, selector),
			sqlgraph.To(workflow.Table, workflow.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, workflowlink.WorkflowTable, workflowlink.WorkflowColumn),
		)
		fromU = sqlgraph.SetNeighbors(wlq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first WorkflowLink entity from the query.
// Returns a *NotFoundError when no WorkflowLink was found.
func (wlq *WorkflowLinkQuery) First(ctx context.Context) (*WorkflowLink, error) {
	nodes, err := wlq.Limit(1).All(setContextOp(ctx, wlq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{workflowlink.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (wlq *WorkflowLinkQuery) FirstX(ctx context.Context) *WorkflowLink {
	node, err := wlq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first WorkflowLink ID from the query.
// Returns a *NotFoundError when no WorkflowLink ID was found.
func (wlq *WorkflowLinkQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = wlq.Limit(1).IDs(setContextOp(ctx, wlq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{workflowlink.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (wlq *WorkflowLinkQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := wlq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single WorkflowLink entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one WorkflowLink entity is found.
// Returns a *NotFoundError when no WorkflowLink entities are found.
func (wlq *WorkflowLinkQuery) Only(ctx context.Context) (*WorkflowLink, error) {
	nodes, err := wlq.Limit(2).All(setContextOp(ctx, wlq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{workflowlink.Label}
	default:
		return nil, &NotSingularError{workflowlink.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (wlq *WorkflowLinkQuery) OnlyX(ctx context.Context) *WorkflowLink {
	node, err := wlq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only WorkflowLink ID in the query.
// Returns a *NotSingularError when more than one WorkflowLink ID is found.
// Returns a *NotFoundError when no entities are found.
func (wlq *WorkflowLinkQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = wlq.Limit(2).IDs(setContextOp(ctx, wlq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{workflowlink.Label}
	default:
		err = &NotSingularError{workflowlink.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (wlq *WorkflowLinkQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := wlq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of WorkflowLinks.
func (wlq *WorkflowLinkQuery) All(ctx context.Context) ([]*WorkflowLink, error) {
	ctx = setContextOp(ctx, wlq.ctx, ent.OpQueryAll)
	if err := wlq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*WorkflowLink, *WorkflowLinkQuery]()
	return withInterceptors[[]*WorkflowLink](ctx, wlq, qr, wlq.inters)
}

// AllX is like All, but panics if an error occurs.
func (wlq *WorkflowLinkQuery) AllX(ctx context.Context) []*WorkflowLink {
	nodes, err := wlq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of WorkflowLink IDs.
func (wlq *WorkflowLinkQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if wlq.ctx.Unique == nil && wlq.path != nil {
		wlq.Unique(true)
	}
	ctx = setContextOp(ctx, wlq.ctx, ent.OpQueryIDs)
	if err = wlq.Select(workflowlink.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (wlq *WorkflowLinkQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := wlq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (wlq *WorkflowLinkQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, wlq.ctx, ent.OpQueryCount)
	if err := wlq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, wlq, querierCount[*WorkflowLinkQuery](), wlq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (wlq *WorkflowLinkQuery) CountX(ctx context.Context) int {
	count, err := wlq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (wlq *WorkflowLinkQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, wlq.ctx, ent.OpQueryExist)
	switch _, err := wlq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (wlq *WorkflowLinkQuery) ExistX(ctx context.Context) bool {
	exist, err := wlq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the WorkflowLinkQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (wlq *WorkflowLinkQuery) Clone() *WorkflowLinkQuery {
	if wlq == nil {
		return nil
	}
	return &WorkflowLinkQuery{
		config:       wlq.config,
		ctx:          wlq.ctx.Clone(),
		order:        append([]workflowlink.OrderOption{}, wlq.order...),
		inters:       append([]Interceptor{}, wlq.inters...),
		predicates:   append([]predicate.WorkflowLink{}, wlq.predicates...),
		withWorkflow: wlq.withWorkflow.Clone(),
		// clone intermediate query.
		sql:  wlq.sql.Clone(),
		path: wlq.path,
	}
}

// WithWorkflow tells the query-builder to eager-load the nodes that are connected to
// the "workflow" edge. The optional arguments are used to configure the query builder of the edge.
func (wlq *WorkflowLinkQuery) WithWorkflow(opts ...func(*WorkflowQuery)) *WorkflowLinkQuery {
	query := (&WorkflowClient{config: wlq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	wlq.withWorkflow = query
	return wlq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		WorkflowID uuid.UUID `json:"workflow_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.WorkflowLink.Query().
//		GroupBy(workflowlink.FieldWorkflowID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (wlq *WorkflowLinkQuery) GroupBy(field string, fields ...string) *WorkflowLinkGroupBy {
	wlq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &WorkflowLinkGroupBy{build: wlq}
	grbuild.flds = &wlq.ctx.Fields
	grbuild.label = workflowlink.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		WorkflowID uuid.UUID `json:"workflow_id,omitempty"`
//	}
//
//	client.WorkflowLink.Query().
//		Select(workflowlink.FieldWorkflowID).
//		Scan(ctx, &v)
func (wlq *WorkflowLinkQuery) Select(fields ...string) *WorkflowLinkSelect {
	wlq.ctx.Fields = append(wlq.ctx.Fields, fields...)
	sbuild := &WorkflowLinkSelect{WorkflowLinkQuery: wlq}
	sbuild.label = workflowlink.Label
	sbuild.flds, sbuild.scan = &wlq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a WorkflowLinkSelect configured with the given aggregations.
func (wlq *WorkflowLinkQuery) Aggregate(fns ...AggregateFunc) *WorkflowLinkSelect {
	return wlq.Select().Aggregate(fns...)
}

func (wlq *WorkflowLinkQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range wlq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, wlq); err != nil {
				return err
			}
		}
	}
	for _, f := range wlq.ctx.Fields {
		if !workflowlink.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if wlq.path != nil {
		prev, err := wlq.path(ctx)
		if err != nil {
			return err
		}
		wlq.sql = prev
	}
	return nil
}

func (wlq *WorkflowLinkQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*WorkflowLink, error) {
	var (
		nodes       = []*WorkflowLink{}
		_spec       = wlq.querySpec()
		loadedTypes = [1]bool{
			wlq.withWorkflow != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*WorkflowLink).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &WorkflowLink{config: wlq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, wlq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := wlq.withWorkflow; query != nil {
		if err := wlq.loadWorkflow(ctx, query, nodes, nil,
			func(n *WorkflowLink, e *Workflow) { n.Edges.Workflow = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (wlq *WorkflowLinkQuery) loadWorkflow(ctx context.Context, query *WorkflowQuery, nodes []*WorkflowLink, init func(*WorkflowLink), assign func(*WorkflowLink, *Workflow)) error {
	ids := make([]uuid.UUID, 0, len(nodes))
	nodeids := make(map[uuid.UUID][]*WorkflowLink)
	for i := range nodes {
		fk := nodes[i].WorkflowID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(workflow.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "workflow_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (wlq *WorkflowLinkQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := wlq.querySpec()
	_spec.Node.Columns = wlq.ctx.Fields
	if len(wlq.ctx.Fields) > 0 {
		_spec.Unique = wlq.ctx.Unique != nil && *wlq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, wlq.driver, _spec)
}

func (wlq *WorkflowLinkQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(workflowlink.Table, workflowlink.Columns, sqlgraph.NewFieldSpec(workflowlink.FieldID, field.TypeUUID))
	_spec.From = wlq.sql
	if unique := wlq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if wlq.path != nil {
		_spec.Unique = true
	}
	if fields := wlq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, workflowlink.FieldID)
		for i := range fields {
			if fields[i] != workflowlink.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if wlq.withWorkflow != nil {
			_spec.Node.AddColumnOnce(workflowlink.FieldWorkflowID)
		}
	}
	if ps := wlq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := wlq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := wlq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := wlq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (wlq *WorkflowLinkQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(wlq.driver.Dialect())
	t1 := builder.Table(workflowlink.Table)
	columns := wlq.ctx.Fields
	if len(columns) == 0 {
		columns = workflowlink.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if wlq.sql != nil {
		selector = wlq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if wlq.ctx.Unique != nil && *wlq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range wlq.predicates {
		p(selector)
	}
	for _, p := range wlq.order {
		p(selector)
	}
	if offset := wlq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := wlq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// WorkflowLinkGroupBy is the group-by builder for WorkflowLink entities.
type WorkflowLinkGroupBy struct {
	selector
	build *WorkflowLinkQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (wlgb *WorkflowLinkGroupBy) Aggregate(fns ...AggregateFunc) *WorkflowLinkGroupBy {
	wlgb.fns = append(wlgb.fns, fns...)
	return wlgb
}

// Scan applies the selector query and scans the result into the given value.
func (wlgb *WorkflowLinkGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wlgb.build.ctx, ent.OpQueryGroupBy)
	if err := wlgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WorkflowLinkQuery, *WorkflowLinkGroupBy](ctx, wlgb.build, wlgb, wlgb.build.inters, v)
}

func (wlgb *WorkflowLinkGroupBy) sqlScan(ctx context.Context, root *WorkflowLinkQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(wlgb.fns))
	for _, fn := range wlgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*wlgb.flds)+len(wlgb.fns))
		for _, f := range *wlgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*wlgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wlgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// WorkflowLinkSelect is the builder for selecting fields of WorkflowLink entities.
type WorkflowLinkSelect struct {
	*WorkflowLinkQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (wls *WorkflowLinkSelect) Aggregate(fns ...AggregateFunc) *WorkflowLinkSelect {
	wls.fns = append(wls.fns, fns...)
	return wls
}

// Scan applies the selector query and scans the result into the given value.
func (wls *WorkflowLinkSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wls.ctx, ent.OpQuerySelect)
	if err := wls.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WorkflowLinkQuery, *WorkflowLinkSelect](ctx, wls.WorkflowLinkQuery, wls, wls.inters, v)
}

func (wls *WorkflowLinkSelect) sqlScan(ctx context.Context, root *WorkflowLinkQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(wls.fns))
	for _, fn := range wls.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*wls.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wls.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
