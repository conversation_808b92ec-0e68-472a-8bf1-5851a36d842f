// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"resflow/ent/predicate"
	"resflow/ent/workflowlink"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WorkflowLinkDelete is the builder for deleting a WorkflowLink entity.
type WorkflowLinkDelete struct {
	config
	hooks    []Hook
	mutation *WorkflowLinkMutation
}

// Where appends a list predicates to the WorkflowLinkDelete builder.
func (wld *WorkflowLinkDelete) Where(ps ...predicate.WorkflowLink) *WorkflowLinkDelete {
	wld.mutation.Where(ps...)
	return wld
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (wld *WorkflowLinkDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, wld.sqlExec, wld.mutation, wld.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (wld *WorkflowLinkDelete) ExecX(ctx context.Context) int {
	n, err := wld.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (wld *WorkflowLinkDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(workflowlink.Table, sqlgraph.NewFieldSpec(workflowlink.FieldID, field.TypeUUID))
	if ps := wld.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, wld.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	wld.mutation.done = true
	return affected, err
}

// WorkflowLinkDeleteOne is the builder for deleting a single WorkflowLink entity.
type WorkflowLinkDeleteOne struct {
	wld *WorkflowLinkDelete
}

// Where appends a list predicates to the WorkflowLinkDelete builder.
func (wldo *WorkflowLinkDeleteOne) Where(ps ...predicate.WorkflowLink) *WorkflowLinkDeleteOne {
	wldo.wld.mutation.Where(ps...)
	return wldo
}

// Exec executes the deletion query.
func (wldo *WorkflowLinkDeleteOne) Exec(ctx context.Context) error {
	n, err := wldo.wld.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{workflowlink.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (wldo *WorkflowLinkDeleteOne) ExecX(ctx context.Context) {
	if err := wldo.Exec(ctx); err != nil {
		panic(err)
	}
}
