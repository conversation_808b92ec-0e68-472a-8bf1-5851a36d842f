// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"resflow/ent/workflow"
	"resflow/ent/workflowlink"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

// WorkflowLink is the model entity for the WorkflowLink schema.
type WorkflowLink struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// WorkflowID holds the value of the "workflow_id" field.
	WorkflowID uuid.UUID `json:"workflow_id,omitempty"`
	// FromNodeID holds the value of the "from_node_id" field.
	FromNodeID uuid.UUID `json:"from_node_id,omitempty"`
	// ToNodeID holds the value of the "to_node_id" field.
	ToNodeID uuid.UUID `json:"to_node_id,omitempty"`
	// FromPortID holds the value of the "from_port_id" field.
	FromPortID string `json:"from_port_id,omitempty"`
	// ToPortID holds the value of the "to_port_id" field.
	ToPortID string `json:"to_port_id,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the WorkflowLinkQuery when eager-loading is set.
	Edges        WorkflowLinkEdges `json:"edges"`
	selectValues sql.SelectValues
}

// WorkflowLinkEdges holds the relations/edges for other nodes in the graph.
type WorkflowLinkEdges struct {
	// Workflow holds the value of the workflow edge.
	Workflow *Workflow `json:"workflow,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// WorkflowOrErr returns the Workflow value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e WorkflowLinkEdges) WorkflowOrErr() (*Workflow, error) {
	if e.Workflow != nil {
		return e.Workflow, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: workflow.Label}
	}
	return nil, &NotLoadedError{edge: "workflow"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*WorkflowLink) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case workflowlink.FieldFromPortID, workflowlink.FieldToPortID, workflowlink.FieldType:
			values[i] = new(sql.NullString)
		case workflowlink.FieldCreatedAt, workflowlink.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case workflowlink.FieldID, workflowlink.FieldWorkflowID, workflowlink.FieldFromNodeID, workflowlink.FieldToNodeID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the WorkflowLink fields.
func (wl *WorkflowLink) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case workflowlink.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				wl.ID = *value
			}
		case workflowlink.FieldWorkflowID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field workflow_id", values[i])
			} else if value != nil {
				wl.WorkflowID = *value
			}
		case workflowlink.FieldFromNodeID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field from_node_id", values[i])
			} else if value != nil {
				wl.FromNodeID = *value
			}
		case workflowlink.FieldToNodeID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field to_node_id", values[i])
			} else if value != nil {
				wl.ToNodeID = *value
			}
		case workflowlink.FieldFromPortID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field from_port_id", values[i])
			} else if value.Valid {
				wl.FromPortID = value.String
			}
		case workflowlink.FieldToPortID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field to_port_id", values[i])
			} else if value.Valid {
				wl.ToPortID = value.String
			}
		case workflowlink.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				wl.Type = value.String
			}
		case workflowlink.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				wl.CreatedAt = value.Time
			}
		case workflowlink.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				wl.UpdatedAt = value.Time
			}
		default:
			wl.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the WorkflowLink.
// This includes values selected through modifiers, order, etc.
func (wl *WorkflowLink) Value(name string) (ent.Value, error) {
	return wl.selectValues.Get(name)
}

// QueryWorkflow queries the "workflow" edge of the WorkflowLink entity.
func (wl *WorkflowLink) QueryWorkflow() *WorkflowQuery {
	return NewWorkflowLinkClient(wl.config).QueryWorkflow(wl)
}

// Update returns a builder for updating this WorkflowLink.
// Note that you need to call WorkflowLink.Unwrap() before calling this method if this WorkflowLink
// was returned from a transaction, and the transaction was committed or rolled back.
func (wl *WorkflowLink) Update() *WorkflowLinkUpdateOne {
	return NewWorkflowLinkClient(wl.config).UpdateOne(wl)
}

// Unwrap unwraps the WorkflowLink entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (wl *WorkflowLink) Unwrap() *WorkflowLink {
	_tx, ok := wl.config.driver.(*txDriver)
	if !ok {
		panic("ent: WorkflowLink is not a transactional entity")
	}
	wl.config.driver = _tx.drv
	return wl
}

// String implements the fmt.Stringer.
func (wl *WorkflowLink) String() string {
	var builder strings.Builder
	builder.WriteString("WorkflowLink(")
	builder.WriteString(fmt.Sprintf("id=%v, ", wl.ID))
	builder.WriteString("workflow_id=")
	builder.WriteString(fmt.Sprintf("%v", wl.WorkflowID))
	builder.WriteString(", ")
	builder.WriteString("from_node_id=")
	builder.WriteString(fmt.Sprintf("%v", wl.FromNodeID))
	builder.WriteString(", ")
	builder.WriteString("to_node_id=")
	builder.WriteString(fmt.Sprintf("%v", wl.ToNodeID))
	builder.WriteString(", ")
	builder.WriteString("from_port_id=")
	builder.WriteString(wl.FromPortID)
	builder.WriteString(", ")
	builder.WriteString("to_port_id=")
	builder.WriteString(wl.ToPortID)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(wl.Type)
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(wl.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(wl.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// WorkflowLinks is a parsable slice of WorkflowLink.
type WorkflowLinks []*WorkflowLink
