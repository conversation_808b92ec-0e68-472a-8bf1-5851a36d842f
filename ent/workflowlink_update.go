// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"resflow/ent/predicate"
	"resflow/ent/workflow"
	"resflow/ent/workflowlink"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowLinkUpdate is the builder for updating WorkflowLink entities.
type WorkflowLinkUpdate struct {
	config
	hooks    []Hook
	mutation *WorkflowLinkMutation
}

// Where appends a list predicates to the WorkflowLinkUpdate builder.
func (wlu *WorkflowLinkUpdate) Where(ps ...predicate.WorkflowLink) *WorkflowLinkUpdate {
	wlu.mutation.Where(ps...)
	return wlu
}

// SetWorkflowID sets the "workflow_id" field.
func (wlu *WorkflowLinkUpdate) SetWorkflowID(u uuid.UUID) *WorkflowLinkUpdate {
	wlu.mutation.SetWorkflowID(u)
	return wlu
}

// SetNillableWorkflowID sets the "workflow_id" field if the given value is not nil.
func (wlu *WorkflowLinkUpdate) SetNillableWorkflowID(u *uuid.UUID) *WorkflowLinkUpdate {
	if u != nil {
		wlu.SetWorkflowID(*u)
	}
	return wlu
}

// SetFromNodeID sets the "from_node_id" field.
func (wlu *WorkflowLinkUpdate) SetFromNodeID(u uuid.UUID) *WorkflowLinkUpdate {
	wlu.mutation.SetFromNodeID(u)
	return wlu
}

// SetNillableFromNodeID sets the "from_node_id" field if the given value is not nil.
func (wlu *WorkflowLinkUpdate) SetNillableFromNodeID(u *uuid.UUID) *WorkflowLinkUpdate {
	if u != nil {
		wlu.SetFromNodeID(*u)
	}
	return wlu
}

// SetToNodeID sets the "to_node_id" field.
func (wlu *WorkflowLinkUpdate) SetToNodeID(u uuid.UUID) *WorkflowLinkUpdate {
	wlu.mutation.SetToNodeID(u)
	return wlu
}

// SetNillableToNodeID sets the "to_node_id" field if the given value is not nil.
func (wlu *WorkflowLinkUpdate) SetNillableToNodeID(u *uuid.UUID) *WorkflowLinkUpdate {
	if u != nil {
		wlu.SetToNodeID(*u)
	}
	return wlu
}

// SetFromPortID sets the "from_port_id" field.
func (wlu *WorkflowLinkUpdate) SetFromPortID(s string) *WorkflowLinkUpdate {
	wlu.mutation.SetFromPortID(s)
	return wlu
}

// SetNillableFromPortID sets the "from_port_id" field if the given value is not nil.
func (wlu *WorkflowLinkUpdate) SetNillableFromPortID(s *string) *WorkflowLinkUpdate {
	if s != nil {
		wlu.SetFromPortID(*s)
	}
	return wlu
}

// SetToPortID sets the "to_port_id" field.
func (wlu *WorkflowLinkUpdate) SetToPortID(s string) *WorkflowLinkUpdate {
	wlu.mutation.SetToPortID(s)
	return wlu
}

// SetNillableToPortID sets the "to_port_id" field if the given value is not nil.
func (wlu *WorkflowLinkUpdate) SetNillableToPortID(s *string) *WorkflowLinkUpdate {
	if s != nil {
		wlu.SetToPortID(*s)
	}
	return wlu
}

// SetType sets the "type" field.
func (wlu *WorkflowLinkUpdate) SetType(s string) *WorkflowLinkUpdate {
	wlu.mutation.SetType(s)
	return wlu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (wlu *WorkflowLinkUpdate) SetNillableType(s *string) *WorkflowLinkUpdate {
	if s != nil {
		wlu.SetType(*s)
	}
	return wlu
}

// SetUpdatedAt sets the "updated_at" field.
func (wlu *WorkflowLinkUpdate) SetUpdatedAt(t time.Time) *WorkflowLinkUpdate {
	wlu.mutation.SetUpdatedAt(t)
	return wlu
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wlu *WorkflowLinkUpdate) SetNillableUpdatedAt(t *time.Time) *WorkflowLinkUpdate {
	if t != nil {
		wlu.SetUpdatedAt(*t)
	}
	return wlu
}

// SetWorkflow sets the "workflow" edge to the Workflow entity.
func (wlu *WorkflowLinkUpdate) SetWorkflow(w *Workflow) *WorkflowLinkUpdate {
	return wlu.SetWorkflowID(w.ID)
}

// Mutation returns the WorkflowLinkMutation object of the builder.
func (wlu *WorkflowLinkUpdate) Mutation() *WorkflowLinkMutation {
	return wlu.mutation
}

// ClearWorkflow clears the "workflow" edge to the Workflow entity.
func (wlu *WorkflowLinkUpdate) ClearWorkflow() *WorkflowLinkUpdate {
	wlu.mutation.ClearWorkflow()
	return wlu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (wlu *WorkflowLinkUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, wlu.sqlSave, wlu.mutation, wlu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wlu *WorkflowLinkUpdate) SaveX(ctx context.Context) int {
	affected, err := wlu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (wlu *WorkflowLinkUpdate) Exec(ctx context.Context) error {
	_, err := wlu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wlu *WorkflowLinkUpdate) ExecX(ctx context.Context) {
	if err := wlu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wlu *WorkflowLinkUpdate) check() error {
	if wlu.mutation.WorkflowCleared() && len(wlu.mutation.WorkflowIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "WorkflowLink.workflow"`)
	}
	return nil
}

func (wlu *WorkflowLinkUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := wlu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(workflowlink.Table, workflowlink.Columns, sqlgraph.NewFieldSpec(workflowlink.FieldID, field.TypeUUID))
	if ps := wlu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wlu.mutation.FromNodeID(); ok {
		_spec.SetField(workflowlink.FieldFromNodeID, field.TypeUUID, value)
	}
	if value, ok := wlu.mutation.ToNodeID(); ok {
		_spec.SetField(workflowlink.FieldToNodeID, field.TypeUUID, value)
	}
	if value, ok := wlu.mutation.FromPortID(); ok {
		_spec.SetField(workflowlink.FieldFromPortID, field.TypeString, value)
	}
	if value, ok := wlu.mutation.ToPortID(); ok {
		_spec.SetField(workflowlink.FieldToPortID, field.TypeString, value)
	}
	if value, ok := wlu.mutation.GetType(); ok {
		_spec.SetField(workflowlink.FieldType, field.TypeString, value)
	}
	if value, ok := wlu.mutation.UpdatedAt(); ok {
		_spec.SetField(workflowlink.FieldUpdatedAt, field.TypeTime, value)
	}
	if wlu.mutation.WorkflowCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflowlink.WorkflowTable,
			Columns: []string{workflowlink.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wlu.mutation.WorkflowIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflowlink.WorkflowTable,
			Columns: []string{workflowlink.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, wlu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workflowlink.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	wlu.mutation.done = true
	return n, nil
}

// WorkflowLinkUpdateOne is the builder for updating a single WorkflowLink entity.
type WorkflowLinkUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *WorkflowLinkMutation
}

// SetWorkflowID sets the "workflow_id" field.
func (wluo *WorkflowLinkUpdateOne) SetWorkflowID(u uuid.UUID) *WorkflowLinkUpdateOne {
	wluo.mutation.SetWorkflowID(u)
	return wluo
}

// SetNillableWorkflowID sets the "workflow_id" field if the given value is not nil.
func (wluo *WorkflowLinkUpdateOne) SetNillableWorkflowID(u *uuid.UUID) *WorkflowLinkUpdateOne {
	if u != nil {
		wluo.SetWorkflowID(*u)
	}
	return wluo
}

// SetFromNodeID sets the "from_node_id" field.
func (wluo *WorkflowLinkUpdateOne) SetFromNodeID(u uuid.UUID) *WorkflowLinkUpdateOne {
	wluo.mutation.SetFromNodeID(u)
	return wluo
}

// SetNillableFromNodeID sets the "from_node_id" field if the given value is not nil.
func (wluo *WorkflowLinkUpdateOne) SetNillableFromNodeID(u *uuid.UUID) *WorkflowLinkUpdateOne {
	if u != nil {
		wluo.SetFromNodeID(*u)
	}
	return wluo
}

// SetToNodeID sets the "to_node_id" field.
func (wluo *WorkflowLinkUpdateOne) SetToNodeID(u uuid.UUID) *WorkflowLinkUpdateOne {
	wluo.mutation.SetToNodeID(u)
	return wluo
}

// SetNillableToNodeID sets the "to_node_id" field if the given value is not nil.
func (wluo *WorkflowLinkUpdateOne) SetNillableToNodeID(u *uuid.UUID) *WorkflowLinkUpdateOne {
	if u != nil {
		wluo.SetToNodeID(*u)
	}
	return wluo
}

// SetFromPortID sets the "from_port_id" field.
func (wluo *WorkflowLinkUpdateOne) SetFromPortID(s string) *WorkflowLinkUpdateOne {
	wluo.mutation.SetFromPortID(s)
	return wluo
}

// SetNillableFromPortID sets the "from_port_id" field if the given value is not nil.
func (wluo *WorkflowLinkUpdateOne) SetNillableFromPortID(s *string) *WorkflowLinkUpdateOne {
	if s != nil {
		wluo.SetFromPortID(*s)
	}
	return wluo
}

// SetToPortID sets the "to_port_id" field.
func (wluo *WorkflowLinkUpdateOne) SetToPortID(s string) *WorkflowLinkUpdateOne {
	wluo.mutation.SetToPortID(s)
	return wluo
}

// SetNillableToPortID sets the "to_port_id" field if the given value is not nil.
func (wluo *WorkflowLinkUpdateOne) SetNillableToPortID(s *string) *WorkflowLinkUpdateOne {
	if s != nil {
		wluo.SetToPortID(*s)
	}
	return wluo
}

// SetType sets the "type" field.
func (wluo *WorkflowLinkUpdateOne) SetType(s string) *WorkflowLinkUpdateOne {
	wluo.mutation.SetType(s)
	return wluo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (wluo *WorkflowLinkUpdateOne) SetNillableType(s *string) *WorkflowLinkUpdateOne {
	if s != nil {
		wluo.SetType(*s)
	}
	return wluo
}

// SetUpdatedAt sets the "updated_at" field.
func (wluo *WorkflowLinkUpdateOne) SetUpdatedAt(t time.Time) *WorkflowLinkUpdateOne {
	wluo.mutation.SetUpdatedAt(t)
	return wluo
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wluo *WorkflowLinkUpdateOne) SetNillableUpdatedAt(t *time.Time) *WorkflowLinkUpdateOne {
	if t != nil {
		wluo.SetUpdatedAt(*t)
	}
	return wluo
}

// SetWorkflow sets the "workflow" edge to the Workflow entity.
func (wluo *WorkflowLinkUpdateOne) SetWorkflow(w *Workflow) *WorkflowLinkUpdateOne {
	return wluo.SetWorkflowID(w.ID)
}

// Mutation returns the WorkflowLinkMutation object of the builder.
func (wluo *WorkflowLinkUpdateOne) Mutation() *WorkflowLinkMutation {
	return wluo.mutation
}

// ClearWorkflow clears the "workflow" edge to the Workflow entity.
func (wluo *WorkflowLinkUpdateOne) ClearWorkflow() *WorkflowLinkUpdateOne {
	wluo.mutation.ClearWorkflow()
	return wluo
}

// Where appends a list predicates to the WorkflowLinkUpdate builder.
func (wluo *WorkflowLinkUpdateOne) Where(ps ...predicate.WorkflowLink) *WorkflowLinkUpdateOne {
	wluo.mutation.Where(ps...)
	return wluo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (wluo *WorkflowLinkUpdateOne) Select(field string, fields ...string) *WorkflowLinkUpdateOne {
	wluo.fields = append([]string{field}, fields...)
	return wluo
}

// Save executes the query and returns the updated WorkflowLink entity.
func (wluo *WorkflowLinkUpdateOne) Save(ctx context.Context) (*WorkflowLink, error) {
	return withHooks(ctx, wluo.sqlSave, wluo.mutation, wluo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wluo *WorkflowLinkUpdateOne) SaveX(ctx context.Context) *WorkflowLink {
	node, err := wluo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (wluo *WorkflowLinkUpdateOne) Exec(ctx context.Context) error {
	_, err := wluo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wluo *WorkflowLinkUpdateOne) ExecX(ctx context.Context) {
	if err := wluo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wluo *WorkflowLinkUpdateOne) check() error {
	if wluo.mutation.WorkflowCleared() && len(wluo.mutation.WorkflowIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "WorkflowLink.workflow"`)
	}
	return nil
}

func (wluo *WorkflowLinkUpdateOne) sqlSave(ctx context.Context) (_node *WorkflowLink, err error) {
	if err := wluo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(workflowlink.Table, workflowlink.Columns, sqlgraph.NewFieldSpec(workflowlink.FieldID, field.TypeUUID))
	id, ok := wluo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "WorkflowLink.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := wluo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, workflowlink.FieldID)
		for _, f := range fields {
			if !workflowlink.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != workflowlink.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := wluo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wluo.mutation.FromNodeID(); ok {
		_spec.SetField(workflowlink.FieldFromNodeID, field.TypeUUID, value)
	}
	if value, ok := wluo.mutation.ToNodeID(); ok {
		_spec.SetField(workflowlink.FieldToNodeID, field.TypeUUID, value)
	}
	if value, ok := wluo.mutation.FromPortID(); ok {
		_spec.SetField(workflowlink.FieldFromPortID, field.TypeString, value)
	}
	if value, ok := wluo.mutation.ToPortID(); ok {
		_spec.SetField(workflowlink.FieldToPortID, field.TypeString, value)
	}
	if value, ok := wluo.mutation.GetType(); ok {
		_spec.SetField(workflowlink.FieldType, field.TypeString, value)
	}
	if value, ok := wluo.mutation.UpdatedAt(); ok {
		_spec.SetField(workflowlink.FieldUpdatedAt, field.TypeTime, value)
	}
	if wluo.mutation.WorkflowCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflowlink.WorkflowTable,
			Columns: []string{workflowlink.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wluo.mutation.WorkflowIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflowlink.WorkflowTable,
			Columns: []string{workflowlink.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &WorkflowLink{config: wluo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, wluo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workflowlink.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	wluo.mutation.done = true
	return _node, nil
}
