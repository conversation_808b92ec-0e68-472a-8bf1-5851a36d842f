// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"resflow/ent/workflow"
	"resflow/ent/workflowlink"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowLinkCreate is the builder for creating a WorkflowLink entity.
type WorkflowLinkCreate struct {
	config
	mutation *WorkflowLinkMutation
	hooks    []Hook
}

// SetWorkflowID sets the "workflow_id" field.
func (wlc *WorkflowLinkCreate) SetWorkflowID(u uuid.UUID) *WorkflowLinkCreate {
	wlc.mutation.SetWorkflowID(u)
	return wlc
}

// SetFromNodeID sets the "from_node_id" field.
func (wlc *WorkflowLinkCreate) SetFromNodeID(u uuid.UUID) *WorkflowLinkCreate {
	wlc.mutation.SetFromNodeID(u)
	return wlc
}

// SetToNodeID sets the "to_node_id" field.
func (wlc *WorkflowLinkCreate) SetToNodeID(u uuid.UUID) *WorkflowLinkCreate {
	wlc.mutation.SetToNodeID(u)
	return wlc
}

// SetFromPortID sets the "from_port_id" field.
func (wlc *WorkflowLinkCreate) SetFromPortID(s string) *WorkflowLinkCreate {
	wlc.mutation.SetFromPortID(s)
	return wlc
}

// SetToPortID sets the "to_port_id" field.
func (wlc *WorkflowLinkCreate) SetToPortID(s string) *WorkflowLinkCreate {
	wlc.mutation.SetToPortID(s)
	return wlc
}

// SetType sets the "type" field.
func (wlc *WorkflowLinkCreate) SetType(s string) *WorkflowLinkCreate {
	wlc.mutation.SetType(s)
	return wlc
}

// SetCreatedAt sets the "created_at" field.
func (wlc *WorkflowLinkCreate) SetCreatedAt(t time.Time) *WorkflowLinkCreate {
	wlc.mutation.SetCreatedAt(t)
	return wlc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (wlc *WorkflowLinkCreate) SetNillableCreatedAt(t *time.Time) *WorkflowLinkCreate {
	if t != nil {
		wlc.SetCreatedAt(*t)
	}
	return wlc
}

// SetUpdatedAt sets the "updated_at" field.
func (wlc *WorkflowLinkCreate) SetUpdatedAt(t time.Time) *WorkflowLinkCreate {
	wlc.mutation.SetUpdatedAt(t)
	return wlc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wlc *WorkflowLinkCreate) SetNillableUpdatedAt(t *time.Time) *WorkflowLinkCreate {
	if t != nil {
		wlc.SetUpdatedAt(*t)
	}
	return wlc
}

// SetID sets the "id" field.
func (wlc *WorkflowLinkCreate) SetID(u uuid.UUID) *WorkflowLinkCreate {
	wlc.mutation.SetID(u)
	return wlc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (wlc *WorkflowLinkCreate) SetNillableID(u *uuid.UUID) *WorkflowLinkCreate {
	if u != nil {
		wlc.SetID(*u)
	}
	return wlc
}

// SetWorkflow sets the "workflow" edge to the Workflow entity.
func (wlc *WorkflowLinkCreate) SetWorkflow(w *Workflow) *WorkflowLinkCreate {
	return wlc.SetWorkflowID(w.ID)
}

// Mutation returns the WorkflowLinkMutation object of the builder.
func (wlc *WorkflowLinkCreate) Mutation() *WorkflowLinkMutation {
	return wlc.mutation
}

// Save creates the WorkflowLink in the database.
func (wlc *WorkflowLinkCreate) Save(ctx context.Context) (*WorkflowLink, error) {
	wlc.defaults()
	return withHooks(ctx, wlc.sqlSave, wlc.mutation, wlc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (wlc *WorkflowLinkCreate) SaveX(ctx context.Context) *WorkflowLink {
	v, err := wlc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wlc *WorkflowLinkCreate) Exec(ctx context.Context) error {
	_, err := wlc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wlc *WorkflowLinkCreate) ExecX(ctx context.Context) {
	if err := wlc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wlc *WorkflowLinkCreate) defaults() {
	if _, ok := wlc.mutation.CreatedAt(); !ok {
		v := workflowlink.DefaultCreatedAt()
		wlc.mutation.SetCreatedAt(v)
	}
	if _, ok := wlc.mutation.UpdatedAt(); !ok {
		v := workflowlink.DefaultUpdatedAt()
		wlc.mutation.SetUpdatedAt(v)
	}
	if _, ok := wlc.mutation.ID(); !ok {
		v := workflowlink.DefaultID()
		wlc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wlc *WorkflowLinkCreate) check() error {
	if _, ok := wlc.mutation.WorkflowID(); !ok {
		return &ValidationError{Name: "workflow_id", err: errors.New(`ent: missing required field "WorkflowLink.workflow_id"`)}
	}
	if _, ok := wlc.mutation.FromNodeID(); !ok {
		return &ValidationError{Name: "from_node_id", err: errors.New(`ent: missing required field "WorkflowLink.from_node_id"`)}
	}
	if _, ok := wlc.mutation.ToNodeID(); !ok {
		return &ValidationError{Name: "to_node_id", err: errors.New(`ent: missing required field "WorkflowLink.to_node_id"`)}
	}
	if _, ok := wlc.mutation.FromPortID(); !ok {
		return &ValidationError{Name: "from_port_id", err: errors.New(`ent: missing required field "WorkflowLink.from_port_id"`)}
	}
	if _, ok := wlc.mutation.ToPortID(); !ok {
		return &ValidationError{Name: "to_port_id", err: errors.New(`ent: missing required field "WorkflowLink.to_port_id"`)}
	}
	if _, ok := wlc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "WorkflowLink.type"`)}
	}
	if _, ok := wlc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "WorkflowLink.created_at"`)}
	}
	if _, ok := wlc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "WorkflowLink.updated_at"`)}
	}
	if len(wlc.mutation.WorkflowIDs()) == 0 {
		return &ValidationError{Name: "workflow", err: errors.New(`ent: missing required edge "WorkflowLink.workflow"`)}
	}
	return nil
}

func (wlc *WorkflowLinkCreate) sqlSave(ctx context.Context) (*WorkflowLink, error) {
	if err := wlc.check(); err != nil {
		return nil, err
	}
	_node, _spec := wlc.createSpec()
	if err := sqlgraph.CreateNode(ctx, wlc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	wlc.mutation.id = &_node.ID
	wlc.mutation.done = true
	return _node, nil
}

func (wlc *WorkflowLinkCreate) createSpec() (*WorkflowLink, *sqlgraph.CreateSpec) {
	var (
		_node = &WorkflowLink{config: wlc.config}
		_spec = sqlgraph.NewCreateSpec(workflowlink.Table, sqlgraph.NewFieldSpec(workflowlink.FieldID, field.TypeUUID))
	)
	if id, ok := wlc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := wlc.mutation.FromNodeID(); ok {
		_spec.SetField(workflowlink.FieldFromNodeID, field.TypeUUID, value)
		_node.FromNodeID = value
	}
	if value, ok := wlc.mutation.ToNodeID(); ok {
		_spec.SetField(workflowlink.FieldToNodeID, field.TypeUUID, value)
		_node.ToNodeID = value
	}
	if value, ok := wlc.mutation.FromPortID(); ok {
		_spec.SetField(workflowlink.FieldFromPortID, field.TypeString, value)
		_node.FromPortID = value
	}
	if value, ok := wlc.mutation.ToPortID(); ok {
		_spec.SetField(workflowlink.FieldToPortID, field.TypeString, value)
		_node.ToPortID = value
	}
	if value, ok := wlc.mutation.GetType(); ok {
		_spec.SetField(workflowlink.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := wlc.mutation.CreatedAt(); ok {
		_spec.SetField(workflowlink.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := wlc.mutation.UpdatedAt(); ok {
		_spec.SetField(workflowlink.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if nodes := wlc.mutation.WorkflowIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflowlink.WorkflowTable,
			Columns: []string{workflowlink.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.WorkflowID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// WorkflowLinkCreateBulk is the builder for creating many WorkflowLink entities in bulk.
type WorkflowLinkCreateBulk struct {
	config
	err      error
	builders []*WorkflowLinkCreate
}

// Save creates the WorkflowLink entities in the database.
func (wlcb *WorkflowLinkCreateBulk) Save(ctx context.Context) ([]*WorkflowLink, error) {
	if wlcb.err != nil {
		return nil, wlcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(wlcb.builders))
	nodes := make([]*WorkflowLink, len(wlcb.builders))
	mutators := make([]Mutator, len(wlcb.builders))
	for i := range wlcb.builders {
		func(i int, root context.Context) {
			builder := wlcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*WorkflowLinkMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, wlcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, wlcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, wlcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (wlcb *WorkflowLinkCreateBulk) SaveX(ctx context.Context) []*WorkflowLink {
	v, err := wlcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wlcb *WorkflowLinkCreateBulk) Exec(ctx context.Context) error {
	_, err := wlcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wlcb *WorkflowLinkCreateBulk) ExecX(ctx context.Context) {
	if err := wlcb.Exec(ctx); err != nil {
		panic(err)
	}
}
