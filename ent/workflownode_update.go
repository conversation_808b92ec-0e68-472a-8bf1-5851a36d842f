// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"resflow/ent/predicate"
	"resflow/ent/workflow"
	"resflow/ent/workflownode"
	v1 "resflow/proto/generated_go/v1"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowNodeUpdate is the builder for updating WorkflowNode entities.
type WorkflowNodeUpdate struct {
	config
	hooks    []Hook
	mutation *WorkflowNodeMutation
}

// Where appends a list predicates to the WorkflowNodeUpdate builder.
func (wnu *WorkflowNodeUpdate) Where(ps ...predicate.WorkflowNode) *WorkflowNodeUpdate {
	wnu.mutation.Where(ps...)
	return wnu
}

// SetWorkflowID sets the "workflow_id" field.
func (wnu *WorkflowNodeUpdate) SetWorkflowID(u uuid.UUID) *WorkflowNodeUpdate {
	wnu.mutation.SetWorkflowID(u)
	return wnu
}

// SetNillableWorkflowID sets the "workflow_id" field if the given value is not nil.
func (wnu *WorkflowNodeUpdate) SetNillableWorkflowID(u *uuid.UUID) *WorkflowNodeUpdate {
	if u != nil {
		wnu.SetWorkflowID(*u)
	}
	return wnu
}

// SetName sets the "name" field.
func (wnu *WorkflowNodeUpdate) SetName(s string) *WorkflowNodeUpdate {
	wnu.mutation.SetName(s)
	return wnu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (wnu *WorkflowNodeUpdate) SetNillableName(s *string) *WorkflowNodeUpdate {
	if s != nil {
		wnu.SetName(*s)
	}
	return wnu
}

// SetDescription sets the "description" field.
func (wnu *WorkflowNodeUpdate) SetDescription(s string) *WorkflowNodeUpdate {
	wnu.mutation.SetDescription(s)
	return wnu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (wnu *WorkflowNodeUpdate) SetNillableDescription(s *string) *WorkflowNodeUpdate {
	if s != nil {
		wnu.SetDescription(*s)
	}
	return wnu
}

// SetIcon sets the "icon" field.
func (wnu *WorkflowNodeUpdate) SetIcon(s string) *WorkflowNodeUpdate {
	wnu.mutation.SetIcon(s)
	return wnu
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (wnu *WorkflowNodeUpdate) SetNillableIcon(s *string) *WorkflowNodeUpdate {
	if s != nil {
		wnu.SetIcon(*s)
	}
	return wnu
}

// SetType sets the "type" field.
func (wnu *WorkflowNodeUpdate) SetType(s string) *WorkflowNodeUpdate {
	wnu.mutation.SetType(s)
	return wnu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (wnu *WorkflowNodeUpdate) SetNillableType(s *string) *WorkflowNodeUpdate {
	if s != nil {
		wnu.SetType(*s)
	}
	return wnu
}

// SetVersion sets the "version" field.
func (wnu *WorkflowNodeUpdate) SetVersion(s string) *WorkflowNodeUpdate {
	wnu.mutation.SetVersion(s)
	return wnu
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (wnu *WorkflowNodeUpdate) SetNillableVersion(s *string) *WorkflowNodeUpdate {
	if s != nil {
		wnu.SetVersion(*s)
	}
	return wnu
}

// SetPluginName sets the "plugin_name" field.
func (wnu *WorkflowNodeUpdate) SetPluginName(s string) *WorkflowNodeUpdate {
	wnu.mutation.SetPluginName(s)
	return wnu
}

// SetNillablePluginName sets the "plugin_name" field if the given value is not nil.
func (wnu *WorkflowNodeUpdate) SetNillablePluginName(s *string) *WorkflowNodeUpdate {
	if s != nil {
		wnu.SetPluginName(*s)
	}
	return wnu
}

// SetPluginVersion sets the "plugin_version" field.
func (wnu *WorkflowNodeUpdate) SetPluginVersion(s string) *WorkflowNodeUpdate {
	wnu.mutation.SetPluginVersion(s)
	return wnu
}

// SetNillablePluginVersion sets the "plugin_version" field if the given value is not nil.
func (wnu *WorkflowNodeUpdate) SetNillablePluginVersion(s *string) *WorkflowNodeUpdate {
	if s != nil {
		wnu.SetPluginVersion(*s)
	}
	return wnu
}

// SetInputParams sets the "input_params" field.
func (wnu *WorkflowNodeUpdate) SetInputParams(vp []*v1.NodeParam) *WorkflowNodeUpdate {
	wnu.mutation.SetInputParams(vp)
	return wnu
}

// AppendInputParams appends vp to the "input_params" field.
func (wnu *WorkflowNodeUpdate) AppendInputParams(vp []*v1.NodeParam) *WorkflowNodeUpdate {
	wnu.mutation.AppendInputParams(vp)
	return wnu
}

// SetInputValues sets the "input_values" field.
func (wnu *WorkflowNodeUpdate) SetInputValues(jm json.RawMessage) *WorkflowNodeUpdate {
	wnu.mutation.SetInputValues(jm)
	return wnu
}

// AppendInputValues appends jm to the "input_values" field.
func (wnu *WorkflowNodeUpdate) AppendInputValues(jm json.RawMessage) *WorkflowNodeUpdate {
	wnu.mutation.AppendInputValues(jm)
	return wnu
}

// SetOutputParams sets the "output_params" field.
func (wnu *WorkflowNodeUpdate) SetOutputParams(vp []*v1.NodeParam) *WorkflowNodeUpdate {
	wnu.mutation.SetOutputParams(vp)
	return wnu
}

// AppendOutputParams appends vp to the "output_params" field.
func (wnu *WorkflowNodeUpdate) AppendOutputParams(vp []*v1.NodeParam) *WorkflowNodeUpdate {
	wnu.mutation.AppendOutputParams(vp)
	return wnu
}

// SetOutputValues sets the "output_values" field.
func (wnu *WorkflowNodeUpdate) SetOutputValues(jm json.RawMessage) *WorkflowNodeUpdate {
	wnu.mutation.SetOutputValues(jm)
	return wnu
}

// AppendOutputValues appends jm to the "output_values" field.
func (wnu *WorkflowNodeUpdate) AppendOutputValues(jm json.RawMessage) *WorkflowNodeUpdate {
	wnu.mutation.AppendOutputValues(jm)
	return wnu
}

// SetInputPorts sets the "input_ports" field.
func (wnu *WorkflowNodeUpdate) SetInputPorts(vp []*v1.NodePort) *WorkflowNodeUpdate {
	wnu.mutation.SetInputPorts(vp)
	return wnu
}

// AppendInputPorts appends vp to the "input_ports" field.
func (wnu *WorkflowNodeUpdate) AppendInputPorts(vp []*v1.NodePort) *WorkflowNodeUpdate {
	wnu.mutation.AppendInputPorts(vp)
	return wnu
}

// SetOutputPorts sets the "output_ports" field.
func (wnu *WorkflowNodeUpdate) SetOutputPorts(vp []*v1.NodePort) *WorkflowNodeUpdate {
	wnu.mutation.SetOutputPorts(vp)
	return wnu
}

// AppendOutputPorts appends vp to the "output_ports" field.
func (wnu *WorkflowNodeUpdate) AppendOutputPorts(vp []*v1.NodePort) *WorkflowNodeUpdate {
	wnu.mutation.AppendOutputPorts(vp)
	return wnu
}

// SetPosition sets the "position" field.
func (wnu *WorkflowNodeUpdate) SetPosition(vnp *v1.WorkflowNodePosition) *WorkflowNodeUpdate {
	wnu.mutation.SetPosition(vnp)
	return wnu
}

// SetData sets the "data" field.
func (wnu *WorkflowNodeUpdate) SetData(jm json.RawMessage) *WorkflowNodeUpdate {
	wnu.mutation.SetData(jm)
	return wnu
}

// AppendData appends jm to the "data" field.
func (wnu *WorkflowNodeUpdate) AppendData(jm json.RawMessage) *WorkflowNodeUpdate {
	wnu.mutation.AppendData(jm)
	return wnu
}

// ClearData clears the value of the "data" field.
func (wnu *WorkflowNodeUpdate) ClearData() *WorkflowNodeUpdate {
	wnu.mutation.ClearData()
	return wnu
}

// SetUpdatedAt sets the "updated_at" field.
func (wnu *WorkflowNodeUpdate) SetUpdatedAt(t time.Time) *WorkflowNodeUpdate {
	wnu.mutation.SetUpdatedAt(t)
	return wnu
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wnu *WorkflowNodeUpdate) SetNillableUpdatedAt(t *time.Time) *WorkflowNodeUpdate {
	if t != nil {
		wnu.SetUpdatedAt(*t)
	}
	return wnu
}

// SetWorkflow sets the "workflow" edge to the Workflow entity.
func (wnu *WorkflowNodeUpdate) SetWorkflow(w *Workflow) *WorkflowNodeUpdate {
	return wnu.SetWorkflowID(w.ID)
}

// Mutation returns the WorkflowNodeMutation object of the builder.
func (wnu *WorkflowNodeUpdate) Mutation() *WorkflowNodeMutation {
	return wnu.mutation
}

// ClearWorkflow clears the "workflow" edge to the Workflow entity.
func (wnu *WorkflowNodeUpdate) ClearWorkflow() *WorkflowNodeUpdate {
	wnu.mutation.ClearWorkflow()
	return wnu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (wnu *WorkflowNodeUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, wnu.sqlSave, wnu.mutation, wnu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wnu *WorkflowNodeUpdate) SaveX(ctx context.Context) int {
	affected, err := wnu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (wnu *WorkflowNodeUpdate) Exec(ctx context.Context) error {
	_, err := wnu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wnu *WorkflowNodeUpdate) ExecX(ctx context.Context) {
	if err := wnu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wnu *WorkflowNodeUpdate) check() error {
	if wnu.mutation.WorkflowCleared() && len(wnu.mutation.WorkflowIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "WorkflowNode.workflow"`)
	}
	return nil
}

func (wnu *WorkflowNodeUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := wnu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(workflownode.Table, workflownode.Columns, sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID))
	if ps := wnu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wnu.mutation.Name(); ok {
		_spec.SetField(workflownode.FieldName, field.TypeString, value)
	}
	if value, ok := wnu.mutation.Description(); ok {
		_spec.SetField(workflownode.FieldDescription, field.TypeString, value)
	}
	if value, ok := wnu.mutation.Icon(); ok {
		_spec.SetField(workflownode.FieldIcon, field.TypeString, value)
	}
	if value, ok := wnu.mutation.GetType(); ok {
		_spec.SetField(workflownode.FieldType, field.TypeString, value)
	}
	if value, ok := wnu.mutation.Version(); ok {
		_spec.SetField(workflownode.FieldVersion, field.TypeString, value)
	}
	if value, ok := wnu.mutation.PluginName(); ok {
		_spec.SetField(workflownode.FieldPluginName, field.TypeString, value)
	}
	if value, ok := wnu.mutation.PluginVersion(); ok {
		_spec.SetField(workflownode.FieldPluginVersion, field.TypeString, value)
	}
	if value, ok := wnu.mutation.InputParams(); ok {
		_spec.SetField(workflownode.FieldInputParams, field.TypeJSON, value)
	}
	if value, ok := wnu.mutation.AppendedInputParams(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldInputParams, value)
		})
	}
	if value, ok := wnu.mutation.InputValues(); ok {
		_spec.SetField(workflownode.FieldInputValues, field.TypeJSON, value)
	}
	if value, ok := wnu.mutation.AppendedInputValues(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldInputValues, value)
		})
	}
	if value, ok := wnu.mutation.OutputParams(); ok {
		_spec.SetField(workflownode.FieldOutputParams, field.TypeJSON, value)
	}
	if value, ok := wnu.mutation.AppendedOutputParams(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldOutputParams, value)
		})
	}
	if value, ok := wnu.mutation.OutputValues(); ok {
		_spec.SetField(workflownode.FieldOutputValues, field.TypeJSON, value)
	}
	if value, ok := wnu.mutation.AppendedOutputValues(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldOutputValues, value)
		})
	}
	if value, ok := wnu.mutation.InputPorts(); ok {
		_spec.SetField(workflownode.FieldInputPorts, field.TypeJSON, value)
	}
	if value, ok := wnu.mutation.AppendedInputPorts(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldInputPorts, value)
		})
	}
	if value, ok := wnu.mutation.OutputPorts(); ok {
		_spec.SetField(workflownode.FieldOutputPorts, field.TypeJSON, value)
	}
	if value, ok := wnu.mutation.AppendedOutputPorts(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldOutputPorts, value)
		})
	}
	if value, ok := wnu.mutation.Position(); ok {
		_spec.SetField(workflownode.FieldPosition, field.TypeJSON, value)
	}
	if value, ok := wnu.mutation.Data(); ok {
		_spec.SetField(workflownode.FieldData, field.TypeJSON, value)
	}
	if value, ok := wnu.mutation.AppendedData(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldData, value)
		})
	}
	if wnu.mutation.DataCleared() {
		_spec.ClearField(workflownode.FieldData, field.TypeJSON)
	}
	if value, ok := wnu.mutation.UpdatedAt(); ok {
		_spec.SetField(workflownode.FieldUpdatedAt, field.TypeTime, value)
	}
	if wnu.mutation.WorkflowCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflownode.WorkflowTable,
			Columns: []string{workflownode.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wnu.mutation.WorkflowIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflownode.WorkflowTable,
			Columns: []string{workflownode.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, wnu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workflownode.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	wnu.mutation.done = true
	return n, nil
}

// WorkflowNodeUpdateOne is the builder for updating a single WorkflowNode entity.
type WorkflowNodeUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *WorkflowNodeMutation
}

// SetWorkflowID sets the "workflow_id" field.
func (wnuo *WorkflowNodeUpdateOne) SetWorkflowID(u uuid.UUID) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetWorkflowID(u)
	return wnuo
}

// SetNillableWorkflowID sets the "workflow_id" field if the given value is not nil.
func (wnuo *WorkflowNodeUpdateOne) SetNillableWorkflowID(u *uuid.UUID) *WorkflowNodeUpdateOne {
	if u != nil {
		wnuo.SetWorkflowID(*u)
	}
	return wnuo
}

// SetName sets the "name" field.
func (wnuo *WorkflowNodeUpdateOne) SetName(s string) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetName(s)
	return wnuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (wnuo *WorkflowNodeUpdateOne) SetNillableName(s *string) *WorkflowNodeUpdateOne {
	if s != nil {
		wnuo.SetName(*s)
	}
	return wnuo
}

// SetDescription sets the "description" field.
func (wnuo *WorkflowNodeUpdateOne) SetDescription(s string) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetDescription(s)
	return wnuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (wnuo *WorkflowNodeUpdateOne) SetNillableDescription(s *string) *WorkflowNodeUpdateOne {
	if s != nil {
		wnuo.SetDescription(*s)
	}
	return wnuo
}

// SetIcon sets the "icon" field.
func (wnuo *WorkflowNodeUpdateOne) SetIcon(s string) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetIcon(s)
	return wnuo
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (wnuo *WorkflowNodeUpdateOne) SetNillableIcon(s *string) *WorkflowNodeUpdateOne {
	if s != nil {
		wnuo.SetIcon(*s)
	}
	return wnuo
}

// SetType sets the "type" field.
func (wnuo *WorkflowNodeUpdateOne) SetType(s string) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetType(s)
	return wnuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (wnuo *WorkflowNodeUpdateOne) SetNillableType(s *string) *WorkflowNodeUpdateOne {
	if s != nil {
		wnuo.SetType(*s)
	}
	return wnuo
}

// SetVersion sets the "version" field.
func (wnuo *WorkflowNodeUpdateOne) SetVersion(s string) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetVersion(s)
	return wnuo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (wnuo *WorkflowNodeUpdateOne) SetNillableVersion(s *string) *WorkflowNodeUpdateOne {
	if s != nil {
		wnuo.SetVersion(*s)
	}
	return wnuo
}

// SetPluginName sets the "plugin_name" field.
func (wnuo *WorkflowNodeUpdateOne) SetPluginName(s string) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetPluginName(s)
	return wnuo
}

// SetNillablePluginName sets the "plugin_name" field if the given value is not nil.
func (wnuo *WorkflowNodeUpdateOne) SetNillablePluginName(s *string) *WorkflowNodeUpdateOne {
	if s != nil {
		wnuo.SetPluginName(*s)
	}
	return wnuo
}

// SetPluginVersion sets the "plugin_version" field.
func (wnuo *WorkflowNodeUpdateOne) SetPluginVersion(s string) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetPluginVersion(s)
	return wnuo
}

// SetNillablePluginVersion sets the "plugin_version" field if the given value is not nil.
func (wnuo *WorkflowNodeUpdateOne) SetNillablePluginVersion(s *string) *WorkflowNodeUpdateOne {
	if s != nil {
		wnuo.SetPluginVersion(*s)
	}
	return wnuo
}

// SetInputParams sets the "input_params" field.
func (wnuo *WorkflowNodeUpdateOne) SetInputParams(vp []*v1.NodeParam) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetInputParams(vp)
	return wnuo
}

// AppendInputParams appends vp to the "input_params" field.
func (wnuo *WorkflowNodeUpdateOne) AppendInputParams(vp []*v1.NodeParam) *WorkflowNodeUpdateOne {
	wnuo.mutation.AppendInputParams(vp)
	return wnuo
}

// SetInputValues sets the "input_values" field.
func (wnuo *WorkflowNodeUpdateOne) SetInputValues(jm json.RawMessage) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetInputValues(jm)
	return wnuo
}

// AppendInputValues appends jm to the "input_values" field.
func (wnuo *WorkflowNodeUpdateOne) AppendInputValues(jm json.RawMessage) *WorkflowNodeUpdateOne {
	wnuo.mutation.AppendInputValues(jm)
	return wnuo
}

// SetOutputParams sets the "output_params" field.
func (wnuo *WorkflowNodeUpdateOne) SetOutputParams(vp []*v1.NodeParam) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetOutputParams(vp)
	return wnuo
}

// AppendOutputParams appends vp to the "output_params" field.
func (wnuo *WorkflowNodeUpdateOne) AppendOutputParams(vp []*v1.NodeParam) *WorkflowNodeUpdateOne {
	wnuo.mutation.AppendOutputParams(vp)
	return wnuo
}

// SetOutputValues sets the "output_values" field.
func (wnuo *WorkflowNodeUpdateOne) SetOutputValues(jm json.RawMessage) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetOutputValues(jm)
	return wnuo
}

// AppendOutputValues appends jm to the "output_values" field.
func (wnuo *WorkflowNodeUpdateOne) AppendOutputValues(jm json.RawMessage) *WorkflowNodeUpdateOne {
	wnuo.mutation.AppendOutputValues(jm)
	return wnuo
}

// SetInputPorts sets the "input_ports" field.
func (wnuo *WorkflowNodeUpdateOne) SetInputPorts(vp []*v1.NodePort) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetInputPorts(vp)
	return wnuo
}

// AppendInputPorts appends vp to the "input_ports" field.
func (wnuo *WorkflowNodeUpdateOne) AppendInputPorts(vp []*v1.NodePort) *WorkflowNodeUpdateOne {
	wnuo.mutation.AppendInputPorts(vp)
	return wnuo
}

// SetOutputPorts sets the "output_ports" field.
func (wnuo *WorkflowNodeUpdateOne) SetOutputPorts(vp []*v1.NodePort) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetOutputPorts(vp)
	return wnuo
}

// AppendOutputPorts appends vp to the "output_ports" field.
func (wnuo *WorkflowNodeUpdateOne) AppendOutputPorts(vp []*v1.NodePort) *WorkflowNodeUpdateOne {
	wnuo.mutation.AppendOutputPorts(vp)
	return wnuo
}

// SetPosition sets the "position" field.
func (wnuo *WorkflowNodeUpdateOne) SetPosition(vnp *v1.WorkflowNodePosition) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetPosition(vnp)
	return wnuo
}

// SetData sets the "data" field.
func (wnuo *WorkflowNodeUpdateOne) SetData(jm json.RawMessage) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetData(jm)
	return wnuo
}

// AppendData appends jm to the "data" field.
func (wnuo *WorkflowNodeUpdateOne) AppendData(jm json.RawMessage) *WorkflowNodeUpdateOne {
	wnuo.mutation.AppendData(jm)
	return wnuo
}

// ClearData clears the value of the "data" field.
func (wnuo *WorkflowNodeUpdateOne) ClearData() *WorkflowNodeUpdateOne {
	wnuo.mutation.ClearData()
	return wnuo
}

// SetUpdatedAt sets the "updated_at" field.
func (wnuo *WorkflowNodeUpdateOne) SetUpdatedAt(t time.Time) *WorkflowNodeUpdateOne {
	wnuo.mutation.SetUpdatedAt(t)
	return wnuo
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wnuo *WorkflowNodeUpdateOne) SetNillableUpdatedAt(t *time.Time) *WorkflowNodeUpdateOne {
	if t != nil {
		wnuo.SetUpdatedAt(*t)
	}
	return wnuo
}

// SetWorkflow sets the "workflow" edge to the Workflow entity.
func (wnuo *WorkflowNodeUpdateOne) SetWorkflow(w *Workflow) *WorkflowNodeUpdateOne {
	return wnuo.SetWorkflowID(w.ID)
}

// Mutation returns the WorkflowNodeMutation object of the builder.
func (wnuo *WorkflowNodeUpdateOne) Mutation() *WorkflowNodeMutation {
	return wnuo.mutation
}

// ClearWorkflow clears the "workflow" edge to the Workflow entity.
func (wnuo *WorkflowNodeUpdateOne) ClearWorkflow() *WorkflowNodeUpdateOne {
	wnuo.mutation.ClearWorkflow()
	return wnuo
}

// Where appends a list predicates to the WorkflowNodeUpdate builder.
func (wnuo *WorkflowNodeUpdateOne) Where(ps ...predicate.WorkflowNode) *WorkflowNodeUpdateOne {
	wnuo.mutation.Where(ps...)
	return wnuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (wnuo *WorkflowNodeUpdateOne) Select(field string, fields ...string) *WorkflowNodeUpdateOne {
	wnuo.fields = append([]string{field}, fields...)
	return wnuo
}

// Save executes the query and returns the updated WorkflowNode entity.
func (wnuo *WorkflowNodeUpdateOne) Save(ctx context.Context) (*WorkflowNode, error) {
	return withHooks(ctx, wnuo.sqlSave, wnuo.mutation, wnuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wnuo *WorkflowNodeUpdateOne) SaveX(ctx context.Context) *WorkflowNode {
	node, err := wnuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (wnuo *WorkflowNodeUpdateOne) Exec(ctx context.Context) error {
	_, err := wnuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wnuo *WorkflowNodeUpdateOne) ExecX(ctx context.Context) {
	if err := wnuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wnuo *WorkflowNodeUpdateOne) check() error {
	if wnuo.mutation.WorkflowCleared() && len(wnuo.mutation.WorkflowIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "WorkflowNode.workflow"`)
	}
	return nil
}

func (wnuo *WorkflowNodeUpdateOne) sqlSave(ctx context.Context) (_node *WorkflowNode, err error) {
	if err := wnuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(workflownode.Table, workflownode.Columns, sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID))
	id, ok := wnuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "WorkflowNode.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := wnuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, workflownode.FieldID)
		for _, f := range fields {
			if !workflownode.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != workflownode.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := wnuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wnuo.mutation.Name(); ok {
		_spec.SetField(workflownode.FieldName, field.TypeString, value)
	}
	if value, ok := wnuo.mutation.Description(); ok {
		_spec.SetField(workflownode.FieldDescription, field.TypeString, value)
	}
	if value, ok := wnuo.mutation.Icon(); ok {
		_spec.SetField(workflownode.FieldIcon, field.TypeString, value)
	}
	if value, ok := wnuo.mutation.GetType(); ok {
		_spec.SetField(workflownode.FieldType, field.TypeString, value)
	}
	if value, ok := wnuo.mutation.Version(); ok {
		_spec.SetField(workflownode.FieldVersion, field.TypeString, value)
	}
	if value, ok := wnuo.mutation.PluginName(); ok {
		_spec.SetField(workflownode.FieldPluginName, field.TypeString, value)
	}
	if value, ok := wnuo.mutation.PluginVersion(); ok {
		_spec.SetField(workflownode.FieldPluginVersion, field.TypeString, value)
	}
	if value, ok := wnuo.mutation.InputParams(); ok {
		_spec.SetField(workflownode.FieldInputParams, field.TypeJSON, value)
	}
	if value, ok := wnuo.mutation.AppendedInputParams(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldInputParams, value)
		})
	}
	if value, ok := wnuo.mutation.InputValues(); ok {
		_spec.SetField(workflownode.FieldInputValues, field.TypeJSON, value)
	}
	if value, ok := wnuo.mutation.AppendedInputValues(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldInputValues, value)
		})
	}
	if value, ok := wnuo.mutation.OutputParams(); ok {
		_spec.SetField(workflownode.FieldOutputParams, field.TypeJSON, value)
	}
	if value, ok := wnuo.mutation.AppendedOutputParams(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldOutputParams, value)
		})
	}
	if value, ok := wnuo.mutation.OutputValues(); ok {
		_spec.SetField(workflownode.FieldOutputValues, field.TypeJSON, value)
	}
	if value, ok := wnuo.mutation.AppendedOutputValues(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldOutputValues, value)
		})
	}
	if value, ok := wnuo.mutation.InputPorts(); ok {
		_spec.SetField(workflownode.FieldInputPorts, field.TypeJSON, value)
	}
	if value, ok := wnuo.mutation.AppendedInputPorts(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldInputPorts, value)
		})
	}
	if value, ok := wnuo.mutation.OutputPorts(); ok {
		_spec.SetField(workflownode.FieldOutputPorts, field.TypeJSON, value)
	}
	if value, ok := wnuo.mutation.AppendedOutputPorts(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldOutputPorts, value)
		})
	}
	if value, ok := wnuo.mutation.Position(); ok {
		_spec.SetField(workflownode.FieldPosition, field.TypeJSON, value)
	}
	if value, ok := wnuo.mutation.Data(); ok {
		_spec.SetField(workflownode.FieldData, field.TypeJSON, value)
	}
	if value, ok := wnuo.mutation.AppendedData(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflownode.FieldData, value)
		})
	}
	if wnuo.mutation.DataCleared() {
		_spec.ClearField(workflownode.FieldData, field.TypeJSON)
	}
	if value, ok := wnuo.mutation.UpdatedAt(); ok {
		_spec.SetField(workflownode.FieldUpdatedAt, field.TypeTime, value)
	}
	if wnuo.mutation.WorkflowCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflownode.WorkflowTable,
			Columns: []string{workflownode.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wnuo.mutation.WorkflowIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflownode.WorkflowTable,
			Columns: []string{workflownode.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &WorkflowNode{config: wnuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, wnuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workflownode.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	wnuo.mutation.done = true
	return _node, nil
}
