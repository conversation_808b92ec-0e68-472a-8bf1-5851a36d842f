package middlewares

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"resflow/configs"
	"resflow/database"
	"resflow/ent"
	"resflow/ent/user"
	"resflow/errors"
	"resflow/repo"
	"resflow/utils"
	"slices"
)

var WhiteList = []string{
	"/api/login",
}

var AuthUserKey = "auth-user"

func AuthMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		if slices.Contains(WhiteList, c.Path()) {
			return c.Next()
		}
		authHeader := c.Get("Authorization")
		if authHeader == "" {
			return errors.Unauthenticated
		}
		tokenString := authHeader[len("Bearer "):]
		utils.Logger.Debug("Token:", tokenString)
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, errors.NewError(400, "unsupported signing method")
			}
			return []byte(configs.ParsedConfig.JwtSecret), nil
		})

		if err != nil || !token.Valid {
			utils.Logger.Debug("token parse err:", err)
			return errors.Unauthenticated
		}

		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			utils.Logger.Debug("token claims parse err:", err)
			return errors.Unauthenticated
		}
		userId, ok := claims["id"].(uuid.UUID)
		if !ok {
			utils.Logger.Debug("id parse err:", err)
			return errors.Unauthenticated
		}

		// 查找用户
		u, err := database.Client.User.Query().Where(user.ID(userId)).First(c.Context())
		if err != nil {
			utils.Logger.Debug("user not found err:", err)
			return errors.Unauthenticated
		}

		c.Locals(AuthUserKey, u)
		return c.Next()
	}
}

var grpcWhiteList = map[string]bool{
	"/v1.AuthService/Login": true,
}

func inGrpcWhiteList(method string) bool {
	return grpcWhiteList[method]
}

// BuildAuthUnaryInterceptor 构建GRPC认证拦截器
func BuildAuthUnaryInterceptor(userStore repo.User) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp any, err error) {
		// 判断是否在白名单
		if inGrpcWhiteList(info.FullMethod) {
			return handler(ctx, req)
		}

		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return nil, status.Errorf(codes.Unauthenticated, "metadata not provided")
		}

		// 解析token
		token, err := extractAndValidateToken(md)
		if err != nil {
			return nil, status.Errorf(codes.Unauthenticated, err.Error())
		}

		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			utils.Logger.Debug("token claims parse err:", err)
			return nil, status.Error(codes.Unauthenticated, "invalid token claims")
		}

		userIdString, ok := claims["id"].(string)
		if !ok {
			utils.Logger.Debug("id parse err:", err, claims["id"])
			return nil, status.Error(codes.Unauthenticated, "invalid token claims")
		}
		userId, err := uuid.Parse(userIdString)
		if err != nil {
			utils.Logger.Debug("user id parse err:", err)
			return nil, status.Error(codes.Unauthenticated, "invalid token claims")
		}

		// 查找用户
		u, err := userStore.GetById(ctx, userId)
		if err != nil {
			utils.Logger.Debug("user not found err:", err)
			return nil, status.Error(codes.Unauthenticated, err.Error())
		}

		ctx = context.WithValue(ctx, AuthUserKey, u)

		return handler(ctx, req)
	}
}

func extractAndValidateToken(md metadata.MD) (*jwt.Token, error) {
	authorization := md.Get("authorization")
	if len(authorization) == 0 {
		return nil, status.Error(codes.Unauthenticated, "authorization token not provided")
	}

	tokenString := authorization[0][len("Bearer "):]
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.NewError(400, "unsupported signing method")
		}
		return []byte(configs.ParsedConfig.JwtSecret), nil
	})

	if err != nil || !token.Valid {
		return nil, status.Error(codes.Unauthenticated, "invalid token")
	}

	return token, nil
}

func GetCurrentAuthUser(ctx context.Context) (*ent.User, error) {
	u, ok := ctx.Value(AuthUserKey).(*ent.User)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "user not found in context")
	}
	return u, nil
}
