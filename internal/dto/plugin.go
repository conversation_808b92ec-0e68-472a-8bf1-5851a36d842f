package dto

import "time"

type PluginDTO struct {
	ID          string    `json:"id" yaml:"id"`
	Name        string    `json:"name" yaml:"name"`
	Version     string    `json:"version" yaml:"version"`
	Author      string    `json:"author" yaml:"author"`
	DisplayName string    `json:"display_name" yaml:"display_name"`
	Description string    `json:"description" yaml:"description"`
	Icon        string    `json:"icon" yaml:"icon"`
	Path        string    `json:"path" yaml:"path"`
	Builtin     bool      `json:"builtin" yaml:"builtin"`
	Enabled     bool      `json:"enabled" yaml:"enabled"`
	CreatedAt   time.Time `json:"created_at" yaml:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" yaml:"updated_at"`
}
