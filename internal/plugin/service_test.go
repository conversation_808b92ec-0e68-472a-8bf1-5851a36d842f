package plugin

import (
	"context"
	"github.com/stretchr/testify/assert"
	"resflow/internal/dto"
	"testing"
)

func TestPluginService_List(t *testing.T) {
	wantPlugins := []*dto.PluginDTO{
		{
			Name: "test",
		},
	}
	store := &Moqstore{
		ListFunc: func(ctx context.Context) ([]*dto.PluginDTO, error) {
			return wantPlugins, nil
		},
	}

	svc := NewPluginService(store)
	gotPlugins, err := svc.List(context.Background())
	assert.NoError(t, err)
	assert.Equal(t, wantPlugins, gotPlugins)
}
