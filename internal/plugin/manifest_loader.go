package plugin

import (
	"path/filepath"
	"resflow/utils"
)

var ManifestFileName = "manifest.yaml"

type Manifest struct {
	Name        string `json:"name" yaml:"name"`
	Version     string `json:"version" yaml:"version"`
	Author      string `json:"author" yaml:"author"`
	DisplayName string `json:"display_name" yaml:"display_name"`
	Description string `json:"description" yaml:"description"`
	Icon        string `json:"icon" yaml:"icon"`
	Path        string `json:"path" yaml:"path"`
	Builtin     bool   `json:"builtin" yaml:"builtin"`
	Enabled     bool   `json:"enabled" yaml:"enabled"`
}

type ManifestLoader interface {
	LoadPlugin(path string) (*Manifest, error)
}

type manifestLoader struct {
}

func NewManifestLoader() ManifestLoader {
	return &manifestLoader{}
}

func (ml *manifestLoader) LoadPlugin(dir string) (*Manifest, error) {
	manifest := utils.NewManifest[Manifest]()
	return manifest.Load(filepath.Join(dir, ManifestFileName))
}
