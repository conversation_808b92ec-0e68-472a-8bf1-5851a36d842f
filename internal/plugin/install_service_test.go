package plugin

import (
	"context"
	"errors"
	"github.com/stretchr/testify/assert"
	"os"
	"path/filepath"
	"resflow/ent"
	"resflow/ent/enttest"
	"resflow/internal/dto"
	"resflow/internal/node_definition"
	"testing"
)

func TestInstallService_Install_Success(t *testing.T) {
	dir := t.TempDir()

	content := `
name: test-plugin
version: 1.0.0
author: test
display_name: test
description: test
icon: test.svg
`
	err := os.WriteFile(filepath.Join(dir, "manifest.yaml"), []byte(content), 0644)
	assert.NoError(t, err)

	givePlugin := &dto.PluginDTO{
		Name:        "test-plugin",
		Version:     "1.0.0",
		Author:      "test",
		DisplayName: "test",
		Description: "test",
		Icon:        "test.svg",
		Path:        dir,
		Builtin:     true,
		Enabled:     true,
	}

	pluginStore := &Moqstore{
		GetByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*dto.PluginDTO, error) {
			return nil, nil
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, p *dto.PluginDTO) (*dto.PluginDTO, error) {
			return givePlugin, nil
		},
		DisableByNameFunc: func(ctx context.Context, tx *ent.Tx, name string) error {
			return nil
		},
	}
	nodesInstallService := &node_definition.MoqInstallService{
		InstallFunc: func(ctx context.Context, nodesDir string, tx *ent.Tx, plugin *dto.PluginDTO) ([]*ent.NodeDefinition, error) {
			return nil, nil
		},
	}

	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&_fk=1")
	installService := NewInstallService(client, NewManifestLoader(), pluginStore, nodesInstallService)

	err = installService.Install(context.Background(), dir, true)
	assert.NoError(t, err)
}

func TestInstallService_Install_InvalidManifest(t *testing.T) {

	wantErr := errors.New("invalid plugin manifest")

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*Manifest, error) {
			return nil, wantErr
		},
	}

	installService := NewInstallService(nil, manifestLoader, nil, nil)
	err := installService.Install(context.Background(), "", true)
	assert.ErrorIs(t, err, wantErr)
}

func TestInstallService_Install_InfoMissing(t *testing.T) {
	cases := []struct {
		caseDoc      string
		giveManifest *Manifest
		wantErr      error
	}{
		{
			caseDoc: "name为空",
			giveManifest: &Manifest{
				Name:        "",
				Version:     "1.0.0",
				Author:      "test",
				DisplayName: "test",
				Description: "test",
				Icon:        "test.svg",
			},
			wantErr: ErrPluginInfoMissing,
		},
		{
			caseDoc: "version为空",
			giveManifest: &Manifest{
				Name:        "test",
				Version:     "",
				Author:      "test",
				DisplayName: "test",
				Description: "test",
				Icon:        "test.svg",
			},
			wantErr: ErrPluginInfoMissing,
		},
	}

	for _, tc := range cases {
		t.Run(tc.caseDoc, func(t *testing.T) {
			manifestLoader := &MoqManifestLoader{
				LoadPluginFunc: func(path string) (*Manifest, error) {
					return &Manifest{}, nil
				},
			}
			installService := NewInstallService(nil, manifestLoader, nil, nil)
			err := installService.Install(context.Background(), "", true)
			assert.ErrorIs(t, err, tc.wantErr)
		})
	}
}

func TestInstallService_Install_AlreadyInstalled(t *testing.T) {
	pluginStore := &Moqstore{
		GetByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*dto.PluginDTO, error) {
			return &dto.PluginDTO{}, nil
		},
	}

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*Manifest, error) {
			return &Manifest{
				Name:    "test",
				Version: "1.0.0",
			}, nil
		},
	}

	installService := NewInstallService(nil, manifestLoader, pluginStore, nil)
	err := installService.Install(context.Background(), "", true)
	assert.NoError(t, err)
}

func TestInstallService_Install_NodesInstallFailed(t *testing.T) {
	wantErr := errors.New("install nodes failed")

	pluginStore := &Moqstore{
		GetByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*dto.PluginDTO, error) {
			return nil, nil
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, p *dto.PluginDTO) (*dto.PluginDTO, error) {
			return &dto.PluginDTO{}, nil
		},
		DisableByNameFunc: func(ctx context.Context, tx *ent.Tx, name string) error {
			return nil
		},
	}

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*Manifest, error) {
			return &Manifest{
				Name:    "test",
				Version: "1.0.0",
			}, nil
		},
	}

	nodesInstallService := &node_definition.MoqInstallService{
		InstallFunc: func(ctx context.Context, nodesDir string, tx *ent.Tx, plugin *dto.PluginDTO) ([]*ent.NodeDefinition, error) {
			return nil, wantErr
		},
	}

	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&_fk=1")
	installService := NewInstallService(client, manifestLoader, pluginStore, nodesInstallService)
	err := installService.Install(context.Background(), "", true)
	assert.ErrorIs(t, err, wantErr)
}

func TestInstallService_Install_CreateFailed(t *testing.T) {
	wantErr := errors.New("create plugin failed")

	pluginStore := &Moqstore{
		GetByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*dto.PluginDTO, error) {
			return nil, nil
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, p *dto.PluginDTO) (*dto.PluginDTO, error) {
			return nil, wantErr
		},
		DisableByNameFunc: func(ctx context.Context, tx *ent.Tx, name string) error {
			return nil
		},
	}

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*Manifest, error) {
			return &Manifest{
				Name:    "test",
				Version: "1.0.0",
			}, nil
		},
	}

	nodesInstallService := &node_definition.MoqInstallService{
		InstallFunc: func(ctx context.Context, nodesDir string, tx *ent.Tx, plugin *dto.PluginDTO) ([]*ent.NodeDefinition, error) {
			return nil, nil
		},
	}

	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&_fk=1")
	installService := NewInstallService(client, manifestLoader, pluginStore, nodesInstallService)
	err := installService.Install(context.Background(), "", true)
	assert.ErrorIs(t, err, wantErr)
}

func TestInstallService_Install_DisableFailed(t *testing.T) {
	wantErr := errors.New("disable plugin failed")

	pluginStore := &Moqstore{
		GetByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*dto.PluginDTO, error) {
			return nil, nil
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, p *dto.PluginDTO) (*dto.PluginDTO, error) {
			return &dto.PluginDTO{}, nil
		},
		DisableByNameFunc: func(ctx context.Context, tx *ent.Tx, name string) error {
			return wantErr
		},
	}

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*Manifest, error) {
			return &Manifest{
				Name:    "test",
				Version: "1.0.0",
			}, nil
		},
	}

	nodesInstallService := &node_definition.MoqInstallService{
		InstallFunc: func(ctx context.Context, nodesDir string, tx *ent.Tx, plugin *dto.PluginDTO) ([]*ent.NodeDefinition, error) {
			return nil, nil
		},
	}

	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&_fk=1")
	installService := NewInstallService(client, manifestLoader, pluginStore, nodesInstallService)
	err := installService.Install(context.Background(), "", true)
	assert.ErrorIs(t, err, wantErr)
}

func TestInstallService_Install_GetPluginFailed(t *testing.T) {
	wantErr := errors.New("get plugin failed")

	pluginStore := &Moqstore{
		GetByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*dto.PluginDTO, error) {
			return nil, wantErr
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, p *dto.PluginDTO) (*dto.PluginDTO, error) {
			return &dto.PluginDTO{}, nil
		},
		DisableByNameFunc: func(ctx context.Context, tx *ent.Tx, name string) error {
			return nil
		},
	}

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*Manifest, error) {
			return &Manifest{
				Name:    "test",
				Version: "1.0.0",
			}, nil
		},
	}

	nodesInstallService := &node_definition.MoqInstallService{
		InstallFunc: func(ctx context.Context, nodesDir string, tx *ent.Tx, plugin *dto.PluginDTO) ([]*ent.NodeDefinition, error) {
			return nil, nil
		},
	}

	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&_fk=1")
	installService := NewInstallService(client, manifestLoader, pluginStore, nodesInstallService)
	err := installService.Install(context.Background(), "", true)
	assert.ErrorIs(t, err, wantErr)
}
