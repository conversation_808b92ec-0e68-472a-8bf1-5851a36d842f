package plugin

import (
	"context"
	"resflow/internal/dto"
)

type Service interface {
	List(ctx context.Context) ([]*dto.PluginDTO, error)
	GetByNameAndVersion(ctx context.Context, name string, version string) (*dto.PluginDTO, error)
}

type pluginService struct {
	store store
}

func NewPluginService(store store) Service {
	return &pluginService{
		store: store,
	}
}

func (ps *pluginService) List(ctx context.Context) ([]*dto.PluginDTO, error) {
	return ps.store.List(ctx)
}

func (ps *pluginService) GetByNameAndVersion(ctx context.Context, name string, version string) (*dto.PluginDTO, error) {
	return ps.store.GetByNameAndVersion(ctx, name, version)
}
