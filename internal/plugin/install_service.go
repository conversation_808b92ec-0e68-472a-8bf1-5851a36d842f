package plugin

import (
	"context"
	"errors"
	"path/filepath"
	"resflow/database"
	"resflow/ent"
	"resflow/internal/dto"
	"resflow/internal/node_definition"
	"resflow/utils"
)

var (
	ErrPluginInfoMissing = errors.New("plugin info missing")
)

type InstallService interface {
	Install(ctx context.Context, path string, builtin bool) error
}

type installService struct {
	client              *ent.Client
	manifestLoader      ManifestLoader
	store               store
	nodesInstallService node_definition.InstallService
}

func NewInstallService(client *ent.Client, manifestLoader ManifestLoader, store store, nodesInstallService node_definition.InstallService) InstallService {
	return &installService{
		client:              client,
		manifestLoader:      manifestLoader,
		store:               store,
		nodesInstallService: nodesInstallService,
	}
}

func (is *installService) Install(ctx context.Context, pluginDir string, builtin bool) error {

	pluginManifest, err := is.manifestLoader.LoadPlugin(pluginDir)
	if err != nil {
		return err
	}

	if pluginManifest.Name == "" || pluginManifest.Version == "" {
		return ErrPluginInfoMissing
	}

	plugin, err := is.store.GetByNameAndVersion(ctx, pluginManifest.Name, pluginManifest.Version)
	if err != nil && !ent.IsNotFound(err) {
		return err
	}
	if plugin != nil {
		utils.Logger.Debug("plugin already installed, skip")
		return nil
	}

	err = database.WithTx(context.Background(), is.client, func(tx *ent.Tx) error {
		utils.Logger.Debug("plugin installing")

		err = is.store.DisableByName(context.Background(), tx, pluginManifest.Name)
		if err != nil && !ent.IsNotFound(err) {
			return err
		}

		plugin, err = is.store.Create(context.Background(), tx, &dto.PluginDTO{
			Name:        pluginManifest.Name,
			Version:     pluginManifest.Version,
			Author:      pluginManifest.Author,
			DisplayName: pluginManifest.DisplayName,
			Description: pluginManifest.Description,
			Icon:        pluginManifest.Icon,
			Path:        pluginDir,
			Builtin:     builtin,
			Enabled:     true,
		})
		if err != nil {
			return err
		}

		_, err = is.nodesInstallService.Install(ctx, filepath.Join(pluginDir, "nodes"), tx, plugin)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	utils.Logger.Debug("plugin installed")

	return nil
}
