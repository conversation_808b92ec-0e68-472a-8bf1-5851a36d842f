// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: matryer

package plugin

import (
	"context"
	"resflow/ent"
	"resflow/internal/dto"
	"sync"
)

// Ensure that MoqManifestLoader does implement ManifestLoader.
// If this is not the case, regenerate this file with mockery.
var _ ManifestLoader = &MoqManifestLoader{}

// MoqManifestLoader is a mock implementation of ManifestLoader.
//
//	func TestSomethingThatUsesManifestLoader(t *testing.T) {
//
//		// make and configure a mocked ManifestLoader
//		mockedManifestLoader := &MoqManifestLoader{
//			LoadPluginFunc: func(path string) (*Manifest, error) {
//				panic("mock out the LoadPlugin method")
//			},
//		}
//
//		// use mockedManifestLoader in code that requires ManifestLoader
//		// and then make assertions.
//
//	}
type MoqManifestLoader struct {
	// LoadPluginFunc mocks the LoadPlugin method.
	LoadPluginFunc func(path string) (*Manifest, error)

	// calls tracks calls to the methods.
	calls struct {
		// LoadPlugin holds details about calls to the LoadPlugin method.
		LoadPlugin []struct {
			// Path is the path argument value.
			Path string
		}
	}
	lockLoadPlugin sync.RWMutex
}

// LoadPlugin calls LoadPluginFunc.
func (mock *MoqManifestLoader) LoadPlugin(path string) (*Manifest, error) {
	if mock.LoadPluginFunc == nil {
		panic("MoqManifestLoader.LoadPluginFunc: method is nil but ManifestLoader.LoadPlugin was just called")
	}
	callInfo := struct {
		Path string
	}{
		Path: path,
	}
	mock.lockLoadPlugin.Lock()
	mock.calls.LoadPlugin = append(mock.calls.LoadPlugin, callInfo)
	mock.lockLoadPlugin.Unlock()
	return mock.LoadPluginFunc(path)
}

// LoadPluginCalls gets all the calls that were made to LoadPlugin.
// Check the length with:
//
//	len(mockedManifestLoader.LoadPluginCalls())
func (mock *MoqManifestLoader) LoadPluginCalls() []struct {
	Path string
} {
	var calls []struct {
		Path string
	}
	mock.lockLoadPlugin.RLock()
	calls = mock.calls.LoadPlugin
	mock.lockLoadPlugin.RUnlock()
	return calls
}

// Ensure that Moqstore does implement store.
// If this is not the case, regenerate this file with mockery.
var _ store = &Moqstore{}

// Moqstore is a mock implementation of store.
//
//	func TestSomethingThatUsesstore(t *testing.T) {
//
//		// make and configure a mocked store
//		mockedstore := &Moqstore{
//			CreateFunc: func(ctx context.Context, tx *ent.Tx, p *dto.PluginDTO) (*dto.PluginDTO, error) {
//				panic("mock out the Create method")
//			},
//			DisableByNameFunc: func(ctx context.Context, tx *ent.Tx, name string) error {
//				panic("mock out the DisableByName method")
//			},
//			GetByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*dto.PluginDTO, error) {
//				panic("mock out the GetByNameAndVersion method")
//			},
//			ListFunc: func(ctx context.Context) ([]*dto.PluginDTO, error) {
//				panic("mock out the List method")
//			},
//		}
//
//		// use mockedstore in code that requires store
//		// and then make assertions.
//
//	}
type Moqstore struct {
	// CreateFunc mocks the Create method.
	CreateFunc func(ctx context.Context, tx *ent.Tx, p *dto.PluginDTO) (*dto.PluginDTO, error)

	// DisableByNameFunc mocks the DisableByName method.
	DisableByNameFunc func(ctx context.Context, tx *ent.Tx, name string) error

	// GetByNameAndVersionFunc mocks the GetByNameAndVersion method.
	GetByNameAndVersionFunc func(ctx context.Context, name string, version string) (*dto.PluginDTO, error)

	// ListFunc mocks the List method.
	ListFunc func(ctx context.Context) ([]*dto.PluginDTO, error)

	// calls tracks calls to the methods.
	calls struct {
		// Create holds details about calls to the Create method.
		Create []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// P is the p argument value.
			P *dto.PluginDTO
		}
		// DisableByName holds details about calls to the DisableByName method.
		DisableByName []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// Name is the name argument value.
			Name string
		}
		// GetByNameAndVersion holds details about calls to the GetByNameAndVersion method.
		GetByNameAndVersion []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Name is the name argument value.
			Name string
			// Version is the version argument value.
			Version string
		}
		// List holds details about calls to the List method.
		List []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
		}
	}
	lockCreate              sync.RWMutex
	lockDisableByName       sync.RWMutex
	lockGetByNameAndVersion sync.RWMutex
	lockList                sync.RWMutex
}

// Create calls CreateFunc.
func (mock *Moqstore) Create(ctx context.Context, tx *ent.Tx, p *dto.PluginDTO) (*dto.PluginDTO, error) {
	if mock.CreateFunc == nil {
		panic("Moqstore.CreateFunc: method is nil but store.Create was just called")
	}
	callInfo := struct {
		Ctx context.Context
		Tx  *ent.Tx
		P   *dto.PluginDTO
	}{
		Ctx: ctx,
		Tx:  tx,
		P:   p,
	}
	mock.lockCreate.Lock()
	mock.calls.Create = append(mock.calls.Create, callInfo)
	mock.lockCreate.Unlock()
	return mock.CreateFunc(ctx, tx, p)
}

// CreateCalls gets all the calls that were made to Create.
// Check the length with:
//
//	len(mockedstore.CreateCalls())
func (mock *Moqstore) CreateCalls() []struct {
	Ctx context.Context
	Tx  *ent.Tx
	P   *dto.PluginDTO
} {
	var calls []struct {
		Ctx context.Context
		Tx  *ent.Tx
		P   *dto.PluginDTO
	}
	mock.lockCreate.RLock()
	calls = mock.calls.Create
	mock.lockCreate.RUnlock()
	return calls
}

// DisableByName calls DisableByNameFunc.
func (mock *Moqstore) DisableByName(ctx context.Context, tx *ent.Tx, name string) error {
	if mock.DisableByNameFunc == nil {
		panic("Moqstore.DisableByNameFunc: method is nil but store.DisableByName was just called")
	}
	callInfo := struct {
		Ctx  context.Context
		Tx   *ent.Tx
		Name string
	}{
		Ctx:  ctx,
		Tx:   tx,
		Name: name,
	}
	mock.lockDisableByName.Lock()
	mock.calls.DisableByName = append(mock.calls.DisableByName, callInfo)
	mock.lockDisableByName.Unlock()
	return mock.DisableByNameFunc(ctx, tx, name)
}

// DisableByNameCalls gets all the calls that were made to DisableByName.
// Check the length with:
//
//	len(mockedstore.DisableByNameCalls())
func (mock *Moqstore) DisableByNameCalls() []struct {
	Ctx  context.Context
	Tx   *ent.Tx
	Name string
} {
	var calls []struct {
		Ctx  context.Context
		Tx   *ent.Tx
		Name string
	}
	mock.lockDisableByName.RLock()
	calls = mock.calls.DisableByName
	mock.lockDisableByName.RUnlock()
	return calls
}

// GetByNameAndVersion calls GetByNameAndVersionFunc.
func (mock *Moqstore) GetByNameAndVersion(ctx context.Context, name string, version string) (*dto.PluginDTO, error) {
	if mock.GetByNameAndVersionFunc == nil {
		panic("Moqstore.GetByNameAndVersionFunc: method is nil but store.GetByNameAndVersion was just called")
	}
	callInfo := struct {
		Ctx     context.Context
		Name    string
		Version string
	}{
		Ctx:     ctx,
		Name:    name,
		Version: version,
	}
	mock.lockGetByNameAndVersion.Lock()
	mock.calls.GetByNameAndVersion = append(mock.calls.GetByNameAndVersion, callInfo)
	mock.lockGetByNameAndVersion.Unlock()
	return mock.GetByNameAndVersionFunc(ctx, name, version)
}

// GetByNameAndVersionCalls gets all the calls that were made to GetByNameAndVersion.
// Check the length with:
//
//	len(mockedstore.GetByNameAndVersionCalls())
func (mock *Moqstore) GetByNameAndVersionCalls() []struct {
	Ctx     context.Context
	Name    string
	Version string
} {
	var calls []struct {
		Ctx     context.Context
		Name    string
		Version string
	}
	mock.lockGetByNameAndVersion.RLock()
	calls = mock.calls.GetByNameAndVersion
	mock.lockGetByNameAndVersion.RUnlock()
	return calls
}

// List calls ListFunc.
func (mock *Moqstore) List(ctx context.Context) ([]*dto.PluginDTO, error) {
	if mock.ListFunc == nil {
		panic("Moqstore.ListFunc: method is nil but store.List was just called")
	}
	callInfo := struct {
		Ctx context.Context
	}{
		Ctx: ctx,
	}
	mock.lockList.Lock()
	mock.calls.List = append(mock.calls.List, callInfo)
	mock.lockList.Unlock()
	return mock.ListFunc(ctx)
}

// ListCalls gets all the calls that were made to List.
// Check the length with:
//
//	len(mockedstore.ListCalls())
func (mock *Moqstore) ListCalls() []struct {
	Ctx context.Context
} {
	var calls []struct {
		Ctx context.Context
	}
	mock.lockList.RLock()
	calls = mock.calls.List
	mock.lockList.RUnlock()
	return calls
}
