package plugin

import (
	"resflow/ent"
	"resflow/internal/dto"
)

func toDTO(pluginEntity *ent.Plugin) *dto.PluginDTO {
	return &dto.PluginDTO{
		ID:          pluginEntity.ID.String(),
		Name:        pluginEntity.Name,
		Version:     pluginEntity.Version,
		Author:      pluginEntity.Author,
		DisplayName: pluginEntity.DisplayName,
		Description: pluginEntity.Description,
		Icon:        pluginEntity.Icon,
		Path:        pluginEntity.Path,
		Builtin:     pluginEntity.Builtin,
		Enabled:     pluginEntity.Enabled,
		CreatedAt:   pluginEntity.CreatedAt,
		UpdatedAt:   pluginEntity.UpdatedAt,
	}
}

func toDTOs(pluginEntities []*ent.Plugin) []*dto.PluginDTO {
	pluginDTOs := make([]*dto.PluginDTO, 0)
	for _, pluginEntity := range pluginEntities {
		pluginDTOs = append(pluginDTOs, toDTO(pluginEntity))
	}
	return pluginDTOs
}
