package plugin

import (
	"context"
	"entgo.io/ent/dialect/sql"
	"resflow/ent"
	"resflow/ent/plugin"
	"resflow/internal/dto"
)

type store interface {
	GetByNameAndVersion(ctx context.Context, name string, version string) (*dto.PluginDTO, error)
	Create(ctx context.Context, tx *ent.Tx, p *dto.PluginDTO) (*dto.PluginDTO, error)
	DisableByName(ctx context.Context, tx *ent.Tx, name string) error
	List(ctx context.Context) ([]*dto.PluginDTO, error)
}

type PluginStore struct {
	client *ent.Client
}

func NewPluginStore(client *ent.Client) store {
	return &PluginStore{
		client: client,
	}
}

func (ps *PluginStore) GetByNameAndVersion(ctx context.Context, name string, version string) (*dto.PluginDTO, error) {
	pluginEntity, err := ps.client.Plugin.Query().Where(plugin.Name(name)).Where(plugin.Version(version)).First(ctx)
	if err != nil {
		return nil, err
	}
	return toDTO(pluginEntity), err
}

func (ps *PluginStore) Create(ctx context.Context, tx *ent.Tx, p *dto.PluginDTO) (*dto.PluginDTO, error) {
	pluginEntity, err := tx.Plugin.Create().SetName(p.Name).SetVersion(p.Version).SetAuthor(p.Author).SetDisplayName(p.DisplayName).SetDescription(p.Description).SetIcon(p.Icon).SetPath(p.Path).SetBuiltin(p.Builtin).SetEnabled(p.Enabled).Save(ctx)
	if err != nil {
		return nil, err
	}
	return toDTO(pluginEntity), err
}

func (ps *PluginStore) DisableByName(ctx context.Context, tx *ent.Tx, name string) error {
	return tx.Plugin.Update().Where(plugin.Name(name)).SetEnabled(false).Exec(ctx)
}

func (ps *PluginStore) List(ctx context.Context) ([]*dto.PluginDTO, error) {
	pluginEntities, err := ps.client.Plugin.Query().Where(plugin.Enabled(true)).Order(plugin.ByCreatedAt(sql.OrderDesc())).All(ctx)
	if err != nil {
		return nil, err
	}
	return toDTOs(pluginEntities), err
}
