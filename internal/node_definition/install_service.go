package node_definition

import (
	"context"
	"errors"
	"os"
	"path/filepath"
	"resflow/ent"
	"resflow/internal/dto"
	"resflow/utils"
)

var (
	ErrNodeInfoMissing = errors.New("node info missing")
)

type InstallService interface {
	Install(ctx context.Context, nodesDir string, tx *ent.Tx, plugin *dto.PluginDTO) ([]*NodeDefinitionDTO, error)
}

type installService struct {
	manifestLoader ManifestLoader
	store          store
}

func NewInstallService(manifestLoader ManifestLoader, store store) InstallService {
	return &installService{
		manifestLoader: manifestLoader,
		store:          store,
	}
}

func (is *installService) Install(ctx context.Context, nodesDir string, tx *ent.Tx, plugin *dto.PluginDTO) ([]*NodeDefinitionDTO, error) {
	utils.Logger.Debug("plugin nodes installing")

	files, err := os.ReadDir(nodesDir)
	if err != nil {
		return nil, err
	}

	nodeDefinitions := make([]*NodeDefinitionDTO, 0)
	for _, file := range files {
		if !file.IsDir() {
			continue
		}

		nodeDef, err := is.installNode(ctx, filepath.Join(nodesDir, file.Name()), plugin, tx)
		if err != nil {
			return nil, err
		}
		nodeDefinitions = append(nodeDefinitions, nodeDef)
	}

	return nodeDefinitions, nil
}

func (is *installService) installNode(ctx context.Context, nodeDir string, plugin *dto.PluginDTO, tx *ent.Tx) (*NodeDefinitionDTO, error) {
	utils.Logger.Debugf("installing node %s", nodeDir)
	nodeManifest, err := is.manifestLoader.LoadNode(nodeDir)
	if err != nil {
		return nil, err
	}

	if nodeManifest.Type == "" || nodeManifest.Version == "" {
		return nil, ErrNodeInfoMissing
	}

	nodeDef, err := is.store.GetByTypeAndVersion(ctx, nodeManifest.Type, nodeManifest.Version)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}
	if nodeDef != nil {
		utils.Logger.Debug("node %s already installed, skip", nodeDef.Name)
		return nodeDef, nil
	}

	err = is.store.DisableNodeByType(ctx, tx, nodeManifest.Type)
	if err != nil {
		return nil, err
	}

	nodeDef, err = is.store.Create(context.Background(), tx, &NodeDefinitionDTO{
		PluginName:    plugin.Name,
		PluginVersion: plugin.Version,
		Name:          nodeManifest.Name,
		Author:        nodeManifest.Author,
		Description:   nodeManifest.Description,
		Icon:          nodeManifest.Icon,
		Type:          nodeManifest.Type,
		Version:       nodeManifest.Version,
		Category:      nodeManifest.Category,
		InputParams:   nodeManifest.InputParams,
		OutputParams:  nodeManifest.OutputParams,
		InputPorts:    nodeManifest.InputPorts,
		OutputPorts:   nodeManifest.OutputPorts,
		Exception:     nodeManifest.Exception,
		Path:          nodeDir,
		Builtin:       plugin.Builtin,
		Enabled:       true,
	})
	if err != nil {
		return nil, err
	}

	return nodeDef, nil
}
