package node_definition

type NodeParam struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	ViewType    string                 `json:"view_type"`
	Label       string                 `json:"label"`
	Description string                 `json:"description"`
	Placeholder string                 `json:"placeholder"`
	Required    bool                   `json:"required"`
	Default     string                 `json:"default"`
	ViewConfig  map[string]interface{} `json:"view_config"`
}

type SelectOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type SelectOptions []*SelectOption

type MapSchema struct {
	KeyTitle   string `json:"key_title"`
	KeyType    string `json:"key_type"`
	ValueTitle string `json:"value_title"`
	ValueType  string `json:"value_type"`
}
