package node_definition

import (
	"context"
	"resflow/ent"
	"resflow/ent/nodedefinition"
)

type store interface {
	GetByTypeAndVersion(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error)
	Create(ctx context.Context, tx *ent.Tx, nodeDefinition *NodeDefinitionDTO) (*NodeDefinitionDTO, error)
	DisableNodeByType(ctx context.Context, tx *ent.Tx, typeVar string) error
	List(ctx context.Context) ([]*NodeDefinitionDTO, error)
}

type NodeDefinitionStore struct {
	client *ent.Client
}

func NewNodeDefinitionStore(client *ent.Client) store {
	return &NodeDefinitionStore{client: client}
}

func (nds *NodeDefinitionStore) GetByTypeAndVersion(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
	nodeDefinition, err := nds.client.NodeDefinition.Query().Where(nodedefinition.Type(typeVar), nodedefinition.Version(version)).First(ctx)
	if err != nil {
		return nil, err
	}
	return toDTO(nodeDefinition), err
}

func (nds *NodeDefinitionStore) Create(ctx context.Context, tx *ent.Tx, nodeDefinition *NodeDefinitionDTO) (*NodeDefinitionDTO, error) {
	savedNodeDefinition, err := tx.NodeDefinition.Create().SetPluginName(nodeDefinition.PluginName).SetPluginVersion(nodeDefinition.PluginVersion).SetName(nodeDefinition.Name).SetAuthor(nodeDefinition.Author).SetDescription(nodeDefinition.Description).SetIcon(nodeDefinition.Icon).SetType(nodeDefinition.Type).SetVersion(nodeDefinition.Version).SetCategory(nodeDefinition.Category).SetInputParams(nodeDefinition.InputParams).SetOutputParams(nodeDefinition.OutputParams).SetInputPorts(nodeDefinition.InputPorts).SetOutputPorts(nodeDefinition.OutputPorts).SetException(nodeDefinition.Exception).SetPath(nodeDefinition.Path).SetBuiltin(nodeDefinition.Builtin).SetEnabled(nodeDefinition.Enabled).Save(ctx)
	if err != nil {
		return nil, err
	}
	return toDTO(savedNodeDefinition), err
}

func (nds *NodeDefinitionStore) DisableNodeByType(ctx context.Context, tx *ent.Tx, typeVar string) error {
	return tx.NodeDefinition.Update().Where(nodedefinition.Type(typeVar)).SetEnabled(false).Exec(ctx)
}

func (nds *NodeDefinitionStore) List(ctx context.Context) ([]*NodeDefinitionDTO, error) {
	nodeDefinitions, err := nds.client.NodeDefinition.Query().Where(nodedefinition.Enabled(true)).All(ctx)
	if err != nil {
		return nil, err
	}
	return toDTOs(nodeDefinitions), err
}
