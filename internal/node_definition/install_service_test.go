package node_definition

import (
	"context"
	"errors"
	"github.com/stretchr/testify/assert"
	"os"
	"path/filepath"
	"resflow/ent"
	"resflow/internal/dto"
	v1 "resflow/proto/generated_go/v1"
	"testing"
)

func TestNodesInstallService_Install_Success(t *testing.T) {
	dir := t.TempDir()
	err := os.Mkdir(filepath.Join(dir, "test-node"), 0755)
	assert.NoError(t, err)

	giveManifest := &Manifest{
		Name:        "test",
		Author:      "test",
		Version:     "1.0.0",
		Description: "test",
		Icon:        "icon.svg",
		Type:        "test",
		Category:    "test",
		InputParams: []*v1.NodeParam{
			{
				Type:        "string",
				Label:       "test",
				Description: "test",
				Value:       "test",
				Required:    true,
			},
		},
		OutputParams: []*v1.NodeParam{
			{
				Type:        "string",
				Label:       "test",
				Description: "test",
				Value:       "test",
				Required:    true,
			},
		},
		InputPorts: []*v1.NodePort{
			{
				Id:       "input",
				Type:     "input",
				Position: "left",
			},
		},
		OutputPorts: []*v1.NodePort{
			{
				Id:       "output",
				Type:     "output",
				Position: "right",
			},
		},
		Exception: true,
	}

	manifestLoader := &MoqManifestLoader{
		LoadNodeFunc: func(path string) (*Manifest, error) {
			return giveManifest, nil
		},
	}
	store := &Moqstore{
		GetByTypeAndVersionFunc: func(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
			return nil, nil
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, n *NodeDefinitionDTO) (*NodeDefinitionDTO, error) {
			return &NodeDefinitionDTO{
				Name:         giveManifest.Name,
				Author:       giveManifest.Author,
				Description:  giveManifest.Description,
				Icon:         giveManifest.Icon,
				Type:         giveManifest.Type,
				Version:      giveManifest.Version,
				Category:     giveManifest.Category,
				InputParams:  giveManifest.InputParams,
				OutputParams: giveManifest.OutputParams,
				InputPorts:   giveManifest.InputPorts,
				OutputPorts:  giveManifest.OutputPorts,
				Exception:    giveManifest.Exception,
			}, nil
		},
		DisableNodeByTypeFunc: func(ctx context.Context, tx *ent.Tx, typeVar string) error {
			return nil
		},
	}

	nodesInstallService := NewInstallService(manifestLoader, store)
	nodeDefinitions, err := nodesInstallService.Install(context.Background(), dir, nil, &dto.PluginDTO{})
	assert.NoError(t, err)
	assert.Len(t, nodeDefinitions, 1)
	assert.Equal(t, giveManifest.Name, nodeDefinitions[0].Name)
	assert.Equal(t, giveManifest.Author, nodeDefinitions[0].Author)
	assert.Equal(t, giveManifest.Description, nodeDefinitions[0].Description)
	assert.Equal(t, giveManifest.Icon, nodeDefinitions[0].Icon)
	assert.Equal(t, giveManifest.Type, nodeDefinitions[0].Type)
	assert.Equal(t, giveManifest.Version, nodeDefinitions[0].Version)
	assert.Equal(t, giveManifest.Category, nodeDefinitions[0].Category)
	assert.Equal(t, giveManifest.InputParams, nodeDefinitions[0].InputParams)
	assert.Equal(t, giveManifest.OutputParams, nodeDefinitions[0].OutputParams)
	assert.Equal(t, giveManifest.InputPorts, nodeDefinitions[0].InputPorts)
	assert.Equal(t, giveManifest.OutputPorts, nodeDefinitions[0].OutputPorts)
	assert.Equal(t, giveManifest.Exception, nodeDefinitions[0].Exception)
}

func TestNodesInstallService_Install_DirError(t *testing.T) {
	nodesInstallService := NewInstallService(nil, nil)
	_, err := nodesInstallService.Install(context.Background(), "", nil, nil)
	assert.ErrorIs(t, err, os.ErrNotExist)
}

func TestNodesInstallService_Install_SkipDir(t *testing.T) {
	dir := t.TempDir()
	err := os.WriteFile(filepath.Join(dir, "test"), []byte("this is not dir"), 0644)
	assert.NoError(t, err)
	err = os.Mkdir(filepath.Join(dir, "test-node"), 0755)
	assert.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadNodeFunc: func(path string) (*Manifest, error) {
			return &Manifest{
				Name:    "test",
				Version: "1.0.0",
				Type:    "test",
			}, nil
		}}

	store := &Moqstore{
		GetByTypeAndVersionFunc: func(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
			return nil, nil
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, n *NodeDefinitionDTO) (*NodeDefinitionDTO, error) {
			return &NodeDefinitionDTO{}, nil
		},
		DisableNodeByTypeFunc: func(ctx context.Context, tx *ent.Tx, typeVar string) error {
			return nil
		},
	}

	nodesInstallService := NewInstallService(manifestLoader, store)
	nodeDefinitions, err := nodesInstallService.Install(context.Background(), dir, nil, &dto.PluginDTO{})
	assert.NoError(t, err)
	assert.Len(t, nodeDefinitions, 1)
}

func TestNodesInstallService_Install_InvalidManifest(t *testing.T) {
	wantErr := errors.New("invalid manifest")
	dir := t.TempDir()
	err := os.Mkdir(filepath.Join(dir, "test-node"), 0755)
	assert.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadNodeFunc: func(path string) (*Manifest, error) {
			return nil, wantErr
		},
	}
	store := &Moqstore{
		GetByTypeAndVersionFunc: func(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
			return nil, nil
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, n *NodeDefinitionDTO) (*NodeDefinitionDTO, error) {
			return &NodeDefinitionDTO{}, nil
		},
		DisableNodeByTypeFunc: func(ctx context.Context, tx *ent.Tx, typeVar string) error {
			return nil
		},
	}

	nodesInstallService := NewInstallService(manifestLoader, store)
	_, err = nodesInstallService.Install(context.Background(), dir, nil, &dto.PluginDTO{})
	assert.ErrorIs(t, err, wantErr)
}

func TestNodesInstallService_Install_InfoMissing(t *testing.T) {
	dir := t.TempDir()
	err := os.Mkdir(filepath.Join(dir, "test-node"), 0755)
	assert.NoError(t, err)

	cases := []struct {
		CaseDoc      string
		GiveManifest *Manifest
		WantErr      error
	}{
		{
			CaseDoc: "type为空",
			GiveManifest: &Manifest{
				Type:    "",
				Version: "1.0.0",
			},
			WantErr: ErrNodeInfoMissing,
		}, {
			CaseDoc: "version为空",
			GiveManifest: &Manifest{
				Type:    "test",
				Version: "",
			},
			WantErr: ErrNodeInfoMissing,
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			manifestLoader := &MoqManifestLoader{
				LoadNodeFunc: func(path string) (*Manifest, error) {
					return tc.GiveManifest, nil
				},
			}
			store := &Moqstore{
				GetByTypeAndVersionFunc: func(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
					return nil, nil
				},
				CreateFunc: func(ctx context.Context, tx *ent.Tx, n *NodeDefinitionDTO) (*NodeDefinitionDTO, error) {
					return &NodeDefinitionDTO{}, nil
				},
				DisableNodeByTypeFunc: func(ctx context.Context, tx *ent.Tx, typeVar string) error {
					return nil
				},
			}

			nodesInstallService := NewInstallService(manifestLoader, store)
			_, err = nodesInstallService.Install(context.Background(), dir, nil, &dto.PluginDTO{})
			assert.ErrorIs(t, err, ErrNodeInfoMissing)
		})
	}
}

func TestNodesInstallService_Install_AlreadyInstalled(t *testing.T) {
	dir := t.TempDir()
	err := os.Mkdir(filepath.Join(dir, "test-node"), 0755)
	assert.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadNodeFunc: func(path string) (*Manifest, error) {
			return &Manifest{
				Name:    "test",
				Version: "1.0.0",
				Type:    "test",
			}, nil
		},
	}

	store := &Moqstore{
		GetByTypeAndVersionFunc: func(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
			return &NodeDefinitionDTO{}, nil
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, n *NodeDefinitionDTO) (*NodeDefinitionDTO, error) {
			return &NodeDefinitionDTO{}, nil
		},
		DisableNodeByTypeFunc: func(ctx context.Context, tx *ent.Tx, typeVar string) error {
			return nil
		},
	}

	nodesInstallService := NewInstallService(manifestLoader, store)
	nodeDefinitions, err := nodesInstallService.Install(context.Background(), dir, nil, &dto.PluginDTO{})
	assert.NoError(t, err)
	assert.Len(t, nodeDefinitions, 1)
}

func TestNodesInstallService_Install_DisableFailed(t *testing.T) {
	wantErr := errors.New("disable node failed")

	dir := t.TempDir()
	err := os.Mkdir(filepath.Join(dir, "test-node"), 0755)
	assert.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadNodeFunc: func(path string) (*Manifest, error) {
			return &Manifest{
				Name:    "test",
				Version: "1.0.0",
				Type:    "test",
			}, nil
		},
	}

	store := &Moqstore{
		GetByTypeAndVersionFunc: func(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
			return nil, nil
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, n *NodeDefinitionDTO) (*NodeDefinitionDTO, error) {
			return &NodeDefinitionDTO{}, nil
		},
		DisableNodeByTypeFunc: func(ctx context.Context, tx *ent.Tx, typeVar string) error {
			return wantErr
		},
	}

	nodesInstallService := NewInstallService(manifestLoader, store)
	_, err = nodesInstallService.Install(context.Background(), dir, nil, &dto.PluginDTO{})
	assert.ErrorIs(t, err, wantErr)
}

func TestNodesInstallService_Install_GetFailed(t *testing.T) {
	wantErr := errors.New("get node failed")

	dir := t.TempDir()
	err := os.Mkdir(filepath.Join(dir, "test-node"), 0755)
	assert.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadNodeFunc: func(path string) (*Manifest, error) {
			return &Manifest{
				Name:    "test",
				Version: "1.0.0",
				Type:    "test",
			}, nil
		},
	}

	store := &Moqstore{
		GetByTypeAndVersionFunc: func(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
			return nil, wantErr
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, n *NodeDefinitionDTO) (*NodeDefinitionDTO, error) {
			return &NodeDefinitionDTO{}, nil
		},
		DisableNodeByTypeFunc: func(ctx context.Context, tx *ent.Tx, typeVar string) error {
			return nil
		},
	}

	nodesInstallService := NewInstallService(manifestLoader, store)
	_, err = nodesInstallService.Install(context.Background(), dir, nil, &dto.PluginDTO{})
	assert.ErrorIs(t, err, wantErr)
}

func TestNodesInstallService_Install_CreateFailed(t *testing.T) {
	wantErr := errors.New("create node failed")

	dir := t.TempDir()
	err := os.Mkdir(filepath.Join(dir, "test-node"), 0755)
	assert.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadNodeFunc: func(path string) (*Manifest, error) {
			return &Manifest{
				Name:    "test",
				Version: "1.0.0",
				Type:    "test",
			}, nil
		},
	}

	store := &Moqstore{
		GetByTypeAndVersionFunc: func(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
			return nil, nil
		},
		CreateFunc: func(ctx context.Context, tx *ent.Tx, n *NodeDefinitionDTO) (*NodeDefinitionDTO, error) {
			return nil, wantErr
		},
		DisableNodeByTypeFunc: func(ctx context.Context, tx *ent.Tx, typeVar string) error {
			return nil
		},
	}

	nodesInstallService := NewInstallService(manifestLoader, store)
	_, err = nodesInstallService.Install(context.Background(), dir, nil, &dto.PluginDTO{})
	assert.ErrorIs(t, err, wantErr)
}
