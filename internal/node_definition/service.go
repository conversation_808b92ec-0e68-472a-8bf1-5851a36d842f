package node_definition

import (
	"context"
	v1 "resflow/proto/generated_go/v1"
	"time"
)

type NodeDefinitionDTO struct {
	ID            string          `json:"id" yaml:"id"`
	PluginName    string          `json:"plugin_name" yaml:"plugin_name"`
	PluginVersion string          `json:"plugin_version" yaml:"plugin_version"`
	Name          string          `json:"name" yaml:"name"`
	Author        string          `json:"author" yaml:"author"`
	Description   string          `json:"description" yaml:"description"`
	Icon          string          `json:"icon" yaml:"icon"`
	Type          string          `json:"type" yaml:"type"`
	Version       string          `json:"version" yaml:"version"`
	Category      string          `json:"category" yaml:"category"`
	InputParams   []*v1.NodeParam `json:"input_params" yaml:"input_params"`
	OutputParams  []*v1.NodeParam `json:"output_params" yaml:"output_params"`
	InputPorts    []*v1.NodePort  `json:"input_ports" yaml:"input_ports"`
	OutputPorts   []*v1.NodePort  `json:"output_ports" yaml:"output_ports"`
	Exception     bool            `json:"exception" yaml:"exception"`
	Path          string          `json:"path" yaml:"path"`
	Builtin       bool            `json:"builtin" yaml:"builtin"`
	Enabled       bool            `json:"enabled" yaml:"enabled"`
	CreatedAt     time.Time       `json:"created_at" yaml:"created_at"`
	UpdatedAt     time.Time       `json:"updated_at" yaml:"updated_at"`
}

type Service interface {
	List(ctx context.Context) ([]*NodeDefinitionDTO, error)
	GetByTypeAndVersion(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error)
}

type service struct {
	store store
}

func NewService(store store) Service {
	return &service{store: store}
}

func (s *service) List(ctx context.Context) ([]*NodeDefinitionDTO, error) {
	return s.store.List(ctx)
}

func (s *service) GetByTypeAndVersion(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
	return s.store.GetByTypeAndVersion(ctx, typeVar, version)
}
