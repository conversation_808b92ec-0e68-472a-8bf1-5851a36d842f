package node_definition

import "resflow/ent"

func toDTO(nodeDefinition *ent.NodeDefinition) *NodeDefinitionDTO {
	return &NodeDefinitionDTO{
		ID:            nodeDefinition.ID.String(),
		PluginName:    nodeDefinition.PluginName,
		PluginVersion: nodeDefinition.PluginVersion,
		Name:          nodeDefinition.Name,
		Author:        nodeDefinition.Author,
		Description:   nodeDefinition.Description,
		Icon:          nodeDefinition.Icon,
		Type:          nodeDefinition.Type,
		Version:       nodeDefinition.Version,
		Category:      nodeDefinition.Category,
		InputParams:   nodeDefinition.InputParams,
		OutputParams:  nodeDefinition.OutputParams,
		InputPorts:    nodeDefinition.InputPorts,
		OutputPorts:   nodeDefinition.OutputPorts,
		Path:          nodeDefinition.Path,
		Builtin:       nodeDefinition.Builtin,
		Enabled:       nodeDefinition.Enabled,
		CreatedAt:     nodeDefinition.CreatedAt,
		UpdatedAt:     nodeDefinition.UpdatedAt,
	}
}

func toDTOs(nodeDefinitions []*ent.NodeDefinition) []*NodeDefinitionDTO {
	nodeDefinitionDTOs := make([]*NodeDefinitionDTO, 0)
	for _, nodeDefinition := range nodeDefinitions {
		nodeDefinitionDTOs = append(nodeDefinitionDTOs, toDTO(nodeDefinition))
	}
	return nodeDefinitionDTOs
}
