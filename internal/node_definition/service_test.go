package node_definition

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestService_List(t *testing.T) {
	wantNodeDefinitions := []*NodeDefinitionDTO{
		{
			Name: "test",
		},
	}
	store := &Moqstore{
		ListFunc: func(ctx context.Context) ([]*NodeDefinitionDTO, error) {
			return wantNodeDefinitions, nil
		},
	}

	svc := NewService(store)
	gotNodeDefinitions, err := svc.List(context.Background())
	assert.NoError(t, err)
	assert.Equal(t, wantNodeDefinitions, gotNodeDefinitions)
}
