package node_definition

import (
	"path/filepath"
	"resflow/utils"
)

var ManifestFileName = "node.yaml"

type Manifest struct {
	Name         string       `json:"name" yaml:"name"`
	Author       string       `json:"author" yaml:"author"`
	Version      string       `json:"version" yaml:"version"`
	Description  string       `json:"description" yaml:"description"`
	Icon         string       `json:"icon" yaml:"icon"`
	Type         string       `json:"type" yaml:"type"`
	Category     string       `json:"category" yaml:"category"`
	InputParams  []*NodeParam `json:"input_params" yaml:"input_params"`
	OutputParams []*NodeParam `json:"output_params" yaml:"output_params"`
	InputPorts   []*NodePort  `json:"input_ports" yaml:"input_ports"`
	OutputPorts  []*NodePort  `json:"output_ports" yaml:"output_ports"`
	Exception    bool         `json:"exception" yaml:"exception"`
}

type ManifestLoader interface {
	LoadNode(path string) (*Manifest, error)
}

type manifestLoader struct {
}

func NewManifestLoader() ManifestLoader {
	return &manifestLoader{}
}

func (ml *manifestLoader) LoadNode(dir string) (*Manifest, error) {
	manifest := utils.NewManifest[Manifest]()
	return manifest.Load(filepath.Join(dir, ManifestFileName))
}
