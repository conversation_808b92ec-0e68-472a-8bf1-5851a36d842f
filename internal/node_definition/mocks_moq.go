// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: matryer

package node_definition

import (
	"context"
	"resflow/ent"
	"resflow/internal/dto"
	"sync"
)

// Ensure that MoqInstallService does implement InstallService.
// If this is not the case, regenerate this file with mockery.
var _ InstallService = &MoqInstallService{}

// MoqInstallService is a mock implementation of InstallService.
//
//	func TestSomethingThatUsesInstallService(t *testing.T) {
//
//		// make and configure a mocked InstallService
//		mockedInstallService := &MoqInstallService{
//			InstallFunc: func(ctx context.Context, nodesDir string, tx *ent.Tx, plugin *dto.PluginDTO) ([]*NodeDefinitionDTO, error) {
//				panic("mock out the Install method")
//			},
//		}
//
//		// use mockedInstallService in code that requires InstallService
//		// and then make assertions.
//
//	}
type MoqInstallService struct {
	// InstallFunc mocks the Install method.
	InstallFunc func(ctx context.Context, nodesDir string, tx *ent.Tx, plugin *dto.PluginDTO) ([]*NodeDefinitionDTO, error)

	// calls tracks calls to the methods.
	calls struct {
		// Install holds details about calls to the Install method.
		Install []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// NodesDir is the nodesDir argument value.
			NodesDir string
			// Tx is the tx argument value.
			Tx *ent.Tx
			// Plugin is the plugin argument value.
			Plugin *dto.PluginDTO
		}
	}
	lockInstall sync.RWMutex
}

// Install calls InstallFunc.
func (mock *MoqInstallService) Install(ctx context.Context, nodesDir string, tx *ent.Tx, plugin *dto.PluginDTO) ([]*NodeDefinitionDTO, error) {
	if mock.InstallFunc == nil {
		panic("MoqInstallService.InstallFunc: method is nil but InstallService.Install was just called")
	}
	callInfo := struct {
		Ctx      context.Context
		NodesDir string
		Tx       *ent.Tx
		Plugin   *dto.PluginDTO
	}{
		Ctx:      ctx,
		NodesDir: nodesDir,
		Tx:       tx,
		Plugin:   plugin,
	}
	mock.lockInstall.Lock()
	mock.calls.Install = append(mock.calls.Install, callInfo)
	mock.lockInstall.Unlock()
	return mock.InstallFunc(ctx, nodesDir, tx, plugin)
}

// InstallCalls gets all the calls that were made to Install.
// Check the length with:
//
//	len(mockedInstallService.InstallCalls())
func (mock *MoqInstallService) InstallCalls() []struct {
	Ctx      context.Context
	NodesDir string
	Tx       *ent.Tx
	Plugin   *dto.PluginDTO
} {
	var calls []struct {
		Ctx      context.Context
		NodesDir string
		Tx       *ent.Tx
		Plugin   *dto.PluginDTO
	}
	mock.lockInstall.RLock()
	calls = mock.calls.Install
	mock.lockInstall.RUnlock()
	return calls
}

// Ensure that MoqManifestLoader does implement ManifestLoader.
// If this is not the case, regenerate this file with mockery.
var _ ManifestLoader = &MoqManifestLoader{}

// MoqManifestLoader is a mock implementation of ManifestLoader.
//
//	func TestSomethingThatUsesManifestLoader(t *testing.T) {
//
//		// make and configure a mocked ManifestLoader
//		mockedManifestLoader := &MoqManifestLoader{
//			LoadNodeFunc: func(path string) (*Manifest, error) {
//				panic("mock out the LoadNode method")
//			},
//		}
//
//		// use mockedManifestLoader in code that requires ManifestLoader
//		// and then make assertions.
//
//	}
type MoqManifestLoader struct {
	// LoadNodeFunc mocks the LoadNode method.
	LoadNodeFunc func(path string) (*Manifest, error)

	// calls tracks calls to the methods.
	calls struct {
		// LoadNode holds details about calls to the LoadNode method.
		LoadNode []struct {
			// Path is the path argument value.
			Path string
		}
	}
	lockLoadNode sync.RWMutex
}

// LoadNode calls LoadNodeFunc.
func (mock *MoqManifestLoader) LoadNode(path string) (*Manifest, error) {
	if mock.LoadNodeFunc == nil {
		panic("MoqManifestLoader.LoadNodeFunc: method is nil but ManifestLoader.LoadNode was just called")
	}
	callInfo := struct {
		Path string
	}{
		Path: path,
	}
	mock.lockLoadNode.Lock()
	mock.calls.LoadNode = append(mock.calls.LoadNode, callInfo)
	mock.lockLoadNode.Unlock()
	return mock.LoadNodeFunc(path)
}

// LoadNodeCalls gets all the calls that were made to LoadNode.
// Check the length with:
//
//	len(mockedManifestLoader.LoadNodeCalls())
func (mock *MoqManifestLoader) LoadNodeCalls() []struct {
	Path string
} {
	var calls []struct {
		Path string
	}
	mock.lockLoadNode.RLock()
	calls = mock.calls.LoadNode
	mock.lockLoadNode.RUnlock()
	return calls
}

// Ensure that Moqstore does implement store.
// If this is not the case, regenerate this file with mockery.
var _ store = &Moqstore{}

// Moqstore is a mock implementation of store.
//
//	func TestSomethingThatUsesstore(t *testing.T) {
//
//		// make and configure a mocked store
//		mockedstore := &Moqstore{
//			CreateFunc: func(ctx context.Context, tx *ent.Tx, nodeDefinition *NodeDefinitionDTO) (*NodeDefinitionDTO, error) {
//				panic("mock out the Create method")
//			},
//			DisableNodeByTypeFunc: func(ctx context.Context, tx *ent.Tx, typeVar string) error {
//				panic("mock out the DisableNodeByType method")
//			},
//			GetByTypeAndVersionFunc: func(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
//				panic("mock out the GetByTypeAndVersion method")
//			},
//			ListFunc: func(ctx context.Context) ([]*NodeDefinitionDTO, error) {
//				panic("mock out the List method")
//			},
//		}
//
//		// use mockedstore in code that requires store
//		// and then make assertions.
//
//	}
type Moqstore struct {
	// CreateFunc mocks the Create method.
	CreateFunc func(ctx context.Context, tx *ent.Tx, nodeDefinition *NodeDefinitionDTO) (*NodeDefinitionDTO, error)

	// DisableNodeByTypeFunc mocks the DisableNodeByType method.
	DisableNodeByTypeFunc func(ctx context.Context, tx *ent.Tx, typeVar string) error

	// GetByTypeAndVersionFunc mocks the GetByTypeAndVersion method.
	GetByTypeAndVersionFunc func(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error)

	// ListFunc mocks the List method.
	ListFunc func(ctx context.Context) ([]*NodeDefinitionDTO, error)

	// calls tracks calls to the methods.
	calls struct {
		// Create holds details about calls to the Create method.
		Create []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// NodeDefinition is the nodeDefinition argument value.
			NodeDefinition *NodeDefinitionDTO
		}
		// DisableNodeByType holds details about calls to the DisableNodeByType method.
		DisableNodeByType []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *ent.Tx
			// TypeVar is the typeVar argument value.
			TypeVar string
		}
		// GetByTypeAndVersion holds details about calls to the GetByTypeAndVersion method.
		GetByTypeAndVersion []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// TypeVar is the typeVar argument value.
			TypeVar string
			// Version is the version argument value.
			Version string
		}
		// List holds details about calls to the List method.
		List []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
		}
	}
	lockCreate              sync.RWMutex
	lockDisableNodeByType   sync.RWMutex
	lockGetByTypeAndVersion sync.RWMutex
	lockList                sync.RWMutex
}

// Create calls CreateFunc.
func (mock *Moqstore) Create(ctx context.Context, tx *ent.Tx, nodeDefinition *NodeDefinitionDTO) (*NodeDefinitionDTO, error) {
	if mock.CreateFunc == nil {
		panic("Moqstore.CreateFunc: method is nil but store.Create was just called")
	}
	callInfo := struct {
		Ctx            context.Context
		Tx             *ent.Tx
		NodeDefinition *NodeDefinitionDTO
	}{
		Ctx:            ctx,
		Tx:             tx,
		NodeDefinition: nodeDefinition,
	}
	mock.lockCreate.Lock()
	mock.calls.Create = append(mock.calls.Create, callInfo)
	mock.lockCreate.Unlock()
	return mock.CreateFunc(ctx, tx, nodeDefinition)
}

// CreateCalls gets all the calls that were made to Create.
// Check the length with:
//
//	len(mockedstore.CreateCalls())
func (mock *Moqstore) CreateCalls() []struct {
	Ctx            context.Context
	Tx             *ent.Tx
	NodeDefinition *NodeDefinitionDTO
} {
	var calls []struct {
		Ctx            context.Context
		Tx             *ent.Tx
		NodeDefinition *NodeDefinitionDTO
	}
	mock.lockCreate.RLock()
	calls = mock.calls.Create
	mock.lockCreate.RUnlock()
	return calls
}

// DisableNodeByType calls DisableNodeByTypeFunc.
func (mock *Moqstore) DisableNodeByType(ctx context.Context, tx *ent.Tx, typeVar string) error {
	if mock.DisableNodeByTypeFunc == nil {
		panic("Moqstore.DisableNodeByTypeFunc: method is nil but store.DisableNodeByType was just called")
	}
	callInfo := struct {
		Ctx     context.Context
		Tx      *ent.Tx
		TypeVar string
	}{
		Ctx:     ctx,
		Tx:      tx,
		TypeVar: typeVar,
	}
	mock.lockDisableNodeByType.Lock()
	mock.calls.DisableNodeByType = append(mock.calls.DisableNodeByType, callInfo)
	mock.lockDisableNodeByType.Unlock()
	return mock.DisableNodeByTypeFunc(ctx, tx, typeVar)
}

// DisableNodeByTypeCalls gets all the calls that were made to DisableNodeByType.
// Check the length with:
//
//	len(mockedstore.DisableNodeByTypeCalls())
func (mock *Moqstore) DisableNodeByTypeCalls() []struct {
	Ctx     context.Context
	Tx      *ent.Tx
	TypeVar string
} {
	var calls []struct {
		Ctx     context.Context
		Tx      *ent.Tx
		TypeVar string
	}
	mock.lockDisableNodeByType.RLock()
	calls = mock.calls.DisableNodeByType
	mock.lockDisableNodeByType.RUnlock()
	return calls
}

// GetByTypeAndVersion calls GetByTypeAndVersionFunc.
func (mock *Moqstore) GetByTypeAndVersion(ctx context.Context, typeVar string, version string) (*NodeDefinitionDTO, error) {
	if mock.GetByTypeAndVersionFunc == nil {
		panic("Moqstore.GetByTypeAndVersionFunc: method is nil but store.GetByTypeAndVersion was just called")
	}
	callInfo := struct {
		Ctx     context.Context
		TypeVar string
		Version string
	}{
		Ctx:     ctx,
		TypeVar: typeVar,
		Version: version,
	}
	mock.lockGetByTypeAndVersion.Lock()
	mock.calls.GetByTypeAndVersion = append(mock.calls.GetByTypeAndVersion, callInfo)
	mock.lockGetByTypeAndVersion.Unlock()
	return mock.GetByTypeAndVersionFunc(ctx, typeVar, version)
}

// GetByTypeAndVersionCalls gets all the calls that were made to GetByTypeAndVersion.
// Check the length with:
//
//	len(mockedstore.GetByTypeAndVersionCalls())
func (mock *Moqstore) GetByTypeAndVersionCalls() []struct {
	Ctx     context.Context
	TypeVar string
	Version string
} {
	var calls []struct {
		Ctx     context.Context
		TypeVar string
		Version string
	}
	mock.lockGetByTypeAndVersion.RLock()
	calls = mock.calls.GetByTypeAndVersion
	mock.lockGetByTypeAndVersion.RUnlock()
	return calls
}

// List calls ListFunc.
func (mock *Moqstore) List(ctx context.Context) ([]*NodeDefinitionDTO, error) {
	if mock.ListFunc == nil {
		panic("Moqstore.ListFunc: method is nil but store.List was just called")
	}
	callInfo := struct {
		Ctx context.Context
	}{
		Ctx: ctx,
	}
	mock.lockList.Lock()
	mock.calls.List = append(mock.calls.List, callInfo)
	mock.lockList.Unlock()
	return mock.ListFunc(ctx)
}

// ListCalls gets all the calls that were made to List.
// Check the length with:
//
//	len(mockedstore.ListCalls())
func (mock *Moqstore) ListCalls() []struct {
	Ctx context.Context
} {
	var calls []struct {
		Ctx context.Context
	}
	mock.lockList.RLock()
	calls = mock.calls.List
	mock.lockList.RUnlock()
	return calls
}
