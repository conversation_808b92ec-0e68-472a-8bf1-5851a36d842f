package configs

import (
	"fmt"
	"github.com/spf13/viper"
)

type Config struct {
	AppConfig      `mapstructure:"app"`
	DatabaseConfig `mapstructure:"database"`
}

var ParsedConfig Config

func init() {
	viper.AddConfigPath("./")
	viper.AddConfigPath("../")
	viper.AddConfigPath("../../")
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")

	ReadConfig()

	viper.WatchConfig()
}

func ReadConfig() {
	if err := viper.ReadInConfig(); err != nil {
		panic(fmt.Errorf("读取配置文件失败, %s", err))
	}

	if err := viper.Unmarshal(&ParsedConfig); err != nil {
		panic(fmt.Errorf("解析配置失败, %s", err))
	}
}
