package utils

import (
	"errors"
	"gopkg.in/yaml.v3"
	"os"
)

var ErrIsDir = errors.New("path is a directory")

type Manifest[T any] interface {
	Load(path string) (*T, error)
}

type manifest[T any] struct {
}

func NewManifest[T any]() Manifest[T] {
	return &manifest[T]{}
}

func (m manifest[T]) Load(manifestFilePath string) (*T, error) {
	// 判断清单文件是否存在
	manifestFileInfo, err := os.Stat(manifestFilePath)
	if err != nil {
		return nil, err
	}
	if manifestFileInfo.IsDir() {
		return nil, ErrIsDir
	}
	// 读取清单文件内容
	manifestFileContent, err := os.ReadFile(manifestFilePath)
	if err != nil {
		return nil, err
	}

	pluginManifest := new(T)
	if err := yaml.Unmarshal(manifestFileContent, pluginManifest); err != nil {
		return nil, err
	}
	return pluginManifest, nil
}
