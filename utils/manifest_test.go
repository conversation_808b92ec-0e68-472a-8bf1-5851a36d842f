package utils

import (
	"github.com/stretchr/testify/assert"
	"os"
	"path/filepath"
	"testing"
)

type manifestType struct {
	Name    string `json:"name" yaml:"name"`
	Version string `json:"version" yaml:"version"`
}

func TestManifest_Load_Success(t *testing.T) {
	dir := t.TempDir()

	content := `
name: test
version: 1.0.0
`
	filePath := filepath.Join(dir, "manifest.yaml")
	err := os.WriteFile(filePath, []byte(content), 0644)
	assert.NoError(t, err)

	manifest := NewManifest[manifestType]()
	manifestData, err := manifest.Load(filePath)
	assert.NoError(t, err)
	assert.Equal(t, "test", manifestData.Name)
	assert.Equal(t, "1.0.0", manifestData.Version)
}

func TestManifest_Load_FileNotExists(t *testing.T) {
	dir := t.TempDir()

	manifest := NewManifest[manifestType]()
	_, err := manifest.Load(filepath.Join(dir, "manifest.yaml"))
	assert.ErrorIs(t, err, os.ErrNotExist)
}

func TestManifest_Load_FileIsDir(t *testing.T) {
	dir := t.TempDir()

	manifest := NewManifest[manifestType]()
	_, err := manifest.Load(dir)
	assert.ErrorIs(t, err, ErrIsDir)
}

func TestManifest_Load_InvalidManifest(t *testing.T) {
	dir := t.TempDir()

	content := `
some error text
name: test
version: 1.0.0
`
	filePath := filepath.Join(dir, "manifest.yaml")
	err := os.WriteFile(filePath, []byte(content), 0644)
	assert.NoError(t, err)

	manifest := NewManifest[manifestType]()
	_, err = manifest.Load(filePath)
	assert.Error(t, err)
}

func TestManifest_Load_ReadFileError(t *testing.T) {
	dir := t.TempDir()

	content := `
name: test
version: 1.0.0
`
	filePath := filepath.Join(dir, "manifest.yaml")
	err := os.WriteFile(filePath, []byte(content), 0222)
	assert.NoError(t, err)

	manifest := NewManifest[manifestType]()
	_, err = manifest.Load(filePath)
	assert.ErrorIs(t, err, os.ErrPermission)
}
