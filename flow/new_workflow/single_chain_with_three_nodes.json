{"name": "single chain with three nodes", "description": "for test", "author": "test", "nodes": [{"id": "node1", "icon": "icon", "name": "node1", "description": "node1", "nodeType": "trigger", "version": "1", "inputs": [{"id": "triggerWay", "label": "triggerWay", "type": "any", "description": "triggerWay", "value": [{"trigger_type": "cron", "cron_expression": "* * * * *"}, {"trigger_type": "subscription", "type": "all", "media_info": {"type": "movie", "douban_id": 123}}, {"trigger_type": "event", "event_type": "webhook"}]}], "outputs": [{"id": "trigger_time", "label": "触发时间", "description": "如果实际是定时触发，返回触发时间", "type": "time", "value": ""}, {"id": "trigger_event", "label": "触发事件", "description": "如果实际是事件触发，返回触发事件类型", "type": "string", "value": ""}, {"id": "trigger_subscription", "label": "触发订阅", "description": "如果实际是订阅触发，返回订阅的媒体", "type": "any", "value": ""}]}, {"id": "node2", "name": "node2", "icon": "icon", "description": "node2", "nodeType": "test", "version": "1", "inputs": [{"id": "text", "label": "text", "type": "string", "value": "text"}, {"id": "number", "label": "number", "type": "number", "value": 1234}], "outputs": []}, {"id": "node3", "name": "node3", "icon": "icon", "description": "node3", "nodeType": "end", "version": "1", "inputs": [], "outputs": []}], "edges": [{"from": "node1", "to": "node2", "fromPort": "", "toPort": ""}, {"from": "node2", "to": "node3", "fromPort": "", "toPort": ""}]}