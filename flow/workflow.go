package flow

import (
	"encoding/json"
	"resflow/utils"
)

type Workflow struct {
	Name        string          `json:"name"`
	Description string          `json:"description"`
	Author      string          `json:"author"`
	Nodes       []*WorkflowNode `json:"nodes"`
	Edges       []*WorkflowEdge `json:"edges"`
}

// NewWorkflow 创建工作流，根据节点数组和边数组创建工作流对象
func NewWorkflow(name, description, author string, nodes []*WorkflowNode, edges []*WorkflowEdge) *Workflow {
	return &Workflow{
		Name:        name,
		Description: description,
		Author:      author,
		Nodes:       nodes,
		Edges:       edges,
	}
}

// NewWorkflowFromJSON 根据JSON创建Workflow对象
func NewWorkflowFromJSON(jsonStr string) *Workflow {
	workflow := new(Workflow)
	err := json.Unmarshal([]byte(jsonStr), &workflow)
	if err != nil {
		utils.Logger.Error("解析workflow json数据失败：", err)
		return nil
	}
	// 处理节点数据
	nodes := make([]*WorkflowNode, 0)
	if workflow.Nodes != nil {
		for _, node := range workflow.Nodes {
			n, err := NewWorkflowNode(node.ID, node.Icon, node.Name, node.Description, node.NodeType, node.Version, node.Inputs, node.Outputs, node.Ports)
			if err != nil {
				utils.Logger.Error("节点加载失败：", err)
				continue
			}
			nodes = append(nodes, n)
		}
	}
	workflow.Nodes = nodes
	// 处理边数据
	edges := make([]*WorkflowEdge, 0)
	if workflow.Edges != nil {
		for _, edge := range workflow.Edges {
			e := NewWorkflowEdge(nodes, edge.From, edge.To, edge.FromPort, edge.ToPort)
			edges = append(edges, e)
		}
	}
	workflow.Edges = edges
	return workflow
}

// JSON 根据当前工作流对象生成JSON
func (w *Workflow) JSON() (string, error) {
	return "", nil
}

// GetNodeMap 获取当前工作流对象的节点映射，根据ID映射
func (w *Workflow) GetNodeMap() map[string]*WorkflowNode {
	nodeMap := make(map[string]*WorkflowNode)
	for _, node := range w.Nodes {
		nodeMap[node.ID] = node
	}
	return nodeMap
}

// GetNodeById 根据NodeId查找节点
func (w *Workflow) GetNodeById(id string) *WorkflowNode {
	for _, node := range w.Nodes {
		if node.ID == id {
			return node
		}
	}
	return nil
}
