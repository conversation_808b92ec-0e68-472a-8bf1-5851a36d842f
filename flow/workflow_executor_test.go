package flow

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestWorkflowExecutor_Execute(t *testing.T) {
	cases := []struct {
		CaseDoc    string
		Workflow   *Workflow
		WantOutput string
		WantError  error
	}{
		{
			CaseDoc: "单链遍历",
			Workflow: &Workflow{
				Nodes: []*WorkflowNode{
					{
						ID: "node1",
					},
					{
						ID: "node2",
					},
					{
						ID: "node3",
					},
				},
				Edges: []*WorkflowEdge{
					{
						From: "node1",
						To:   "node2",
					}, {
						From: "node2",
						To:   "node3",
					},
				},
			},
			WantOutput: "node1node2node3",
			WantError:  nil,
		}, {
			CaseDoc: "双链遍历",
			Workflow: &Workflow{
				Nodes: []*WorkflowNode{
					{
						ID: "node1",
					}, {
						ID: "node2",
					}, {
						ID: "node3",
					}, {
						ID: "node4",
					}, {
						ID: "node5",
					}, {
						ID: "node6",
					},
				},
				Edges: []*WorkflowEdge{
					{
						From: "node1",
						To:   "node2",
					}, {
						From: "node1",
						To:   "node3",
					}, {
						From: "node2",
						To:   "node4",
					}, {
						From: "node3",
						To:   "node5",
					}, {
						From: "node4",
						To:   "node6",
					}, {
						From: "node5",
						To:   "node6",
					},
				},
			},
			WantOutput: "node1node2node3node4node5node6node6",
			WantError:  nil,
		}, {
			CaseDoc: "双链多起始节点遍历",
			Workflow: &Workflow{
				Nodes: []*WorkflowNode{
					{
						ID: "node1",
					}, {
						ID: "node2",
					}, {
						ID: "node3",
					}, {
						ID: "node4",
					}, {
						ID: "node5",
					}, {
						ID: "node6",
					}, {
						ID: "node7",
					},
				},
				Edges: []*WorkflowEdge{
					{
						From: "node1",
						To:   "node2",
					}, {
						From: "node3",
						To:   "node5",
					}, {
						From: "node2",
						To:   "node4",
					}, {
						From: "node5",
						To:   "node7",
					}, {
						From: "node4",
						To:   "node6",
					}, {
						From: "node7",
						To:   "node4",
					},
				},
			},
			WantOutput: "node1node2node3node4node5node6node6",
			WantError:  nil,
		}, {
			CaseDoc: "带判断分支",
			Workflow: &Workflow{
				Nodes: []*WorkflowNode{
					{
						ID:       "node1",
						NodeType: NodeTypeEcho,
						Inputs: []*NodeParam{
							{
								ID:    "input1",
								Type:  "string",
								Value: "value1",
							},
						},
						Outputs: []*NodeParam{
							{
								ID:          "result",
								Label:       "result",
								Type:        "string",
								Description: "result",
							},
						},
					}, {
						ID:       "node2",
						NodeType: NodeTypeCondition,
						Inputs: []*NodeParam{
							{
								ID:          "condition",
								Type:        "any",
								Label:       "条件",
								Description: "判断的条件",
								Value: map[string]interface{}{
									"operator": "AND",
									"conditions": []map[string]interface{}{
										{
											"operator": "equal",
											"value":    "input1=value1",
											"variables": []string{
												"node1",
												"result",
											},
										},
									},
								},
							},
						},
						Ports: []*NodePort{
							{
								ID: "if",
							}, {
								ID: "else",
							},
						},
					}, {
						ID:       "node3",
						NodeType: NodeTypeEcho,
						Inputs: []*NodeParam{
							{
								ID:    "input1",
								Type:  "string",
								Value: "if",
							},
						},
					}, {
						ID:       "node4",
						NodeType: NodeTypeEcho,
						Inputs: []*NodeParam{
							{
								ID:    "input1",
								Type:  "string",
								Value: "else",
							},
						},
					},
				},
				Edges: []*WorkflowEdge{
					{
						From: "node1",
						To:   "node2",
					}, {
						From:     "node2",
						To:       "node3",
						FromPort: "if",
					}, {
						From:     "node2",
						To:       "node4",
						FromPort: "else",
					},
				},
			},
		}, {
			CaseDoc: "多判断分支",
			Workflow: &Workflow{
				Nodes: []*WorkflowNode{
					{
						ID:       "node1",
						NodeType: NodeTypeEcho,
						Inputs: []*NodeParam{
							{
								ID:    "input1",
								Type:  "string",
								Value: "value1",
							},
						},
						Outputs: []*NodeParam{
							{
								ID:          "result",
								Label:       "result",
								Type:        "string",
								Description: "result",
							},
						},
					}, {
						ID:       "node2",
						NodeType: NodeTypeCondition,
						Inputs: []*NodeParam{
							{
								ID:          "condition",
								Type:        "any",
								Label:       "条件",
								Description: "判断的条件",
								Value: map[string]interface{}{
									"operator": "AND",
									"conditions": []map[string]interface{}{
										{
											"operator": "equal",
											"value":    "input1=value1",
											"variables": []string{
												"node1",
												"result",
											},
										},
									},
								},
							},
						},
						Ports: []*NodePort{
							{
								ID: "if",
							}, {
								ID: "else",
							},
						},
					}, {
						ID:       "node3",
						NodeType: NodeTypeEcho,
						Inputs: []*NodeParam{
							{
								ID:    "input1",
								Type:  "string",
								Value: "if",
							},
						},
					}, {
						ID:       "node4",
						NodeType: NodeTypeEcho,
						Inputs: []*NodeParam{
							{
								ID:    "input1",
								Type:  "string",
								Value: "else",
							},
						},
					}, {
						ID:       "node5",
						NodeType: NodeTypeCondition,
						Inputs: []*NodeParam{
							{
								ID:          "condition",
								Type:        "any",
								Label:       "条件",
								Description: "判断的条件",
								Value: map[string]interface{}{
									"operator": "AND",
									"conditions": []map[string]interface{}{
										{
											"operator": "equal",
											"value":    "not equal",
											"variables": []string{
												"node1",
												"result",
											},
										},
									},
								},
							},
						},
						Ports: []*NodePort{
							{
								ID: "if",
							}, {
								ID: "else",
							},
						},
					}, {
						ID:       "node6",
						NodeType: NodeTypeEcho,
						Inputs: []*NodeParam{
							{
								ID:    "input1",
								Type:  "string",
								Value: "if",
							},
						},
					}, {
						ID:       "node7",
						NodeType: NodeTypeEcho,
						Inputs: []*NodeParam{
							{
								ID:    "input1",
								Type:  "string",
								Value: "else",
							},
						},
					},
				},
				Edges: []*WorkflowEdge{
					{
						From: "node1",
						To:   "node2",
					}, {
						From:     "node2",
						To:       "node3",
						FromPort: "if",
					}, {
						From:     "node2",
						To:       "node4",
						FromPort: "else",
					}, {
						From: "node1",
						To:   "node5",
					}, {
						From:     "node5",
						To:       "node6",
						FromPort: "if",
					}, {
						From:     "node5",
						To:       "node7",
						FromPort: "else",
					},
				},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			executor := NewWorkflowExecutor(tc.Workflow)
			err := executor.Execute()
			t.Log(err)
		})
	}
}

func TestWorkflowExecutor_GetEndNodeCount(t *testing.T) {
	cases := []struct {
		CaseDoc  string
		Workflow *Workflow
		Want     int
	}{
		{
			CaseDoc: "单个结束节点",
			Workflow: &Workflow{
				Nodes: []*WorkflowNode{
					{
						ID: "node1",
					}, {
						ID: "node2",
					}, {
						ID: "node3",
					},
				},
				Edges: []*WorkflowEdge{
					{
						From: "node1",
						To:   "node2",
					}, {
						From: "node2",
						To:   "node3",
					},
				},
			},
			Want: 1,
		}, {
			CaseDoc: "多个结束节点",
			Workflow: &Workflow{
				Nodes: []*WorkflowNode{
					{
						ID: "node1",
					}, {
						ID: "node2",
					}, {
						ID: "node3",
					}, {
						ID: "node4",
					},
				},
				Edges: []*WorkflowEdge{
					{
						From: "node1",
						To:   "node2",
					}, {
						From: "node3",
						To:   "node4",
					},
				},
			},
			Want: 2,
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			executor := NewWorkflowExecutor(tc.Workflow)
			got := executor.getEndNodeCount()
			assert.Equal(t, tc.Want, got)
		})
	}
}

func TestWorkflowExecutor_GetStartNodes(t *testing.T) {
	cases := []struct {
		CaseDoc          string
		WorkflowExecutor *WorkflowExecutor
		WantNodes        []*WorkflowNode
	}{
		{
			CaseDoc: "获取起始节点",
			WorkflowExecutor: &WorkflowExecutor{
				Graph: NewWorkflowGraph(&Workflow{
					Nodes: []*WorkflowNode{
						{
							ID: "node1",
						}, {
							ID: "node2",
						}, {
							ID: "node3",
						},
					},
					Edges: []*WorkflowEdge{
						{
							From: "node1",
							To:   "node2",
						}, {
							From: "node2",
							To:   "node3",
						},
					},
				}),
				NodeExecutors: &NodeExecutorsMap{
					executors: map[string]NodeExecutor{},
				},
			},
			WantNodes: []*WorkflowNode{
				{
					ID: "node1",
				},
			},
		}, {
			CaseDoc: "获取第一个未完成的节点",
			WorkflowExecutor: &WorkflowExecutor{
				Graph: NewWorkflowGraph(&Workflow{
					Nodes: []*WorkflowNode{
						{
							ID: "node1",
						}, {
							ID: "node2",
						}, {
							ID: "node3",
						},
					},
					Edges: []*WorkflowEdge{
						{
							From: "node1",
							To:   "node2",
						}, {
							From: "node2",
							To:   "node3",
						},
					},
				}),
				NodeExecutors: &NodeExecutorsMap{
					executors: map[string]NodeExecutor{
						"node1": &WorkflowNodeExecutor{
							Node: &WorkflowNode{
								ID: "node1",
							},
							State: NodeCompleted,
						},
					},
				},
			},
			WantNodes: []*WorkflowNode{
				{
					ID: "node2",
				},
			},
		}, {
			CaseDoc: "获取两个未完成的节点",
			WorkflowExecutor: &WorkflowExecutor{
				Graph: NewWorkflowGraph(&Workflow{
					Nodes: []*WorkflowNode{
						{
							ID: "node1",
						}, {
							ID: "node2",
						}, {
							ID: "node3",
						}, {
							ID: "node4",
						},
					},
					Edges: []*WorkflowEdge{
						{
							From: "node1",
							To:   "node2",
						}, {
							From: "node2",
							To:   "node3",
						}, {
							From: "node4",
							To:   "node3",
						},
					},
				}),
				NodeExecutors: &NodeExecutorsMap{
					executors: map[string]NodeExecutor{
						"node1": &WorkflowNodeExecutor{
							Node: &WorkflowNode{
								ID: "node1",
							},
							State: NodeCompleted,
						},
					},
				},
			},
			WantNodes: []*WorkflowNode{
				{
					ID: "node4",
				}, {
					ID: "node2",
				},
			},
		}, {
			CaseDoc: "有节点被取消运行",
			WorkflowExecutor: &WorkflowExecutor{
				Graph: NewWorkflowGraph(&Workflow{
					Nodes: []*WorkflowNode{
						{
							ID: "node1",
						}, {
							ID: "node2",
						}, {
							ID: "node3",
						}, {
							ID: "node4",
						},
					},
					Edges: []*WorkflowEdge{
						{
							From: "node1",
							To:   "node2",
						}, {
							From: "node2",
							To:   "node3",
						}, {
							From: "node4",
							To:   "node3",
						},
					},
				}),
				NodeExecutors: &NodeExecutorsMap{
					executors: map[string]NodeExecutor{
						"node1": &WorkflowNodeExecutor{
							Node: &WorkflowNode{
								ID: "node1",
							},
							State: NodeCompleted,
						},
						"node2": &WorkflowNodeExecutor{
							Node: &WorkflowNode{
								ID: "node2",
							},
							State: NodeCanceled,
						},
					},
				},
			},
			WantNodes: []*WorkflowNode{
				{
					ID: "node4",
				}, {
					ID: "node2",
				},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			gotNodes := tc.WorkflowExecutor.getStartNodes()
			assert.Equal(t, tc.WantNodes, gotNodes)
		})
	}
}

func TestWorkflowExecutor_IsInDegreeNodesDone(t *testing.T) {
	cases := []struct {
		CaseDoc          string
		WorkflowExecutor *WorkflowExecutor
		NodeId           string
		Want             bool
	}{
		{
			CaseDoc: "依赖的节点已完成",
			WorkflowExecutor: &WorkflowExecutor{
				Graph: NewWorkflowGraph(&Workflow{
					Nodes: []*WorkflowNode{
						{
							ID: "node1",
						}, {
							ID: "node2",
						}, {
							ID: "node3",
						},
					},
					Edges: []*WorkflowEdge{
						{
							From: "node1",
							To:   "node2",
						}, {
							From: "node2",
							To:   "node3",
						},
					},
				}),
				NodeExecutors: &NodeExecutorsMap{
					executors: map[string]NodeExecutor{
						"node1": &WorkflowNodeExecutor{
							Node: &WorkflowNode{
								ID: "node1",
							},
							State: NodeCompleted,
						},
					},
				},
			},
			NodeId: "node1",
			Want:   true,
		}, {
			CaseDoc: "多个依赖节点已完成",
			WorkflowExecutor: &WorkflowExecutor{
				Graph: NewWorkflowGraph(&Workflow{
					Nodes: []*WorkflowNode{
						{
							ID: "node1",
						}, {
							ID: "node2",
						}, {
							ID: "node3",
						},
					},
					Edges: []*WorkflowEdge{
						{
							From: "node1",
							To:   "node3",
						}, {
							From: "node2",
							To:   "node3",
						},
					},
				}),
				NodeExecutors: &NodeExecutorsMap{
					executors: map[string]NodeExecutor{
						"node1": &WorkflowNodeExecutor{
							Node: &WorkflowNode{
								ID: "node1",
							},
							State: NodeCompleted,
						},
						"node2": &WorkflowNodeExecutor{
							Node: &WorkflowNode{
								ID: "node2",
							},
							State: NodeCompleted,
						},
					},
				},
			},
			NodeId: "node3",
			Want:   true,
		}, {
			CaseDoc: "依赖节点未完成",
			WorkflowExecutor: &WorkflowExecutor{
				Graph: NewWorkflowGraph(&Workflow{
					Nodes: []*WorkflowNode{
						{
							ID: "node1",
						}, {
							ID: "node2",
						},
					},
					Edges: []*WorkflowEdge{
						{
							From: "node1",
							To:   "node2",
						},
					},
				}),
				NodeExecutors: &NodeExecutorsMap{
					executors: map[string]NodeExecutor{},
				},
			},
			NodeId: "node2",
			Want:   false,
		}, {
			CaseDoc: "多个依赖节点部分完成",
			WorkflowExecutor: &WorkflowExecutor{
				Graph: NewWorkflowGraph(&Workflow{
					Nodes: []*WorkflowNode{
						{
							ID: "node1",
						}, {
							ID: "node2",
						}, {
							ID: "node3",
						},
					},
					Edges: []*WorkflowEdge{
						{
							From: "node1",
							To:   "node3",
						}, {
							From: "node2",
							To:   "node3",
						},
					},
				}),
				NodeExecutors: &NodeExecutorsMap{
					executors: map[string]NodeExecutor{
						"node1": &WorkflowNodeExecutor{
							Node: &WorkflowNode{
								ID: "node1",
							},
							State: NodeCompleted,
						},
						"node2": &WorkflowNodeExecutor{
							Node: &WorkflowNode{
								ID: "node2",
							},
							State: NodePending,
						},
					},
				},
			},
			NodeId: "node3",
			Want:   false,
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			got := tc.WorkflowExecutor.IsInDegreeNodesDone(tc.NodeId)
			assert.Equal(t, tc.Want, got)
		})
	}
}

func TestWorkflowExecutor_executeNode(t *testing.T) {

}

func TestWorkflowExecutor_Pause(t *testing.T) {

}
