package flow

import (
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
)

func TestNewWorkflowFromJSONSuccess(t *testing.T) {
	nodes := []*WorkflowNode{
		{
			ID:          "node1",
			Icon:        "icon",
			Name:        "node1",
			Description: "node1",
			NodeType:    "trigger",
			Version:     "1",
			Inputs: []*NodeParam{
				{
					ID:          "triggerWay",
					Label:       "triggerWay",
					Type:        "any",
					Description: "triggerWay",
					Value: []map[string]interface{}{
						{
							"trigger_type":    "cron",
							"cron_expression": "* * * * *",
						}, {
							"trigger_type": "subscription",
							"type":         "all",
							"media_info": map[string]interface{}{
								"type":      "movie",
								"douban_id": float64(123),
							},
						}, {
							"trigger_type": "event",
							"event_type":   "webhook",
						},
					},
				},
			},
			Outputs: []*NodeParam{
				{
					ID:          "trigger_time",
					Label:       "触发时间",
					Description: "如果实际是定时触发，返回触发时间",
					Type:        "time",
					Value:       "",
				},
				{
					ID:          "trigger_event",
					Label:       "触发事件",
					Description: "如果实际是事件触发，返回触发事件类型",
					Type:        "string",
					Value:       "",
				},
				{
					ID:          "trigger_subscription",
					Label:       "触发订阅",
					Description: "如果实际是订阅触发，返回订阅的媒体",
					Type:        "any",
					Value:       "",
				},
			},
			Ports:  nil,
			Params: nil,
		}, {
			ID:          "node2",
			Icon:        "icon",
			Name:        "node2",
			Description: "node2",
			NodeType:    "test",
			Version:     "1",
			Inputs: []*NodeParam{
				{
					ID:    "text",
					Label: "text",
					Type:  "string",
					Value: "text",
				},
				{
					ID:    "number",
					Label: "number",
					Type:  "number",
					Value: float64(1234),
				},
			},
			Outputs: make([]*NodeParam, 0),
			Ports:   nil,
		}, {
			ID:          "node3",
			Icon:        "icon",
			Name:        "node3",
			Description: "node3",
			NodeType:    "end",
			Version:     "1",
			Inputs:      make([]*NodeParam, 0),
			Outputs:     make([]*NodeParam, 0),
			Ports:       nil,
		},
	}

	edges := []*WorkflowEdge{
		{
			From:     "node1",
			To:       "node2",
			FromPort: "",
			ToPort:   "",
		},
		{
			From:     "node2",
			To:       "node3",
			FromPort: "",
			ToPort:   "",
		},
	}

	wantWorkflow := &Workflow{
		Name:        "single chain with three nodes",
		Description: "for test",
		Author:      "test",
		Nodes:       nodes,
		Edges:       edges,
	}

	jsonData, err := os.ReadFile("./new_workflow/single_chain_with_three_nodes.json")
	if err != nil {
		t.Fatal(err)
	}

	gotWorkflow := NewWorkflowFromJSON(string(jsonData))
	assert.Equal(t, wantWorkflow, gotWorkflow)
}
