package flow

import (
	"bytes"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestEchoNodeTask_Run(t *testing.T) {
	cases := []struct {
		CaseDoc string
		Inputs  map[string]interface{}
		Want    string
		WantErr error
	}{
		{
			CaseDoc: "单个",
			Inputs: map[string]interface{}{
				"key": "value",
			},
			Want:    "key=value",
			WantErr: nil,
		}, {
			CaseDoc: "多个",
			Inputs: map[string]interface{}{
				"key":  "value",
				"key2": "value2",
			},
			Want:    "key=valuekey2=value2",
			WantErr: nil,
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			buffer := &bytes.Buffer{}
			task := NewEchoNodeTask(buffer)

			_, _, err := task.Run(tc.Inputs, nil, nil)
			assert.Equal(t, tc.Want, buffer.String())
			assert.Equal(t, tc.WantErr, err)

		})
	}
}
