package flow

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestWorkflowNodeExecutor_BuildInputs(t *testing.T) {
	workflow := &Workflow{
		Nodes: []*WorkflowNode{
			{
				ID: "node1",
			}, {
				ID:       "node2",
				NodeType: "echo",
				Inputs: []*NodeParam{
					{
						ID:          "input1",
						Type:        "string",
						Label:       "input1",
						Description: "input1",
						Value:       "value1",
					}, {
						ID:          "input2",
						Type:        "string",
						Label:       "input2",
						Description: "input2",
						Value:       "value2",
					}, {
						ID:          "input3",
						Type:        "string",
						Label:       "input3",
						Description: "input3",
						Value:       "value3",
					},
				},
				Outputs: []*NodeParam{
					{
						ID:          "result",
						Type:        "string",
						Label:       "result",
						Description: "result",
						Value:       "",
					},
				},
			}, {
				ID:       "node3",
				NodeType: "echo",
				Inputs: []*NodeParam{
					{
						ID:    "input2",
						Type:  "string",
						Label: "input2",
						Value: "{{$node2.result}}",
					},
				},
				Outputs: []*NodeParam{
					{
						ID:    "result",
						Type:  "string",
						Label: "result",
					},
				},
			},
		},
		Edges: []*WorkflowEdge{
			{
				From: "node1",
				To:   "node2",
			}, {
				From: "node2",
				To:   "node3",
			},
		},
	}

	executor := NewWorkflowExecutor(workflow)

	err := executor.Execute()
	node2Outputs := executor.NodeExecutors.Get("node2").GetOutputs()
	assert.Equal(t, map[string]interface{}{
		"result": "input1=value1input2=value2input3=value3",
	}, node2Outputs)
	node3Outputs := executor.NodeExecutors.Get("node3").GetOutputs()
	assert.Equal(t, map[string]interface{}{
		"result": "input2=input1=value1input2=value2input3=value3",
	}, node3Outputs)
	assert.NoError(t, err)
}
