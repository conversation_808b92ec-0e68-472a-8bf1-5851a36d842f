package flow

import (
	"context"
	"resflow/common"
	"resflow/utils"
	"sync"
)

type IWorkflowExecutor interface {
	Execute() error
	Pause() error
	Cancel() error
}

// WorkflowExecutor 工作流执行器，能够执行某个工作流，上面会保存每个节点的执行结果
type WorkflowExecutor struct {
	Graph         *WorkflowGraph     // Graph 工作流邻接表
	NodeExecutors *NodeExecutorsMap  // NodeExecutors 节点执行器map
	nodeChan      chan *WorkflowNode // nodeChan 节点通道
	errChan       chan error         // errChan 错误通道
	doneChan      chan bool          // doneChan 执行完成通道
	cancelChan    chan bool          // cancelChan 取消通道
	RunningNodes  *sync.Map          // RunningNodes 当前正在执行的节点列表
}

// NewWorkflowExecutor 创建工作流执行器
func NewWorkflowExecutor(workflow *Workflow) *WorkflowExecutor {
	return &WorkflowExecutor{
		Graph:         NewWorkflowGraph(workflow),
		NodeExecutors: NewNodeExecutorsMap(),
		node<PERSON>han:      make(chan *WorkflowNode, 100),
		err<PERSON>han:       make(chan error),
		done<PERSON>han:      make(chan bool),
		cancelChan:    make(chan bool),
		RunningNodes:  &sync.Map{},
	}
}

// Execute 执行工作流，负责启动工作流
func (executor *WorkflowExecutor) Execute() error {
	// 获取启动节点
	nodes := executor.getStartNodes()
	for _, node := range nodes {
		executor.nodeChan <- node
	}

	asyncNodeExecCtx, cancelAsyncNodeExec := context.WithCancel(context.Background())
	defer cancelAsyncNodeExec()

	// 异步监听节点执行器通道
	go func(ctx context.Context) {
		for {
			select {
			case node := <-executor.nodeChan:
				// 异步执行节点
				go executor.executeNode(node, asyncNodeExecCtx)
			case <-ctx.Done():
				// 如果被取消则退出
				break
			}
		}
	}(asyncNodeExecCtx)

	for {
		select {
		case err := <-executor.errChan:
			return err
		case <-executor.doneChan:
			// 判断是否有节点还在执行，如果没有节点在执行，则表示完成
			flag := false
			executor.RunningNodes.Range(func(key, value interface{}) bool {
				flag = true
				return false
			})
			if !flag {
				return nil
			}

		case <-executor.cancelChan:
			utils.Logger.Debug("workflow executor is cancelled")
			return nil
		}
	}
}

func (executor *WorkflowExecutor) executeNode(node *WorkflowNode, ctx context.Context) {
	// 初始化节点执行器
	nodeExecutor := NewWorkflowNodeExecutor(node, executor)
	// 检查前驱节点是否都已执行完成
	if !executor.IsInDegreeNodesDone(node.ID) {
		utils.Logger.Debug("前驱节点未执行完，跳过，等待前驱节点执行完")
		return
	}

	// 添加节点执行器到执行器列表
	executor.AddNodeExecutor(nodeExecutor)

	// 检查是否暂停或取消
	select {
	case <-ctx.Done():
		nodeExecutor.Cancel()
		return
	default:
	}

	// 添加节点到执行列表
	executor.RunningNodes.Store(node.ID, node)

	nextNodePort, err := nodeExecutor.Execute(ctx)

	// 从执行列表中移除
	executor.RunningNodes.Delete(node.ID)

	if err != nil {
		// 执行出错
		executor.errChan <- err
	} else {
		// 根据当前执行节点的ID和返回的节点端口继续分发其他节点
		nextNodes := executor.Graph.GetOutDegreeNodesByNodeIdAndPort(nodeExecutor.Node.ID, nextNodePort)
		if len(nextNodes) > 0 {
			for _, nextNode := range nextNodes {
				executor.nodeChan <- nextNode
			}
		} else {
			// 后续已经没有节点
			executor.doneChan <- true
		}
	}
}

// getStartNodes 获取开始节点，从起始节点开始找到状态未完成的节点
func (executor *WorkflowExecutor) getStartNodes() []*WorkflowNode {
	startNodes := make([]*WorkflowNode, 0)
	// 根据邻接表，获取入度为0的节点
	nodes := executor.Graph.GetNodesWithInDegree(0)
	queue := common.NewQueue[*WorkflowNode]()
	for _, node := range nodes {
		queue.Enqueue(node)
	}

	for !queue.IsEmpty() {
		node := queue.Dequeue()
		// 判断是否存在节点执行器，如果不存在则表示未执行过
		nodeExecutor := executor.NodeExecutors.Get(node.ID)
		if nodeExecutor != nil && nodeExecutor.IsComplete() {
			// 如果节点已经完成，添加后继节点到队列
			nextNodes := executor.Graph.GetOutDegreeNodesByNodeIdAndPort(node.ID, nodeExecutor.GetPort())
			for _, nextNode := range nextNodes {
				queue.Enqueue(nextNode)
			}
		} else {
			startNodes = append(startNodes, node)
		}
	}
	return startNodes
}

// getEndNodeCount 获取结束节点个数
func (executor *WorkflowExecutor) getEndNodeCount() int {
	return executor.Graph.GetNodeCountWithOutDegree(0)
}

// IsInDegreeNodesDone 返回某个节点的前驱节点是否都已经执行完毕
func (executor *WorkflowExecutor) IsInDegreeNodesDone(nodeId string) bool {
	fromNodes := executor.Graph.GetInDegreeNodesByNodeId(nodeId)
	flag := true
	for _, node := range fromNodes {
		if !executor.NodeExecutors.NodeIsDone(node.ID) {
			flag = false
		}
	}
	return flag
}

// Pause 暂停执行工作流
func (executor *WorkflowExecutor) Pause() error {
	executor.cancelChan <- true
	return nil
}

// Cancel 取消工作流，清空节点执行器列表
func (executor *WorkflowExecutor) Cancel() error {
	executor.NodeExecutors.Clear()
	executor.cancelChan <- true
	return nil
}

func (executor *WorkflowExecutor) AddNodeExecutor(nodeExecutor *WorkflowNodeExecutor) {
	executor.NodeExecutors.Add(nodeExecutor)
}
