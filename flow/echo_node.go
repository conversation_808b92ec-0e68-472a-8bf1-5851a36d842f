package flow

import "io"

const NodeTypeEcho = "echo"

type EchoNodeTask struct {
	writer io.Writer
}

func NewEchoNodeTask(writer io.Writer) *EchoNodeTask {
	return &EchoNodeTask{
		writer: writer,
	}
}

func (t *EchoNodeTask) Run(inputs map[string]interface{}, ports []*NodePort, paramProvider ParamProvider) (map[string]interface{}, *NodePort, error) {
	result := ""
	resMap := map[string]interface{}{}
	for key, value := range inputs {
		result += key + "=" + value.(string)
	}
	if t.writer != nil {
		t.writer.Write([]byte(result))
	}
	resMap["result"] = result
	return resMap, nil, nil
}
