package flow

type WorkflowEdge struct {
	From     string `json:"from"`
	To       string `json:"to"`
	FromPort string `json:"from_port"`
	ToPort   string `json:"to_port"`
}

func NewWorkflowEdge(nodes []*WorkflowNode, from, to, fromPort, toPort string) *WorkflowEdge {
	if !NodeIsExists(nodes, from) {
		return nil
	}
	if !NodeIsExists(nodes, to) {
		return nil
	}
	return &WorkflowEdge{
		From:     from,
		To:       to,
		FromPort: fromPort,
		ToPort:   toPort,
	}
}

func NodeIsExists(nodes []*WorkflowNode, nodeId string) bool {
	for _, n := range nodes {
		if n.ID == nodeId {
			return true
		}
	}
	return false
}
