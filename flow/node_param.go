package flow

import "strconv"

type NodeParam struct {
	ID          string      `json:"id"`
	Type        string      `json:"type"`
	Label       string      `json:"label"`
	Description string      `json:"description"`
	Value       interface{} `json:"value"`
	Required    bool        `json:"required"`
}

func NewNodeParam(id, ty, label, description string, value interface{}, required bool) (*NodeParam, error) {
	if id == "" {
		return nil, ErrIdRequired
	}
	if ty == "" {
		return nil, ErrParamTypeRequired
	}
	if ty == "any" {
		if v, ok := value.(map[string]interface{}); ok {
			value = v
		} else if v, ok := value.([]interface{}); ok {
			mapv := make([]map[string]interface{}, 0)
			for _, item := range v {
				mapv = append(mapv, item.(map[string]interface{}))
			}
			value = mapv
		}
	} else if ty == "number" {
		if v, ok := value.(int); ok {
			value = float64(v)
		} else if v, ok := value.(string); ok {
			nv, err := strconv.ParseFloat(v, 64)
			if err != nil {
				value = float64(0)
			} else {
				value = nv
			}
		}
	} else {
		if v, ok := value.(string); ok {
			value = v
		}
	}

	return &NodeParam{
		ID:          id,
		Type:        ty,
		Label:       label,
		Description: description,
		Value:       value,
		Required:    required,
	}, nil
}
