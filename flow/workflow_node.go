package flow

import (
	"errors"
	"resflow/utils"
)

var ErrIdRequired = errors.New("node id required")
var ErrParamTypeRequired = errors.New("parameter type required")
var ErrParamParseFail = errors.New("parameter parse fail")

type WorkflowNode struct {
	ID          string       `json:"id"`
	Icon        string       `json:"icon"`
	Name        string       `json:"name"`
	Description string       `json:"description"`
	NodeType    string       `json:"nodeType"`
	Version     string       `json:"version"`
	Inputs      []*NodeParam `json:"inputs"`
	Outputs     []*NodeParam `json:"outputs"`
	Ports       []*NodePort  `json:"ports"`
	Params      []*NodeParam // 存储节点运行时的变量值
}

func NewWorkflowNode(id, icon, name, description, nodeType, version string, inputs, outputs []*NodeParam, ports []*NodePort) (*WorkflowNode, error) {
	if id == "" {
		return nil, ErrIdRequired
	}
	node := &WorkflowNode{
		ID:          id,
		Icon:        icon,
		Name:        name,
		Description: description,
		NodeType:    nodeType,
		Version:     version,
	}

	// 检查输入输出
	if inputs != nil {
		nodeInputs := make([]*NodeParam, 0)
		for _, input := range inputs {
			param, err := NewNodeParam(input.ID, input.Type, input.Label, input.Description, input.Value, input.Required)
			if err != nil {
				utils.Logger.Error("输入参数格式错误", err)
				continue
			}
			nodeInputs = append(nodeInputs, param)
		}
		node.Inputs = nodeInputs
	}
	if outputs != nil {
		nodeOutputs := make([]*NodeParam, 0)
		for _, output := range outputs {
			param, err := NewNodeParam(output.ID, output.Type, output.Label, output.Description, output.Value, output.Required)
			if err != nil {
				utils.Logger.Error("输入参数格式错误", err)
				continue
			}
			nodeOutputs = append(nodeOutputs, param)
		}
		node.Outputs = nodeOutputs
	}

	return node, nil
}

func (wn *WorkflowNode) GetID() string {
	return wn.ID
}

func (wn *WorkflowNode) FromJson(jsonStr string) (*WorkflowNode, error) {
	return nil, nil
}

func (wn *WorkflowNode) ToJson() (string, error) {
	return "", nil
}

func (wn *WorkflowNode) GetPortById(id string) *NodePort {
	if id == "" {
		return nil
	}
	for _, port := range wn.Ports {
		if port.ID == id {
			return port
		}
	}
	return nil
}
