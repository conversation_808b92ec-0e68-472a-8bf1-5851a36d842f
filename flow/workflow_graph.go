package flow

// WorkflowGraph 将工作流转换为邻接表
type WorkflowGraph struct {
	Workflow         *Workflow
	inDegreeAdjList  map[string][]*WorkflowGraphNode // 入度邻接表
	outDegreeAdjList map[string][]*WorkflowGraphNode // 出度邻接表
}

type WorkflowGraphNode struct {
	Node *WorkflowNode
	Port *NodePort
}

func NewWorkflowGraphNode(node *WorkflowNode, port *NodePort) *WorkflowGraphNode {
	return &WorkflowGraphNode{
		Node: node,
		Port: port,
	}
}

func NewWorkflowGraph(workflow *Workflow) *WorkflowGraph {
	graph := &WorkflowGraph{
		Workflow: workflow,
	}

	// 构建节点ID映射
	nodeMap := workflow.GetNodeMap()

	// 构建入边邻接表
	inDegreeAdjList := make(map[string][]*WorkflowGraphNode, len(workflow.Nodes))
	for _, node := range workflow.Nodes {
		inDegreeAdjList[node.ID] = make([]*WorkflowGraphNode, 0)
	}
	for _, edge := range workflow.Edges {
		fromNode := nodeMap[edge.From]
		inDegreeAdjList[edge.To] = append(inDegreeAdjList[edge.To], NewWorkflowGraphNode(fromNode, fromNode.GetPortById(edge.FromPort)))
	}
	graph.inDegreeAdjList = inDegreeAdjList
	// 构建出边邻接表
	outDegreeAdjList := make(map[string][]*WorkflowGraphNode, len(workflow.Nodes))
	for _, node := range workflow.Nodes {
		outDegreeAdjList[node.ID] = make([]*WorkflowGraphNode, 0)
	}
	for _, edge := range workflow.Edges {
		fromNode := nodeMap[edge.From]
		toNode := nodeMap[edge.To]
		outDegreeAdjList[edge.From] = append(outDegreeAdjList[edge.From], NewWorkflowGraphNode(toNode, fromNode.GetPortById(edge.FromPort)))
	}
	graph.outDegreeAdjList = outDegreeAdjList

	return graph
}

// GetNodesWithInDegree 获取指定入度的节点
func (graph *WorkflowGraph) GetNodesWithInDegree(degree int) []*WorkflowNode {
	inDegreeNodes := make([]*WorkflowNode, 0)
	for nodeId, nodes := range graph.inDegreeAdjList {
		if len(nodes) == degree {
			inDegreeNodes = append(inDegreeNodes, graph.Workflow.GetNodeById(nodeId))
		}
	}
	return inDegreeNodes
}

// GetNodeCountWithOutDegree 获取指定出度的节点数
func (graph *WorkflowGraph) GetNodeCountWithOutDegree(degree int) int {
	outDegreeCount := make(map[string]int)
	for nodeId, nodes := range graph.outDegreeAdjList {
		if len(nodes) == degree {
			if _, ok := outDegreeCount[nodeId]; ok {
				outDegreeCount[nodeId] += 1
			} else {
				outDegreeCount[nodeId] = 1
			}
		}
	}
	return len(outDegreeCount)
}

// GetInDegreeNodesByNodeId 根据节点ID获取前驱节点
func (graph *WorkflowGraph) GetInDegreeNodesByNodeId(nodeId string) []*WorkflowNode {
	fromNodes := make([]*WorkflowNode, 0)
	if nodes, ok := graph.inDegreeAdjList[nodeId]; ok {
		for _, node := range nodes {
			fromNodes = append(fromNodes, node.Node)
		}
	}
	return fromNodes
}

// GetOutDegreeNodesByNodeIdAndPort 根据节点ID和节点端口获取后继节点
func (graph *WorkflowGraph) GetOutDegreeNodesByNodeIdAndPort(nodeId string, nodePort *NodePort) []*WorkflowNode {
	nextNodes := make([]*WorkflowNode, 0)
	if nodes, ok := graph.outDegreeAdjList[nodeId]; ok {
		for _, node := range nodes {
			if nodePort == nil || node.Port == nil {
				// nodePort为空则添加所有后继节点
				nextNodes = append(nextNodes, node.Node)
			} else {
				// nodePort不为空，则添加指定port的节点
				if node.Port.ID == nodePort.ID {
					nextNodes = append(nextNodes, node.Node)
				}
			}
		}
	}
	return nextNodes
}
