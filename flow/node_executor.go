package flow

import (
	"context"
	"regexp"
	"resflow/utils"
	"sync"
)

type NodeExecutorState int

const (
	NodePending NodeExecutorState = iota
	NodeRunning
	NodePaused
	NodeCompleted
	NodeCanceled
)

type NodeExecutor interface {
	Execute(ctx context.Context) (*NodePort, error)
	GetId() string
	IsComplete() bool
	Complete() error
	Cancel() error
	GetPort() *NodePort
	GetOutputs() map[string]interface{}
}

type WorkflowNodeExecutor struct {
	Node             *WorkflowNode
	WorkflowExecutor *WorkflowExecutor
	Inputs           map[string]interface{}
	Outputs          map[string]interface{}
	Done             bool
	State            NodeExecutorState
	Port             *NodePort // 执行完成后的节点出口
}

func NewWorkflowNodeExecutor(node *WorkflowNode, executor *WorkflowExecutor) *WorkflowNodeExecutor {
	return &WorkflowNodeExecutor{
		Node:             node,
		WorkflowExecutor: executor,
		State:            NodePending,
	}
}

func (nc *WorkflowNodeExecutor) BuildInputs() (map[string]interface{}, error) {
	inputs := make(map[string]interface{})
	for _, input := range nc.Node.Inputs {
		// 如果是any类型跳过解析
		if input.Type == "any" {
			inputs[input.ID] = input.Value
			continue
		}
		// 解析引用类型的变量
		value, err := input.Parse(nc.WorkflowExecutor.NodeExecutors)
		if err != nil {
			return nil, err
		}
		inputs[input.ID] = value
	}

	return inputs, nil
}

func (nc *WorkflowNodeExecutor) Execute(ctx context.Context) (*NodePort, error) {
	nc.State = NodeRunning

	utils.Logger.Debugf("执行节点%s的任务", nc.Node.ID)
	// 构造节点输入
	inputs, err := nc.BuildInputs()
	if err != nil {
		return nil, err
	}

	// 构造任务
	task := nc.BuildTask()
	var nodePort *NodePort
	if task != nil {
		outputs, port, err := task.Run(inputs, nc.Node.Ports, nc.WorkflowExecutor.NodeExecutors)
		nc.Outputs = outputs
		nodePort = port
		if err != nil {
			return nil, err
		}
	}

	utils.Logger.Debug("节点输出：", nc.Outputs)
	// TODO 保存节点输出结果
	nc.State = NodeCompleted
	nc.Port = nodePort
	return nodePort, nil
}

func (nc *WorkflowNodeExecutor) BuildTask() INodeTask {
	switch nc.Node.NodeType {
	case NodeTypeEcho:
		return NewEchoNodeTask(nil)
	case NodeTypeCondition:
		return NewConditionNode()
	default:
		return nil
	}
}

func (nc *WorkflowNodeExecutor) GetId() string {
	return nc.Node.ID
}

func (nc *WorkflowNodeExecutor) IsComplete() bool {
	return nc.State == NodeCompleted
}

func (nc *WorkflowNodeExecutor) Complete() error {
	nc.State = NodeCompleted
	return nil
}

func (nc *WorkflowNodeExecutor) Cancel() error {
	nc.State = NodeCanceled
	return nil
}

func (nc *WorkflowNodeExecutor) GetPort() *NodePort {
	return nc.Port
}

func (nc *WorkflowNodeExecutor) GetOutputs() map[string]interface{} {
	return nc.Outputs
}

// NodeExecutorsMap 并发安全的节点访问器map
type NodeExecutorsMap struct {
	executors map[string]NodeExecutor
	mutex     sync.RWMutex
}

func NewNodeExecutorsMap() *NodeExecutorsMap {
	return &NodeExecutorsMap{
		executors: make(map[string]NodeExecutor),
	}
}

func (nemap *NodeExecutorsMap) NodeIsDone(nodeId string) bool {
	nemap.mutex.RLock()
	defer nemap.mutex.RUnlock()
	if executor, ok := nemap.executors[nodeId]; ok {
		return executor.IsComplete()
	}
	return false
}

func (nemap *NodeExecutorsMap) Add(executor NodeExecutor) {
	nemap.mutex.Lock()
	defer nemap.mutex.Unlock()
	nemap.executors[executor.GetId()] = executor
}

func (nemap *NodeExecutorsMap) Get(nodeId string) NodeExecutor {
	nemap.mutex.RLock()
	defer nemap.mutex.RUnlock()
	if executor, ok := nemap.executors[nodeId]; ok {
		return executor
	}
	return nil
}

func (nemap *NodeExecutorsMap) MarkDone(nodeId string) {
	nemap.mutex.Lock()
	defer nemap.mutex.Unlock()
	if executor, ok := nemap.executors[nodeId]; ok {
		executor.Complete()
	}
}

func (nemap *NodeExecutorsMap) ToJson() (string, error) {
	return "", nil
}

func (nemap *NodeExecutorsMap) FromJson(jsonStr string) (*NodeExecutorsMap, error) {
	return nil, nil
}

func (nemap *NodeExecutorsMap) GetParam(nodeId, paramId string) (interface{}, error) {
	for executorNodeId, executor := range nemap.executors {
		if executorNodeId == nodeId {
			outputValue := executor.GetOutputs()
			if outputValue != nil {
				return outputValue[paramId], nil
			}
		}
	}
	return nil, nil
}

func (nemp *NodeExecutorsMap) Clear() {
	nemp.executors = make(map[string]NodeExecutor)
}

type ParamProvider interface {
	GetParam(nodeId, paramId string) (interface{}, error)
}

func (np *NodeParam) Parse(provider ParamProvider) (interface{}, error) {
	reg := regexp.MustCompile("\\{\\{\\$([a-zA-Z0-9]+)\\.([a-zA-Z0-9]+)}}")
	matches := reg.FindAllStringSubmatch(np.Value.(string), -1)
	if matches == nil {
		return np.Value, nil
	}
	if len(matches) != 1 && len(matches[0]) != 3 {
		return nil, ErrParamParseFail
	}
	value, err := provider.GetParam(matches[0][1], matches[0][2])
	if err != nil {
		return nil, err
	}
	return value, nil
}
