package flow

import (
	"errors"
	"resflow/common"
	"resflow/utils"
)

var ErrInvalidInputs = errors.New("判断器节点输入参数不正确")

const NodeTypeCondition = "condition"

type ConditionNode struct {
}

type Judgment struct {
	Operator  string   `json:"operator"`
	Value     string   `json:"value"`
	Variables []string `json:"variables"`
}

func NewConditionNode() *ConditionNode {
	return &ConditionNode{}
}

func (cn *ConditionNode) Run(inputs map[string]interface{}, ports []*NodePort, paramProvider ParamProvider) (map[string]interface{}, *NodePort, error) {
	inputsMap, ok := inputs["condition"].(map[string]interface{})
	if !ok {
		return nil, nil, ErrInvalidInputs
	}
	outputs := make(map[string]interface{})
	conditions, ok := inputsMap["conditions"].([]map[string]interface{})
	if !ok {
		return nil, nil, ErrInvalidInputs
	}
	operator := inputsMap["operator"].(string)
	var flag = true
	for _, condition := range conditions {
		var judgment Judgment
		err := common.MapToStruct(condition, &judgment)
		if err != nil {
			return nil, nil, ErrInvalidInputs
		}
		conditionResult := cn.GetJudgmentResult(judgment, paramProvider)
		if operator == "AND" {
			flag = flag && conditionResult
		} else {
			flag = flag || conditionResult
		}
	}

	if flag {
		for _, port := range ports {
			if port.ID == "if" {
				return outputs, port, nil
			}
		}
	} else {
		for _, port := range ports {
			if port.ID == "else" {
				return outputs, port, nil
			}
		}
	}
	return nil, nil, nil
}

func (cn *ConditionNode) GetJudgmentResult(judgment Judgment, provider ParamProvider) bool {
	// 解析变量
	if len(judgment.Variables) != 2 {
		utils.Logger.Debug("判断条件参数不正确")
		return false
	}
	nodeId := judgment.Variables[0]
	paramId := judgment.Variables[1]
	variable, _ := provider.GetParam(nodeId, paramId)
	switch judgment.Operator {
	case "equal":
		return variable == judgment.Value
	}
	return false
}
