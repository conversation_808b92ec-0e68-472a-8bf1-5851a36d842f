package flow

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNewWorkflowNode(t *testing.T) {
	cases := []struct {
		CaseDoc  string
		ID       string
		Name     string
		Icon     string
		Desc     string
		NodeType string
		Version  string
		Inputs   []*NodeParam
		Outputs  []*NodeParam
		Ports    []*NodePort
		WantNode *WorkflowNode
		WantErr  error
	}{
		{
			CaseDoc:  "无输入输出及端口节点",
			ID:       "node",
			Name:     "node",
			Icon:     "icon",
			Desc:     "node",
			NodeType: "node",
			Inputs:   nil,
			Outputs:  nil,
			Ports:    nil,
			WantNode: &WorkflowNode{
				ID:          "node",
				Name:        "node",
				Icon:        "icon",
				Description: "node",
				NodeType:    "node",
				Inputs:      nil,
				Outputs:     nil,
				Ports:       nil,
			},
		}, {
			CaseDoc:  "带数值输入节点",
			ID:       "node",
			Name:     "node",
			Icon:     "icon",
			Desc:     "node",
			NodeType: "node",
			Inputs: []*NodeParam{
				{
					ID:          "param",
					Type:        "number",
					Description: "param",
					Value:       "1",
					Required:    true,
				},
			},
			Outputs: nil,
			Ports:   nil,
			WantNode: &WorkflowNode{
				ID:          "node",
				Name:        "node",
				Icon:        "icon",
				Description: "node",
				NodeType:    "node",
				Inputs: []*NodeParam{
					{
						ID:          "param",
						Type:        "number",
						Description: "param",
						Value:       float64(1),
						Required:    true,
					},
				},
			},
			WantErr: nil,
		}, {
			CaseDoc:  "带复杂结构输入节点",
			ID:       "node",
			Name:     "node",
			Icon:     "icon",
			Desc:     "node",
			NodeType: "node",
			Inputs: []*NodeParam{
				{
					ID:          "param",
					Type:        "any",
					Description: "param",
					Value: map[string]interface{}{
						"number": 1.123,
						"string": "string",
						"bool":   true,
						"null":   nil,
					},
					Required: true,
				},
				{
					ID:          "param",
					Type:        "any",
					Description: "param",
					Value: []map[string]interface{}{
						{
							"number": 1.123,
							"string": "string",
							"bool":   true,
							"null":   nil,
						},
					},
					Required: true,
				},
			},
			Outputs: nil,
			Ports:   nil,
			WantNode: &WorkflowNode{
				ID:          "node",
				Name:        "node",
				Icon:        "icon",
				Description: "node",
				NodeType:    "node",
				Inputs: []*NodeParam{
					{
						ID:          "param",
						Type:        "any",
						Description: "param",
						Value: map[string]interface{}{
							"number": 1.123,
							"string": "string",
							"bool":   true,
							"null":   nil,
						},
						Required: true,
					},
					{
						ID:          "param",
						Type:        "any",
						Description: "param",
						Value: []map[string]interface{}{
							{
								"number": 1.123,
								"string": "string",
								"bool":   true,
								"null":   nil,
							},
						},
						Required: true,
					},
				},
			},
			WantErr: nil,
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {

			gotNode, gotErr := NewWorkflowNode(tc.ID, tc.Icon, tc.Name, tc.Desc, tc.NodeType, tc.Version, tc.Inputs, tc.Outputs, tc.Ports)

			assert.Equal(t, tc.WantNode, gotNode)
			assert.Equal(t, tc.WantErr, gotErr)
		})
	}
}
