package flow

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

var singleGraphWorkflow = &Workflow{
	Nodes: []*WorkflowNode{
		{
			ID: "node1",
		},
		{
			ID: "node2",
		},
		{
			ID: "node3",
		},
	},
	Edges: []*WorkflowEdge{
		{
			From: "node1",
			To:   "node2",
		}, {
			From: "node2",
			To:   "node3",
		},
	},
}

var multipleGraphWorkflow = &Workflow{
	Nodes: []*WorkflowNode{
		{
			ID: "node1",
		}, {
			ID: "node2",
		}, {
			ID: "node3",
		}, {
			ID: "node4",
		}, {
			ID: "node5",
		}, {
			ID: "node6",
		},
	},
	Edges: []*WorkflowEdge{
		{
			From: "node1",
			To:   "node2",
		}, {
			From: "node1",
			To:   "node3",
		}, {
			From: "node2",
			To:   "node4",
		}, {
			From: "node3",
			To:   "node5",
		}, {
			From: "node4",
			To:   "node6",
		}, {
			From: "node5",
			To:   "node6",
		},
	},
}

var multiStartNodesWorkflow = &Workflow{
	Nodes: []*WorkflowNode{
		{
			ID: "node1",
		}, {
			ID: "node2",
		}, {
			ID: "node3",
		}, {
			ID: "node4",
		},
	},
	Edges: []*WorkflowEdge{
		{
			From: "node1",
			To:   "node2",
		}, {
			From: "node2",
			To:   "node3",
		}, {
			From: "node4",
			To:   "node3",
		},
	},
}

func TestNewWorkflowGraph(t *testing.T) {

	cases := []struct {
		CaseDoc   string
		Workflow  *Workflow
		WantGraph *WorkflowGraph
	}{
		{
			CaseDoc:  "单链图",
			Workflow: singleGraphWorkflow,
			WantGraph: &WorkflowGraph{
				Workflow: singleGraphWorkflow,
				inDegreeAdjList: map[string][]*WorkflowGraphNode{
					"node1": {},
					"node2": {
						{
							Node: &WorkflowNode{
								ID: "node1",
							},
							Port: nil,
						},
					},
					"node3": {
						{
							Node: &WorkflowNode{
								ID: "node2",
							},
							Port: nil,
						},
					},
				},
				outDegreeAdjList: map[string][]*WorkflowGraphNode{
					"node1": {
						{
							Node: &WorkflowNode{
								ID: "node2",
							},
							Port: nil,
						},
					},
					"node2": {
						{
							Node: &WorkflowNode{
								ID: "node3",
							},
							Port: nil,
						},
					},
					"node3": {},
				},
			},
		},
		{
			CaseDoc:  "双链图",
			Workflow: multipleGraphWorkflow,
			WantGraph: &WorkflowGraph{
				Workflow: multipleGraphWorkflow,
				inDegreeAdjList: map[string][]*WorkflowGraphNode{
					"node1": {},
					"node2": {
						{
							Node: &WorkflowNode{
								ID: "node1",
							},
						},
					},
					"node3": {
						{
							Node: &WorkflowNode{
								ID: "node1",
							},
						},
					},
					"node4": {
						{
							Node: &WorkflowNode{
								ID: "node2",
							},
						},
					},
					"node5": {
						{
							Node: &WorkflowNode{
								ID: "node3",
							},
						},
					},
					"node6": {
						{
							Node: &WorkflowNode{
								ID: "node4",
							},
						},
						{
							Node: &WorkflowNode{
								ID: "node5",
							},
						},
					},
				},
				outDegreeAdjList: map[string][]*WorkflowGraphNode{
					"node1": {
						{
							Node: &WorkflowNode{
								ID: "node2",
							},
						}, {
							Node: &WorkflowNode{
								ID: "node3",
							},
						},
					},
					"node2": {
						{
							Node: &WorkflowNode{
								ID: "node4",
							},
						},
					},
					"node3": {
						{
							Node: &WorkflowNode{
								ID: "node5",
							},
						},
					},
					"node4": {
						{
							Node: &WorkflowNode{
								ID: "node6",
							},
						},
					},
					"node5": {
						{
							Node: &WorkflowNode{
								ID: "node6",
							},
						},
					},
					"node6": {},
				},
			},
		},
		{
			CaseDoc:  "多个开始节点",
			Workflow: multiStartNodesWorkflow,
			WantGraph: &WorkflowGraph{
				Workflow: multiStartNodesWorkflow,
				inDegreeAdjList: map[string][]*WorkflowGraphNode{
					"node1": {},
					"node2": {
						{
							Node: &WorkflowNode{
								ID: "node1",
							},
						},
					},
					"node3": {
						{
							Node: &WorkflowNode{
								ID: "node2",
							},
						}, {
							Node: &WorkflowNode{
								ID: "node4",
							},
						},
					},
					"node4": {},
				},
				outDegreeAdjList: map[string][]*WorkflowGraphNode{
					"node1": {
						{
							Node: &WorkflowNode{
								ID: "node2",
							},
						},
					},
					"node2": {
						{
							Node: &WorkflowNode{
								ID: "node3",
							},
						},
					},
					"node3": {},
					"node4": {
						{
							Node: &WorkflowNode{
								ID: "node3",
							},
						},
					},
				},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			gotGraph := NewWorkflowGraph(tc.Workflow)
			assert.Equal(t, tc.WantGraph, gotGraph)
		})
	}
}
