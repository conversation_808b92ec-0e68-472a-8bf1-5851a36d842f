package flow

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestConditionNode_Run(t *testing.T) {
	cases := []struct {
		CaseDoc       string
		ParamProvider *NodeExecutorsMap
		Inputs        map[string]interface{}
		Ports         []*NodePort
		Want          *NodePort
		WantErr       error
	}{
		{
			CaseDoc: "if成立",
			ParamProvider: &NodeExecutorsMap{
				executors: map[string]NodeExecutor{
					"node1": &WorkflowNodeExecutor{
						Outputs: map[string]interface{}{
							"result": "input1=value1",
						},
					},
				},
			},
			Inputs: map[string]interface{}{
				"condition": map[string]interface{}{
					"operator": "AND",
					"conditions": []map[string]interface{}{
						{
							"operator": "equal",
							"value":    "input1=value1",
							"variables": []string{
								"node1",
								"result",
							},
						},
					},
				},
			},
			Ports: []*NodePort{
				{
					ID: "if",
				}, {
					ID: "else",
				},
			},
			Want: &NodePort{
				ID: "if",
			},
			WantErr: nil,
		}, {
			CaseDoc: "else成立",
			ParamProvider: &NodeExecutorsMap{
				executors: map[string]NodeExecutor{
					"node1": &WorkflowNodeExecutor{
						Outputs: map[string]interface{}{
							"result": "input1=value1",
						},
					},
				},
			},
			Inputs: map[string]interface{}{
				"condition": map[string]interface{}{
					"operator": "AND",
					"conditions": []map[string]interface{}{
						{
							"operator": "equal",
							"value":    "not equal",
							"variables": []string{
								"node1",
								"result",
							},
						},
					},
				},
			},
			Ports: []*NodePort{
				{
					ID: "if",
				}, {
					ID: "else",
				},
			},
			Want: &NodePort{
				ID: "else",
			},
			WantErr: nil,
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			task := NewConditionNode()
			_, port, err := task.Run(tc.Inputs, tc.Ports, tc.ParamProvider)
			assert.Equal(t, tc.Want, port)
			assert.Equal(t, err, tc.WantErr)
		})
	}
}
