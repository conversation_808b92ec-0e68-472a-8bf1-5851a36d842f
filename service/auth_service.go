package service

import (
	"context"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	v1 "resflow/proto/generated_go/v1"
	"resflow/service/converter"
)

type AuthService struct {
	v1.UnimplementedAuthServiceServer
	userService  UserService
	tokenService TokenService
}

var ErrUserUnauthenticated = status.Error(codes.Unauthenticated, "用户未登录")

func NewAuthService(userService UserService, tokenService TokenService) *AuthService {
	return &AuthService{
		userService:  userService,
		tokenService: tokenService,
	}
}

// Login 登录
func (s *AuthService) Login(ctx context.Context, in *v1.LoginRequest) (*v1.LoginResponse, error) {
	exists, u := s.userService.IsUserExist(ctx, in.Username)
	if !exists {
		return nil, status.Errorf(codes.NotFound, "用户%s不存在", in.Username)
	}

	// 判断密码是否正确
	if correct, _ := s.compareCredential(in.Password, u.Password); !correct {
		return nil, status.Error(codes.InvalidArgument, "密码不正确")
	}

	// token
	token, err := s.tokenService.GenerateUserToken(u)
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}

	// 转换pb响应
	pbUser := converter.UserToUserDetailPb(u)

	return &v1.LoginResponse{User: pbUser, Token: token}, nil
}

// compareCredential 比较密码是否一致
func (s *AuthService) compareCredential(password string, hashedPassword string) (bool, error) {
	if err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password)); err != nil {
		return false, err
	}
	return true, nil
}
