package service

import (
	"github.com/golang-jwt/jwt/v5"
	"resflow/configs"
	"resflow/ent"
	"time"
)

type TokenService interface {
	GenerateUserToken(user *ent.User) (string, error)
}

type tokenService struct {
}

func NewTokenService() TokenService {
	return &tokenService{}
}

func (t *tokenService) GenerateUserToken(u *ent.User) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"id":       u.ID,
		"username": u.Username,
		"sub":      "token",
		"exp":      time.Now().Add(time.Hour * 24 * 15).Unix(),
	})
	return token.SignedString([]byte(configs.ParsedConfig.JwtSecret))
}
