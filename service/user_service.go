package service

import (
	"context"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
	"resflow/ent"
	"resflow/enums"
	"resflow/middlewares"
	v1 "resflow/proto/generated_go/v1"
	"resflow/repo"
	"resflow/service/converter"
	"resflow/utils"
)

var ErrUserExisted = status.Error(codes.AlreadyExists, "用户已存在")
var ErrCreateUserFail = status.Error(codes.Internal, "用户创建失败")

type UserService interface {
	v1.UserServiceServer
	Create(ctx context.Context, req *v1.CreateUserRequest) (*v1.CreateUserResponse, error)
	CreateUser(ctx context.Context, user *ent.User) (*ent.User, error)
	IsUserExist(ctx context.Context, username string) (bool, *ent.User)
	CreateWithRandomPassword(ctx context.Context, username string) (*ent.User, string, error)
}

type userService struct {
	v1.UnimplementedUserServiceServer
	store repo.User
}

func NewUserService(store repo.User) UserService {
	return &userService{
		store: store,
	}
}

// Create 创建用户grpc
func (s *userService) Create(ctx context.Context, req *v1.CreateUserRequest) (*v1.CreateUserResponse, error) {
	user, err := s.CreateUser(ctx, &ent.User{
		Username: req.GetUsername(),
		Password: req.GetPassword(),
		Nickname: req.GetNickname(),
		Status:   enums.Active,
	})

	if err != nil {
		utils.Logger.Debugln("用户创建失败：", err.Error())
		return nil, err
	}

	pbUser := converter.UserToUserDetailPb(user)

	return &v1.CreateUserResponse{User: pbUser}, nil
}

// CreateUser 创建用户
func (s *userService) CreateUser(ctx context.Context, user *ent.User) (*ent.User, error) {
	// 判断用户是否存在
	if exists, _ := s.IsUserExist(ctx, user.Username); exists {
		return nil, ErrUserExisted
	}

	// 加密密码
	hashedPassword, err := s.encryptPassword(user.Password)
	if err != nil {
		utils.Logger.Debugln("密码加密失败：", err.Error())
		return nil, ErrCreateUserFail
	}

	u, err := s.store.Create(ctx, &ent.User{
		Username: user.Username,
		Password: hashedPassword,
		Nickname: user.Nickname,
		Status:   user.Status,
	})

	if err != nil {
		return nil, ErrCreateUserFail
	}

	return u, nil
}

// encryptPassword 加密密码
func (s *userService) encryptPassword(password string) (string, error) {
	hashed, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(hashed), err
}

// IsUserExist 判断用户是否存在
func (s *userService) IsUserExist(context context.Context, username string) (bool, *ent.User) {
	user, _ := s.store.GetByUsername(context, username)
	return user != nil, user
}

// CreateWithRandomPassword 根据用户名创建用户，密码为随机密码
func (s *userService) CreateWithRandomPassword(ctx context.Context, username string) (*ent.User, string, error) {
	password := utils.RandomString(16)
	user, err := s.CreateUser(ctx, &ent.User{
		Username: username,
		Password: password,
		Status:   enums.Active,
	})
	if err != nil {
		return nil, "", err
	}
	return user, password, nil
}

func (s *userService) GetCurrentUser(ctx context.Context, _ *emptypb.Empty) (*v1.User, error) {
	u, err := middlewares.GetCurrentAuthUser(ctx)
	if err != nil {
		return nil, status.Error(codes.Unauthenticated, "未登录")
	}

	pbUser := converter.UserToUserDetailPb(u)
	return pbUser, nil
}
