package service

import (
	"context"
	"encoding/json"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"resflow/common"
	"resflow/ent"
	"resflow/enums"
	"resflow/middlewares"
	v1 "resflow/proto/generated_go/v1"
	"resflow/repo"
	"resflow/service/converter"
	"resflow/utils"
)

var ErrListWorkflowFail = status.Error(codes.Internal, "获取工作流列表失败")
var ErrCreateWorkflowFail = status.Error(codes.Internal, "创建工作流失败")
var ErrUpdateWorkflowFail = status.Errorf(codes.Internal, "保存工作流失败")
var ErrDeleteWorkflowFail = status.Error(codes.Internal, "删除工作流失败")
var ErrWorkflowNotFound = status.Error(codes.NotFound, "工作流不存在")
var ErrWorkflowUnauthorized = status.Error(codes.PermissionDenied, "权限不足")

type WorkflowService interface {
	v1.WorkflowServiceServer
}

type workflowService struct {
	v1.UnimplementedWorkflowServiceServer
	store        repo.Workflow
	nodeStore    repo.WorkflowNode
	linkStore    repo.WorkflowLink
	nodesUpdater UpdateNodesOrLinks[[]*v1.WorkflowNodeRequest]
	linksUpdater UpdateNodesOrLinks[[]*v1.WorkflowLinkRequest]
}

func NewWorkflowService(store repo.Workflow, nodeStore repo.WorkflowNode, linkStore repo.WorkflowLink, nodesUpdater UpdateNodesOrLinks[[]*v1.WorkflowNodeRequest], linksUpdater UpdateNodesOrLinks[[]*v1.WorkflowLinkRequest]) WorkflowService {
	return &workflowService{
		store:        store,
		nodeStore:    nodeStore,
		linkStore:    linkStore,
		nodesUpdater: nodesUpdater,
		linksUpdater: linksUpdater,
	}
}

// GetById 根据ID获取工作流
func (s *workflowService) GetById(ctx context.Context, request *v1.OnlyIdRequest) (*v1.WorkflowResponse, error) {
	id, err := uuid.Parse(request.GetId())
	if err != nil {
		utils.Logger.Debug("工作流ID参数错误")
		return nil, ErrWorkflowNotFound
	}
	workflow, err := s.store.GetById(ctx, id)
	if err != nil {
		utils.Logger.Debugf("工作流%s不存在", id)
		return nil, ErrWorkflowNotFound
	}

	workflowPb := converter.WorkflowToWorkflowPb(ctx, workflow)

	return &v1.WorkflowResponse{Workflow: workflowPb}, nil
}

// Create 创建工作流
func (s *workflowService) Create(ctx context.Context, request *v1.CreateWorkflowRequest) (*v1.WorkflowResponse, error) {
	// 验证参数
	validate, trans := common.GetValidate()

	if errs := validate.Struct(*request); errs != nil {
		return nil, status.Error(codes.InvalidArgument, common.GetValidationErrMsg(errs.(validator.ValidationErrors), trans, *request))
	}

	// 当前用户
	user, _ := middlewares.GetCurrentAuthUser(ctx)

	// 创建工作流
	workflow, err := s.store.CreateWithTx(ctx, user.ID, &ent.Workflow{
		Name:        request.Name,
		Description: request.Description,
		Icon:        request.Icon,
		Status:      enums.Active,
		Viewport:    request.Viewport,
	}, func(tx *ent.Tx, workflow *ent.Workflow) error {
		// 创建节点
		for _, node := range request.Nodes {
			id, err := uuid.Parse(node.Id)
			if err != nil {
				return err
			}
			var pluginName, pluginVersion string
			if node.PluginName != nil {
				pluginName = *node.PluginName
			}
			if node.PluginVersion != nil {
				pluginVersion = *node.PluginVersion
			}
			inputValues := utils.Convert2JSONWithDefault(node.InputValues, json.RawMessage("{}"))
			outputValues := utils.Convert2JSONWithDefault(node.OutputValues, json.RawMessage("{}"))
			data := utils.Convert2JSONWithDefault(node.Data, json.RawMessage("{}"))

			_, err = s.nodeStore.CreateWithTx(ctx, tx, workflow.ID, &ent.WorkflowNode{
				ID:            id,
				Name:          node.Name,
				Description:   node.Description,
				Icon:          node.Icon,
				Type:          node.Type,
				Version:       node.Version,
				PluginName:    pluginName,
				PluginVersion: pluginVersion,
				InputParams:   node.InputParams,
				InputValues:   inputValues,
				OutputParams:  node.OutputParams,
				OutputValues:  outputValues,
				InputPorts:    node.InputPorts,
				OutputPorts:   node.OutputPorts,
				Position:      node.Position,
				Data:          data,
			})
			if err != nil {
				return err
			}
		}
		// 创建边
		for _, link := range request.Links {
			fromId, err := uuid.Parse(link.FromNodeId)
			if err != nil {
				utils.Logger.Debug("来源节点id不存在")
				return err
			}
			toId, err := uuid.Parse(link.ToNodeId)
			if err != nil {
				utils.Logger.Debug("目标节点id不存在")
				return err
			}
			_, err = s.linkStore.CreateWithTx(ctx, tx, workflow.ID, &ent.WorkflowLink{
				FromNodeID: fromId,
				ToNodeID:   toId,
				FromPortID: link.FromPortId,
				ToPortID:   link.ToPortId,
				Type:       link.Type,
			})
			if err != nil {
				utils.Logger.Debug("创建边失败：", err.Error())
				return err
			}
		}
		return nil
	})
	if err != nil {
		utils.Logger.Debug("创建工作流失败：", err.Error())
		return nil, ErrCreateWorkflowFail
	}

	pbWorkflow := converter.WorkflowToWorkflowPb(ctx, workflow)

	return &v1.WorkflowResponse{Workflow: pbWorkflow}, nil
}

// Update 更新工作流
func (s *workflowService) Update(ctx context.Context, request *v1.UpdateWorkflowRequest) (*v1.WorkflowResponse, error) {
	// 验证参数
	validate, trans := common.GetValidate()

	if errs := validate.Struct(*request); errs != nil {
		return nil, status.Error(codes.InvalidArgument, common.GetValidationErrMsg(errs.(validator.ValidationErrors), trans, *request))
	}

	// 判断工作流是否存在
	id, err := uuid.Parse(request.GetId())
	if err != nil {
		utils.Logger.Debug("工作流ID参数错误")
		return nil, ErrWorkflowNotFound
	}
	workflow, err := s.store.GetById(ctx, id)
	if err != nil {
		utils.Logger.Debugf("工作流%s不存在", id)
		return nil, ErrWorkflowNotFound
	}

	// 权限判断
	user, err := middlewares.GetCurrentAuthUser(ctx)
	if err != nil {
		return nil, err
	}
	if user.ID != workflow.UserID {
		utils.Logger.Debug("工作流用户id不一致")
		return nil, ErrWorkflowUnauthorized
	}

	workflow.Name = request.Name
	workflow.Viewport = request.Viewport

	newWorkflow, err := s.store.UpdateWithTx(ctx, workflow, func(tx *ent.Tx) error {
		// 更新节点
		updateErr := s.nodesUpdater.Update(ctx, tx, workflow.ID, request.Nodes)
		if updateErr != nil {
			return updateErr
		}

		// 更新边
		updateErr = s.linksUpdater.Update(ctx, tx, workflow.ID, request.Links)
		if updateErr != nil {
			return updateErr
		}

		if updateErr != nil {
			return updateErr
		}

		return nil
	})
	if err != nil {
		utils.Logger.Debug("更新工作流失败：", err.Error())
		return nil, ErrUpdateWorkflowFail
	}

	pbWorkflow := converter.WorkflowToWorkflowPb(ctx, newWorkflow)

	return &v1.WorkflowResponse{Workflow: pbWorkflow}, nil
}

// DeleteById 根据ID删除工作流
func (s *workflowService) DeleteById(ctx context.Context, request *v1.OnlyIdRequest) (*v1.CommonResponse, error) {
	// 验证参数
	validate, trans := common.GetValidate()

	if errs := validate.Struct(*request); errs != nil {
		return nil, status.Error(codes.InvalidArgument, common.GetValidationErrMsg(errs.(validator.ValidationErrors), trans, *request))
	}

	// 判断是否存在
	id, err := uuid.Parse(request.GetId())
	if err != nil {
		utils.Logger.Debug("工作流ID参数错误")
		return nil, ErrWorkflowNotFound
	}
	if !s.isExists(ctx, id) {
		return nil, ErrWorkflowNotFound
	}

	if err := s.store.DeleteById(ctx, id, nil); err != nil {
		utils.Logger.Debug("删除工作流失败：", err.Error())
		return nil, ErrDeleteWorkflowFail
	}

	return &v1.CommonResponse{
		Code:    0,
		Message: "删除成功",
	}, nil
}

// List 查询所有工作流
func (s *workflowService) List(ctx context.Context, request *v1.ListWorkflowsRequest) (*v1.ListWorkflowsResponse, error) {

	workflows, err := s.store.List(ctx)
	if err != nil {
		return nil, ErrListWorkflowFail
	}

	pbWorkflows := make([]*v1.Workflow, 0)
	for _, workflow := range workflows {
		pbWorkflows = append(pbWorkflows, converter.WorkflowToWorkflowPb(ctx, workflow))
	}

	return &v1.ListWorkflowsResponse{Workflows: pbWorkflows}, nil
}

func (s *workflowService) isExists(ctx context.Context, id uuid.UUID) bool {
	workflow, err := s.store.GetById(ctx, id)
	if err != nil {
		return false
	}
	return workflow != nil
}

type UpdateNodesOrLinks[T interface{ []*v1.WorkflowNodeRequest } | interface{ []*v1.WorkflowLinkRequest }] interface {
	Update(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, items T) error
}

type NodesUpdater struct {
	store repo.WorkflowNode
}

func NewNodeUpdate(store repo.WorkflowNode) UpdateNodesOrLinks[[]*v1.WorkflowNodeRequest] {
	return &NodesUpdater{store: store}
}

// Update 更新节点
func (nu *NodesUpdater) Update(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, nodes []*v1.WorkflowNodeRequest) error {
	// 节点
	nodeIDs := make([]uuid.UUID, 0)
	for _, node := range nodes {
		// 判断节点是否存在，不存在则创建
		nodeId, err := uuid.Parse(node.GetId())
		if err != nil {
			utils.Logger.Debugf("节点ID[%s]错误", node.GetId())
			return err
		}
		workflowNode, err := nu.store.GetById(ctx, workflowId, nodeId)
		var pluginName, pluginVersion string
		if node.PluginName != nil {
			pluginName = *node.PluginName
		}
		if node.PluginVersion != nil {
			pluginVersion = *node.PluginVersion
		}
		inputValues := utils.Convert2JSONWithDefault(node.InputValues, json.RawMessage("{}"))
		outputValues := utils.Convert2JSONWithDefault(node.OutputValues, json.RawMessage("{}"))
		data := utils.Convert2JSONWithDefault(node.Data, json.RawMessage("{}"))
		workflowNodeModel := &ent.WorkflowNode{
			ID:            nodeId,
			Name:          node.Name,
			Description:   node.Description,
			Icon:          node.Icon,
			Type:          node.Type,
			Version:       node.Version,
			PluginName:    pluginName,
			PluginVersion: pluginVersion,
			InputParams:   node.InputParams,
			InputValues:   inputValues,
			OutputParams:  node.OutputParams,
			OutputValues:  outputValues,
			InputPorts:    node.InputPorts,
			OutputPorts:   node.OutputPorts,
			Position:      node.Position,
			Data:          data,
		}
		if workflowNode == nil {
			// 创建
			workflowNode, err = nu.store.CreateWithTx(ctx, tx, workflowId, workflowNodeModel)
		} else {
			workflowNode, err = nu.store.UpdateWithTx(ctx, tx, workflowId, workflowNodeModel)
		}
		if err != nil {
			return err
		}
		nodeIDs = append(nodeIDs, workflowNode.ID)
	}
	// 删除不存在的节点
	return nu.store.DeleteByWorkflowIdAndIdsNotInWithTx(ctx, tx, workflowId, nodeIDs)
}

type LinksUpdater struct {
	store repo.WorkflowLink
}

func NewLinkUpdate(store repo.WorkflowLink) UpdateNodesOrLinks[[]*v1.WorkflowLinkRequest] {
	return &LinksUpdater{
		store: store,
	}
}

// Update 更新边
func (lu *LinksUpdater) Update(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, links []*v1.WorkflowLinkRequest) error {
	// 边
	linkIds := make([]uuid.UUID, 0)
	for _, link := range links {
		// 判断是否存在
		linkId, err := uuid.Parse(link.GetId())
		if err != nil {
			return err
		}
		workflowLink, err := lu.store.GetById(ctx, linkId)
		fromNodeId, err := uuid.Parse(link.GetFromNodeId())
		if err != nil {
			utils.Logger.Debug("来源节点ID错误")
			return err
		}
		toNodeId, err := uuid.Parse(link.GetToNodeId())
		if err != nil {
			utils.Logger.Debug("目标节点ID错误")
			return err
		}
		if workflowLink == nil {
			workflowLink, err = lu.store.CreateWithTx(ctx, tx, workflowId, &ent.WorkflowLink{
				FromNodeID: fromNodeId,
				ToNodeID:   toNodeId,
				FromPortID: link.FromPortId,
				ToPortID:   link.ToPortId,
				Type:       link.Type,
			})
		} else {
			workflowLink, err = lu.store.UpdateWithTx(ctx, tx, &ent.WorkflowLink{
				ID:         linkId,
				FromNodeID: fromNodeId,
				ToNodeID:   toNodeId,
				FromPortID: link.FromPortId,
				ToPortID:   link.ToPortId,
				Type:       link.Type,
			})
		}
		if err != nil {
			return err
		}
		linkIds = append(linkIds, workflowLink.ID)
	}
	// 删除不存在的边
	return lu.store.DeleteByWorkflowIdAndIdsNotInWithTx(ctx, tx, workflowId, linkIds)
}
