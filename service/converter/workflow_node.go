package converter

import (
	"google.golang.org/protobuf/types/known/structpb"
	"resflow/ent"
	v1 "resflow/proto/generated_go/v1"
	"resflow/utils"
)

func WorkflowNodeToPb(node *ent.WorkflowNode) *v1.WorkflowNode {
	inputValues := utils.Convert2PbStructWithDefault(node.InputValues, &structpb.Struct{})
	outputValues := utils.Convert2PbStructWithDefault(node.OutputValues, &structpb.Struct{})
	data := utils.Convert2PbStructWithDefault(node.Data, &structpb.Struct{})

	return &v1.WorkflowNode{
		Id:            node.ID.String(),
		Name:          node.Name,
		Description:   node.Description,
		Icon:          node.Icon,
		Type:          node.Type,
		Version:       node.Version,
		PluginName:    node.PluginName,
		PluginVersion: node.PluginVersion,
		InputParams:   node.InputParams,
		InputValues:   inputValues,
		OutputParams:  node.OutputParams,
		OutputValues:  outputValues,
		InputPorts:    node.InputPorts,
		OutputPorts:   node.OutputPorts,
		Position:      node.Position,
		Data:          data,
		CreatedAt:     node.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     node.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
