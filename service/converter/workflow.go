package converter

import (
	"context"
	"resflow/ent"
	v1 "resflow/proto/generated_go/v1"
)

func WorkflowToWorkflowPb(ctx context.Context, workflow *ent.Workflow) *v1.Workflow {
	nodes := make([]*v1.WorkflowNode, 0)
	if workflow.Edges.Nodes != nil {
		for _, node := range workflow.Edges.Nodes {
			nodes = append(nodes, WorkflowNodeToPb(node))
		}
	}
	links := make([]*v1.WorkflowLink, 0)
	if workflow.Edges.Links != nil {
		for _, link := range workflow.Edges.Links {
			links = append(links, WorkflowLinkToPb(link))
		}
	}

	return &v1.Workflow{
		Id:          workflow.ID.String(),
		Name:        workflow.Name,
		Description: workflow.Description,
		Icon:        workflow.Icon,
		Status:      v1.Status(workflow.Status),
		Viewport:    workflow.Viewport,
		Nodes:       nodes,
		Links:       links,
		CreatedAt:   workflow.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   workflow.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
