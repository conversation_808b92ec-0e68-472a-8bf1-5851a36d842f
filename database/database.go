package database

import (
	"context"
	"database/sql"
	entsql "entgo.io/ent/dialect/sql"
	_ "github.com/mattn/go-sqlite3"
	"go.uber.org/zap"
	"os"
	"os/signal"
	"resflow/configs"
	"resflow/ent"
	"resflow/utils"
	"runtime/debug"
	"syscall"
	"time"
)

var DB *sql.DB
var Client *ent.Client

const AppDatabaseFile = "./data/db/app.db"

func init() {
	utils.Logger.Debug("database connection init")

	var err error

	DB, err = sql.Open("sqlite3", AppDatabaseFile)
	if err != nil {
		utils.Logger.Error("database connection init error", zap.Error(err))
		panic(err)
	}
	DB.SetMaxIdleConns(10)
	DB.SetMaxOpenConns(100)
	DB.SetConnMaxLifetime(time.Hour)

	drv := entsql.OpenDB("sqlite3", DB)
	Client = ent.NewClient(ent.Driver(drv), ent.Log(func(a ...any) {
		utils.Logger.Debug(a...)
	}))
	if configs.ParsedConfig.Debug {
		Client = Client.Debug()
	}

	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigs
		utils.Logger.Debug("closing database connection")
		if DB != nil {
			err = Client.Close()
			err = DB.Close()
			utils.Logger.Debug("database connection close")
			if err != nil {
				utils.Logger.Error("database close error", zap.Error(err))
				panic(err)
			}
		}
		os.Exit(0)
	}()
}

type Schema interface {
	CreateSchema()
}

func WithTx(ctx context.Context, client *ent.Client, fn func(tx *ent.Tx) error) (err error) {
	tx, err := client.Tx(ctx)
	if err != nil {
		return err
	}
	defer func() {
		if v := recover(); v != nil {
			err = v.(error)
			utils.Logger.Debug("事务提交失败，准备回滚：", v)
			utils.Logger.Debug(string(debug.Stack()))
			if rerr := tx.Rollback(); rerr != nil {
				utils.Logger.Errorf("%w: rolling back transaction: %v", err, rerr)
			}
		}
	}()
	if err := fn(tx); err != nil {
		panic(err)
	}
	if err := tx.Commit(); err != nil {
		utils.Logger.Errorf("committing transaction: %w", err)
		return err
	}
	return nil
}
