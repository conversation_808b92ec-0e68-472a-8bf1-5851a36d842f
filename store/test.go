package store

import (
	"context"
	"resflow/ent"
	"resflow/ent/enttest"
	"testing"
)

type SetUpFunc func(ctx context.Context, client *ent.Client)

func BuildTestClient(t *testing.T, ctx context.Context, callback ...SetUpFunc) *ent.Client {
	opts := []enttest.Option{
		enttest.WithOptions(ent.Log(t.Log)),
	}

	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&_fk=1", opts...)

	for _, f := range callback {
		f(ctx, client)
	}
	return client
}
