package store

import (
	"context"
	"github.com/brianvoe/gofakeit/v7"
	"github.com/stretchr/testify/assert"
	"resflow/database"
	"resflow/ent"
	"resflow/ent/workflownode"
	v1 "resflow/proto/generated_go/v1"
	"testing"
)

func createWorkflowNode(client *ent.Client, workflowId int) *ent.WorkflowNode {
	return client.WorkflowNode.Create().SetWorkflowID(workflowId).SetID(gofakeit.UUID()).SetName(gofakeit.Name()).SetDescription(gofakeit.Sentence(50)).SetType(gofakeit.Word()).SetIcon(gofakeit.Word()).SetVersion(gofakeit.AppVersion()).SetInputs([]*v1.NodeParam{}).SetOutputs([]*v1.NodeParam{}).SetPorts([]*v1.NodePort{}).SaveX(context.Background())
}

// TestWorkflowNodeStore_CreateWithTx 测试创建节点
func TestWorkflowNodeStore_CreateWithTx(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()

	user := createUser(client)

	cases := []struct {
		CaseDoc  string
		GiveNode *ent.WorkflowNode
		SetUp    func(client *ent.Client) (*ent.Workflow, func())
		WantErr  error
	}{
		{
			CaseDoc: "成功创建节点",
			GiveNode: &ent.WorkflowNode{
				ID:          gofakeit.UUID(),
				Name:        gofakeit.Name(),
				Description: gofakeit.Sentence(50),
				Icon:        gofakeit.Word(),
				Type:        gofakeit.Word(),
				Version:     gofakeit.AppVersion(),
				Inputs: []*v1.NodeParam{
					{
						Id:          gofakeit.UUID(),
						Type:        gofakeit.Word(),
						Label:       gofakeit.Word(),
						Description: gofakeit.Sentence(50),
						Value:       gofakeit.Word(),
					}, {
						Id:          gofakeit.UUID(),
						Type:        gofakeit.Word(),
						Label:       gofakeit.Word(),
						Description: gofakeit.Sentence(50),
						Value:       gofakeit.Word(),
					},
				},
				Outputs: []*v1.NodeParam{
					{
						Id:          gofakeit.UUID(),
						Type:        gofakeit.Word(),
						Label:       gofakeit.Word(),
						Description: gofakeit.Sentence(50),
						Value:       gofakeit.Word(),
					}, {
						Id:          gofakeit.UUID(),
						Type:        gofakeit.Word(),
						Label:       gofakeit.Word(),
						Description: gofakeit.Sentence(50),
						Value:       gofakeit.Word(),
					},
				},
				Ports: []*v1.NodePort{
					{
						Id:   gofakeit.UUID(),
						Type: gofakeit.Word(),
					}, {
						Id:   gofakeit.UUID(),
						Type: gofakeit.Word(),
					},
				},
			},
			SetUp: func(client *ent.Client) (*ent.Workflow, func()) {
				w := createWorkflow(client, user)
				return w, func() {
					deleteWorkflow(client, w)
				}
			},
			WantErr: nil,
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			workflow, cleanup := tc.SetUp(client)
			defer cleanup()

			if tc.GiveNode != nil {
				tc.GiveNode.WorkflowID = workflow.ID
			}

			store := NewWorkflowNodeStore(client)
			var gotNode *ent.WorkflowNode
			var gotErr error
			database.WithTx(context.Background(), client, func(tx *ent.Tx) error {
				gotNode, gotErr = store.CreateWithTx(context.Background(), tx, workflow.ID, tc.GiveNode)
				return gotErr
			})
			assert.EqualExportedValues(t, tc.GiveNode, gotNode)
			assert.Equal(t, tc.WantErr, gotErr)
		})
	}
}

// TestWorkflowNodeStore_UpdateWithTx 测试更新节点
func TestWorkflowNodeStore_UpdateWithTx(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()
	user := createUser(client)

	cases := []struct {
		CaseDoc   string
		SetUp     func(client *ent.Client) (*ent.WorkflowNode, *ent.WorkflowNode, func())
		AssertErr func(err error) bool
	}{
		{
			CaseDoc: "成功更新节点",
			SetUp: func(client *ent.Client) (*ent.WorkflowNode, *ent.WorkflowNode, func()) {
				w := createWorkflow(client, user)
				node := createWorkflowNode(client, w.ID)
				newName := gofakeit.Name()
				newDescription := gofakeit.Sentence(50)
				newNodeType := gofakeit.Word()
				newIcon := gofakeit.Word()
				newVersion := gofakeit.AppVersion()

				giveNode := &ent.WorkflowNode{
					ID:          node.ID,
					WorkflowID:  node.WorkflowID,
					Name:        newName,
					Description: newDescription,
					Icon:        newIcon,
					Type:        newNodeType,
					Version:     newVersion,
					Inputs:      []*v1.NodeParam{},
					Outputs:     []*v1.NodeParam{},
					Ports:       []*v1.NodePort{},
				}
				node.Name = newName
				node.Description = newDescription
				node.Icon = newIcon
				node.Type = newNodeType
				node.Version = newVersion
				node.Inputs = []*v1.NodeParam{}
				node.Outputs = []*v1.NodeParam{}
				node.Ports = []*v1.NodePort{}

				return giveNode, node, func() {
					deleteWorkflow(client, w)
				}
			},
			AssertErr: func(err error) bool {
				return err == nil
			},
		}, {
			CaseDoc: "更新时节点不存在应返回NotFound错误",
			SetUp: func(client *ent.Client) (*ent.WorkflowNode, *ent.WorkflowNode, func()) {
				return &ent.WorkflowNode{ID: gofakeit.UUID(), WorkflowID: gofakeit.Int()}, nil, func() {}
			},
			AssertErr: func(err error) bool {
				return ent.IsNotFound(err)
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			giveNode, wantNode, cleanup := tc.SetUp(client)
			defer cleanup()

			store := NewWorkflowNodeStore(client)
			var gotNode *ent.WorkflowNode
			var gotErr error
			database.WithTx(context.Background(), client, func(tx *ent.Tx) error {
				gotNode, gotErr = store.UpdateWithTx(context.Background(), tx, giveNode)
				return gotErr
			})
			assert.EqualExportedValues(t, wantNode, gotNode)
			assert.True(t, tc.AssertErr(gotErr))
		})
	}
}

// TestWorkflowNodeStore_DeleteByWorkflowIdWithTx 测试删除某个工作流的所有节点
func TestWorkflowNodeStore_DeleteByWorkflowIdWithTx(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()

	user := createUser(client)

	cases := []struct {
		CaseDoc   string
		SetUp     func(client *ent.Client) (*ent.Workflow, func())
		WantCount int
		AssertErr func(err error) bool
	}{
		{
			CaseDoc: "成功删除某个工作流所有节点",
			SetUp: func(client *ent.Client) (*ent.Workflow, func()) {
				w := createWorkflow(client, user)
				// 创建三个节点
				createWorkflowNode(client, w.ID)
				createWorkflowNode(client, w.ID)
				createWorkflowNode(client, w.ID)
				return w, func() {
					deleteWorkflow(client, w)
				}
			},
			WantCount: 3,
			AssertErr: func(err error) bool {
				return err == nil
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			workflow, cleanup := tc.SetUp(client)
			defer cleanup()

			store := NewWorkflowNodeStore(client)
			var gotCount int
			var gotErr error
			database.WithTx(context.Background(), client, func(tx *ent.Tx) error {
				gotCount, gotErr = store.DeleteByWorkflowIdWithTx(context.Background(), tx, workflow.ID)
				return gotErr
			})
			assert.Equal(t, tc.WantCount, gotCount)
			assert.True(t, tc.AssertErr(gotErr))
		})
	}
}

// DeleteByWorkflowIdAndIdsNotInWithTx 测试删除某个工作流指定ID之外的节点
func TestWorkflowNodeStore_DeleteByWorkflowIdAndIdsNotInWithTx(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()
	user := createUser(client)

	cases := []struct {
		CaseDoc   string
		SetUp     func(client *ent.Client) (*ent.Workflow, []string, func())
		AssertErr func(err error) bool
	}{
		{
			CaseDoc: "成功删除指定ID之外的节点",
			SetUp: func(client *ent.Client) (*ent.Workflow, []string, func()) {
				w := createWorkflow(client, user)
				createWorkflowNode(client, w.ID)
				createWorkflowNode(client, w.ID)
				node3 := createWorkflowNode(client, w.ID)
				node4 := createWorkflowNode(client, w.ID)
				remainIds := make([]string, 0)
				remainIds = append(remainIds, node3.ID)
				remainIds = append(remainIds, node4.ID)
				return w, remainIds, func() {
					deleteWorkflow(client, w)
				}
			},
			AssertErr: func(err error) bool {
				return err == nil
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			w, remainIds, cleanup := tc.SetUp(client)
			defer cleanup()

			store := NewWorkflowNodeStore(client)
			var gotErr error
			database.WithTx(context.Background(), client, func(tx *ent.Tx) error {
				gotErr = store.DeleteByWorkflowIdAndIdsNotInWithTx(context.Background(), tx, w.ID, remainIds)
				return gotErr
			})
			gotRemainIds := make([]string, 0)
			remainNodes := client.WorkflowNode.Query().Where(workflownode.WorkflowID(w.ID)).AllX(context.Background())
			for _, remainNode := range remainNodes {
				gotRemainIds = append(gotRemainIds, remainNode.ID)
			}
			assert.Equal(t, remainIds, gotRemainIds)
			assert.True(t, tc.AssertErr(gotErr))
		})
	}
}

// TestWorkflowNodeStore_GetById 测试根据ID获取节点
func TestWorkflowNodeStore_GetById(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()
	user := createUser(client)

	cases := []struct {
		CaseDoc   string
		SetUp     func(client *ent.Client) (int, string, *ent.WorkflowNode, func())
		AssertErr func(err error) bool
	}{
		{
			CaseDoc: "成功获取指定ID节点",
			SetUp: func(client *ent.Client) (int, string, *ent.WorkflowNode, func()) {
				w := createWorkflow(client, user)
				node := createWorkflowNode(client, w.ID)
				return w.ID, node.ID, node, func() {
					deleteWorkflow(client, w)
				}
			},
			AssertErr: func(err error) bool {
				return err == nil
			},
		}, {
			CaseDoc: "获取不存在的节点时应返回NotFound错误",
			SetUp: func(client *ent.Client) (int, string, *ent.WorkflowNode, func()) {
				return gofakeit.Int(), gofakeit.UUID(), nil, func() {}
			},
			AssertErr: func(err error) bool {
				return ent.IsNotFound(err)
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			workflowId, nodeId, wantNode, cleanup := tc.SetUp(client)
			defer cleanup()

			store := NewWorkflowNodeStore(client)
			gotNode, gotErr := store.GetById(context.Background(), workflowId, nodeId)
			assert.EqualExportedValues(t, wantNode, gotNode)
			assert.True(t, tc.AssertErr(gotErr))
		})
	}
}
