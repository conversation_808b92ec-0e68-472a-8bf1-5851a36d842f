package store

import (
	"context"
	"errors"
	"github.com/brianvoe/gofakeit/v7"
	_ "github.com/mattn/go-sqlite3"
	"github.com/stretchr/testify/assert"
	"resflow/ent"
	"resflow/ent/workflow"
	"resflow/ent/workflowlink"
	"resflow/ent/workflownode"
	"resflow/enums"
	v1 "resflow/proto/generated_go/v1"
	"testing"
)

func createWorkflow(client *ent.Client, user *ent.User) *ent.Workflow {
	return client.Workflow.Create().SetUser(user).SetName(gofakeit.Name()).SetDescription(gofakeit.Sentence(50)).SetState(enums.Active).SaveX(context.Background())
}

func deleteWorkflow(client *ent.Client, w *ent.Workflow) {
	client.WorkflowNode.Delete().Where(workflownode.WorkflowID(w.ID)).ExecX(context.Background())
	client.WorkflowLink.Delete().Where(workflowlink.WorkflowID(w.ID)).ExecX(context.Background())
	client.Workflow.DeleteOne(w).ExecX(context.Background())
}

func SetUpUser(ctx context.Context, client *ent.Client) {
	userStore := NewUserStore(client)
	_, err := userStore.Create(ctx, &ent.User{
		Username: "test",
		Password: "test",
		Nickname: "test",
		State:    1,
	})
	if err != nil {
		panic(err)
	}
}

// TestWorkflowStore_CreateWithTx 测试创建
func TestWorkflowStore_CreateWithTx(t *testing.T) {
	cases := []struct {
		CaseDoc      string
		Ctx          context.Context
		GiveWorkflow *ent.Workflow
		CreateCall   func(tx *ent.Tx, workflow *ent.Workflow) error
		WantWorkflow *ent.Workflow
		WantErr      error
	}{
		{
			CaseDoc: "成功创建工作流",
			Ctx:     context.Background(),
			GiveWorkflow: &ent.Workflow{
				Name:        gofakeit.Name(),
				Description: gofakeit.Sentence(50),
				State:       enums.Active,
				Viewport: &v1.WorkflowViewport{
					X:    gofakeit.Float32(),
					Y:    gofakeit.Float32(),
					Zoom: gofakeit.Float32(),
				},
			},
			CreateCall: func(tx *ent.Tx, workflow *ent.Workflow) error {
				return nil
			},
			WantWorkflow: &ent.Workflow{
				ID:    1,
				State: enums.Active,
			},
			WantErr: nil,
		}, {
			CaseDoc: "当CreateCall出错时应返回错误",
			Ctx:     context.Background(),
			GiveWorkflow: &ent.Workflow{
				Name:        gofakeit.Name(),
				Description: gofakeit.Sentence(50),
				State:       enums.Active,
				Viewport: &v1.WorkflowViewport{
					X:    gofakeit.Float32(),
					Y:    gofakeit.Float32(),
					Zoom: gofakeit.Float32(),
				},
			},
			CreateCall: func(tx *ent.Tx, workflow *ent.Workflow) error {
				return errors.New("create call 出错")
			},
			WantWorkflow: nil,
			WantErr:      errors.New("create call 出错"),
		},
	}
	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			client := BuildTestClient(t, tc.Ctx, SetUpUser)
			defer client.Close()
			user := createUser(client)

			if tc.WantWorkflow != nil {
				tc.WantWorkflow.Name = tc.GiveWorkflow.Name
				tc.WantWorkflow.Description = tc.GiveWorkflow.Description
				tc.WantWorkflow.UserID = user.ID
				tc.WantWorkflow.Viewport = tc.GiveWorkflow.Viewport
			}

			store := NewWorkflowStore(client)
			gotWorkflow, gotErr := store.CreateWithTx(tc.Ctx, user.ID, tc.GiveWorkflow, tc.CreateCall)
			assert.EqualExportedValues(t, tc.WantWorkflow, gotWorkflow)
			assert.Equal(t, tc.WantErr, gotErr)
		})
	}
}

// TestWorkflowStore_GetById 测试根据ID获取工作流
func TestWorkflowStore_GetById(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()

	user := createUser(client)

	cases := []struct {
		CaseDoc      string
		Ctx          context.Context
		SetUp        func(client *ent.Client) (*ent.Workflow, func())
		WantWorkflow *ent.Workflow
		AssertErr    func(err error) bool
	}{
		{
			CaseDoc: "成功查询存在的工作流",
			Ctx:     context.Background(),
			SetUp: func(client *ent.Client) (*ent.Workflow, func()) {
				w := client.Workflow.Create().SetName(gofakeit.Name()).SetDescription(gofakeit.Sentence(50)).SetState(enums.Active).SetUserID(user.ID).SetViewport(&v1.WorkflowViewport{
					X:    gofakeit.Float32(),
					Y:    gofakeit.Float32(),
					Zoom: gofakeit.Float32(),
				}).SaveX(context.Background())
				return w, func() {
					client.Workflow.DeleteOneID(w.ID).ExecX(context.Background())
				}
			},
			WantWorkflow: &ent.Workflow{
				UserID: user.ID,
				State:  enums.Active,
				Edges: ent.WorkflowEdges{
					Nodes: []*ent.WorkflowNode{},
					Links: []*ent.WorkflowLink{},
				},
			},
			AssertErr: func(err error) bool {
				return err == nil
			},
		}, {
			CaseDoc: "工作流不存在时应返回NotFound错误",
			Ctx:     context.Background(),
			SetUp: func(client *ent.Client) (*ent.Workflow, func()) {
				return &ent.Workflow{ID: 999}, func() {}
			},
			WantWorkflow: nil,
			AssertErr: func(err error) bool {
				return ent.IsNotFound(err)
			},
		},
	}
	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {

			w, cleanup := tc.SetUp(client)
			defer cleanup()

			if tc.WantWorkflow != nil {
				tc.WantWorkflow.ID = w.ID
				tc.WantWorkflow.Name = w.Name
				tc.WantWorkflow.Description = w.Description
				tc.WantWorkflow.Viewport = w.Viewport
			}

			store := NewWorkflowStore(client)
			gotWorkflow, err := store.GetById(context.Background(), w.ID)
			assert.EqualExportedValues(t, tc.WantWorkflow, gotWorkflow)
			assert.True(t, tc.AssertErr(err))
		})
	}
}

// TestWorkflowStore_Update 测试更新工作流
func TestWorkflowStore_UpdateWithTx(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()
	user := createUser(client)

	updateErr := errors.New("update call 出错")

	cases := []struct {
		CaseDoc      string
		Ctx          context.Context
		SetUp        func(client *ent.Client) (*ent.Workflow, func())
		UpdateCall   func(tx *ent.Tx) error
		WantWorkflow *ent.Workflow
		AssertErr    func(err error) bool
	}{
		{
			CaseDoc: "成功更新存在的工作流",
			Ctx:     context.Background(),
			SetUp: func(client *ent.Client) (*ent.Workflow, func()) {
				w := client.Workflow.Create().SetUserID(user.ID).SetName(gofakeit.Name()).SetDescription(gofakeit.ProductDescription()).SetState(enums.Active).SetViewport(&v1.WorkflowViewport{
					X:    gofakeit.Float32(),
					Y:    gofakeit.Float32(),
					Zoom: gofakeit.Float32(),
				}).SaveX(context.Background())
				return w, func() {
					client.Workflow.DeleteOneID(w.ID).ExecX(context.Background())
				}
			},
			UpdateCall: func(tx *ent.Tx) error {
				return nil
			},
			WantWorkflow: &ent.Workflow{
				UserID:      user.ID,
				Name:        gofakeit.Name(),
				Description: gofakeit.Sentence(50),
				State:       enums.Inactive,
				Edges: ent.WorkflowEdges{
					Nodes: []*ent.WorkflowNode{},
					Links: []*ent.WorkflowLink{},
				},
			},
			AssertErr: func(err error) bool {
				return err == nil
			},
		}, {
			CaseDoc: "更新不存在的工作流时应返回NotFound错误",
			Ctx:     context.Background(),
			SetUp: func(client *ent.Client) (*ent.Workflow, func()) {
				return &ent.Workflow{
					ID: 999,
				}, func() {}
			},
			UpdateCall: func(tx *ent.Tx) error {
				return nil
			},
			WantWorkflow: nil,
			AssertErr: func(err error) bool {
				return ent.IsNotFound(err)
			},
		}, {
			CaseDoc: "当UpdateCall出错时应返回错误",
			Ctx:     context.Background(),
			SetUp: func(client *ent.Client) (*ent.Workflow, func()) {
				w := client.Workflow.Create().SetUserID(user.ID).SetName(gofakeit.Name()).SetDescription(gofakeit.ProductDescription()).SetState(enums.Active).SetViewport(&v1.WorkflowViewport{
					X:    gofakeit.Float32(),
					Y:    gofakeit.Float32(),
					Zoom: gofakeit.Float32(),
				}).SaveX(context.Background())
				return w, func() {
					client.Workflow.DeleteOneID(w.ID).ExecX(context.Background())
				}
			},
			UpdateCall: func(tx *ent.Tx) error {
				return updateErr
			},
			WantWorkflow: nil,
			AssertErr: func(err error) bool {
				return errors.Is(err, updateErr)
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			w, cleanup := tc.SetUp(client)
			defer cleanup()

			if tc.WantWorkflow != nil {
				tc.WantWorkflow.ID = w.ID
				w.Name = tc.WantWorkflow.Name
				w.Description = tc.WantWorkflow.Description
				w.State = tc.WantWorkflow.State
				w.Viewport = tc.WantWorkflow.Viewport
			}

			store := NewWorkflowStore(client)
			gotWorkflow, err := store.UpdateWithTx(tc.Ctx, w, tc.UpdateCall)
			assert.EqualExportedValues(t, tc.WantWorkflow, gotWorkflow)
			assert.True(t, tc.AssertErr(err))
		})
	}
}

// TestWorkflowStore_DeleteById 测试根据ID删除工作流
func TestWorkflowStore_DeleteById(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()
	user := createUser(client)

	deleteCallErr := errors.New("delete call 出错")

	cases := []struct {
		CaseDoc    string
		Ctx        context.Context
		SetUp      func(client *ent.Client) (int, func())
		DeleteCall func(tx *ent.Tx) error
		WantErr    func(err error) bool
	}{
		{
			CaseDoc: "成功删除存在的工作流",
			Ctx:     context.Background(),
			SetUp: func(client *ent.Client) (int, func()) {
				w := client.Workflow.Create().SetUserID(user.ID).SetName(gofakeit.Name()).SetDescription(gofakeit.ProductDescription()).SetState(enums.Active).SetViewport(&v1.WorkflowViewport{
					X:    gofakeit.Float32(),
					Y:    gofakeit.Float32(),
					Zoom: gofakeit.Float32(),
				}).SaveX(context.Background())
				return w.ID, func() {}
			},
			DeleteCall: func(tx *ent.Tx) error {
				return nil
			},
			WantErr: func(err error) bool {
				return err == nil
			},
		}, {
			CaseDoc: "删除不存在的工作流时应返回NotFound错误",
			Ctx:     context.Background(),
			SetUp: func(client *ent.Client) (int, func()) {
				return 999, func() {}
			},
			DeleteCall: func(tx *ent.Tx) error {
				return nil
			},
			WantErr: func(err error) bool {
				return ent.IsNotFound(err)
			},
		}, {
			CaseDoc: "当DeleteCall出错时应返回错误",
			Ctx:     context.Background(),
			SetUp: func(client *ent.Client) (int, func()) {
				w := client.Workflow.Create().SetUserID(user.ID).SetName(gofakeit.Name()).SetDescription(gofakeit.ProductDescription()).SetState(enums.Active).SetViewport(&v1.WorkflowViewport{
					X:    gofakeit.Float32(),
					Y:    gofakeit.Float32(),
					Zoom: gofakeit.Float32(),
				}).SaveX(context.Background())
				return w.ID, func() {}
			},
			DeleteCall: func(tx *ent.Tx) error {
				return deleteCallErr
			},
			WantErr: func(err error) bool {
				return errors.Is(err, deleteCallErr)
			},
		},
	}
	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			id, cleanup := tc.SetUp(client)
			defer cleanup()

			store := NewWorkflowStore(client)
			err := store.DeleteById(tc.Ctx, id, tc.DeleteCall)
			assert.True(t, tc.WantErr(err))
		})
	}
}

func TestWorkflowStore_List(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()
	user := createUser(client)
	cases := []struct {
		CaseDoc   string
		Ctx       context.Context
		SetUp     func(client *ent.Client) ([]*ent.Workflow, func())
		AssertErr func(err error) bool
	}{
		{
			CaseDoc: "成功获取工作流列表",
			Ctx:     context.Background(),
			SetUp: func(client *ent.Client) ([]*ent.Workflow, func()) {
				workflows := make([]*ent.Workflow, 0)
				for i := 0; i < 10; i++ {
					w := client.Workflow.Create().SetUserID(user.ID).SetName(gofakeit.Name()).SetDescription(gofakeit.ProductDescription()).SetState(enums.Active).SetViewport(&v1.WorkflowViewport{
						X:    gofakeit.Float32(),
						Y:    gofakeit.Float32(),
						Zoom: gofakeit.Float32(),
					}).SaveX(context.Background())
					w = client.Workflow.Query().Where(workflow.ID(w.ID)).WithNodes().WithLinks().OnlyX(context.Background())
					workflows = append(workflows, w)
				}
				return workflows, func() {
					for _, w := range workflows {
						client.Workflow.DeleteOneID(w.ID).ExecX(context.Background())
					}
				}
			},
			AssertErr: func(err error) bool {
				return err == nil
			},
		}, {
			CaseDoc: "当工作流为空时返回空数组",
			Ctx:     context.Background(),
			SetUp: func(client *ent.Client) ([]*ent.Workflow, func()) {
				return []*ent.Workflow{}, func() {}
			},
			AssertErr: func(err error) bool {
				return err == nil
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			workflows, cleanup := tc.SetUp(client)
			defer cleanup()
			store := NewWorkflowStore(client)
			gotWorkflows, err := store.List(tc.Ctx)
			assert.EqualExportedValues(t, workflows, gotWorkflows)
			assert.True(t, tc.AssertErr(err))
		})
	}
}
