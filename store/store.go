package store

import (
	"resflow/ent"
	"resflow/repo"
)

type Store struct {
	UserStore         repo.User
	WorkflowStore     repo.Workflow
	WorkflowNodeStore repo.WorkflowNode
	WorkflowLinkStore repo.WorkflowLink
}

func NewStore(client *ent.Client) *Store {
	return &Store{
		UserStore:         NewUserStore(client),
		WorkflowStore:     NewWorkflowStore(client),
		WorkflowNodeStore: NewWorkflowNodeStore(client),
		WorkflowLinkStore: NewWorkflowLinkStore(client),
	}
}
