package store

import (
	"context"
	"github.com/brianvoe/gofakeit/v7"
	"github.com/stretchr/testify/assert"
	"resflow/ent"
	"resflow/enums"
	"testing"
)

func createUser(client *ent.Client) *ent.User {
	user := client.User.Create().SetUsername(gofakeit.Username()).SetNickname(gofakeit.Name()).SetPassword(gofakeit.Password(true, true, true, true, false, 8)).SetState(enums.Active).SaveX(context.Background())
	return user
}

func TestUserStore_Create(t *testing.T) {
	cases := []struct {
		CaseDoc  string
		Ctx      context.Context
		GiveUser *ent.User
		WantUser *ent.User
		WantErr  error
	}{
		{
			CaseDoc: "创建用户",
			Ctx:     context.Background(),
			GiveUser: &ent.User{
				Username: "test",
				Nickname: "test",
				Password: "test",
				State:    enums.Active,
			},
			WantUser: &ent.User{
				ID:       1,
				Username: "test",
				Nickname: "test",
				Password: "test",
				State:    enums.Active,
			},
			WantErr: nil,
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			client := BuildTestClient(t, tc.Ctx)
			defer client.Close()

			userStore := NewUserStore(client)
			gotUser, err := userStore.Create(tc.Ctx, tc.GiveUser)
			assert.EqualExportedValues(t, tc.WantUser, gotUser)
			assert.Equal(t, tc.WantErr, err)
		})
	}
}

func TestUserStore_GetByUsername(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()
	user, err := client.User.Create().SetUsername("test").SetNickname("test").SetPassword("test").SetState(enums.Active).Save(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	userStore := NewUserStore(client)
	gotUser, err := userStore.GetByUsername(context.Background(), "test")
	assert.EqualExportedValues(t, user, gotUser)
	assert.Equal(t, nil, err)
}

func TestUserStore_GetById(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()

	wantUser, err := client.User.Create().SetUsername("test").SetNickname("test").SetPassword("test").SetState(enums.Active).Save(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	userStore := NewUserStore(client)
	gotUser, err := userStore.GetById(context.Background(), 1)
	assert.EqualExportedValues(t, wantUser, gotUser)
	assert.Equal(t, nil, err)
}
