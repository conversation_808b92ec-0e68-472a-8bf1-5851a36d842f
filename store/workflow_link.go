package store

import (
	"context"
	"github.com/google/uuid"
	"resflow/ent"
	"resflow/ent/workflowlink"
	"resflow/repo"
)

type WorkflowLinkStore struct {
	client *ent.Client
}

func NewWorkflowLinkStore(client *ent.Client) repo.WorkflowLink {
	return &WorkflowLinkStore{
		client: client,
	}
}

func (s *WorkflowLinkStore) CreateWithTx(ctx context.Context, tx *ent.Tx, workflowID uuid.UUID, link *ent.WorkflowLink) (*ent.WorkflowLink, error) {
	return tx.WorkflowLink.Create().SetWorkflowID(workflowID).SetFromNodeID(link.FromNodeID).SetToNodeID(link.ToNodeID).SetFromPortID(link.FromPortID).SetToPortID(link.ToPortID).SetType(link.Type).Save(ctx)
}

func (s *WorkflowLinkStore) UpdateWithTx(ctx context.Context, tx *ent.Tx, link *ent.WorkflowLink) (*ent.WorkflowLink, error) {
	return tx.WorkflowLink.UpdateOneID(link.ID).SetFromNodeID(link.FromNodeID).SetToNodeID(link.ToNodeID).SetFromPortID(link.FromPortID).SetToPortID(link.ToPortID).SetType(link.Type).Save(ctx)
}

func (s *WorkflowLinkStore) DeleteByWorkflowIdWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID) (int, error) {
	return tx.WorkflowLink.Delete().Where(workflowlink.WorkflowID(workflowId)).Exec(ctx)
}

func (s *WorkflowLinkStore) DeleteByWorkflowIdAndIdsNotInWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, ids []uuid.UUID) error {
	_, err := tx.WorkflowLink.Delete().Where(workflowlink.WorkflowID(workflowId)).Where(workflowlink.IDNotIn(ids...)).Exec(ctx)
	return err
}

func (s *WorkflowLinkStore) GetById(ctx context.Context, id uuid.UUID) (*ent.WorkflowLink, error) {
	return s.client.WorkflowLink.Query().Where(workflowlink.ID(id)).First(ctx)
}
