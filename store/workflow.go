package store

import (
	"context"
	"github.com/google/uuid"
	"resflow/database"
	"resflow/ent"
	"resflow/ent/workflow"
	"resflow/repo"
)

type WorkflowStore struct {
	client *ent.Client
}

func NewWorkflowStore(client *ent.Client) repo.Workflow {
	return &WorkflowStore{
		client: client,
	}
}

func (s *WorkflowStore) Create(ctx context.Context, user *ent.User, w *ent.Workflow, nodes []*ent.WorkflowNode, links []*ent.WorkflowLink) (*ent.Workflow, error) {
	var _workflow *ent.Workflow
	var err error

	err = database.WithTx(ctx, s.client, func(tx *ent.Tx) error {
		// _workflow
		_workflow, err = tx.Workflow.Create().SetUser(user).SetName(w.Name).SetDescription(w.Description).SetStatus(w.Status).SetViewport(w.Viewport).Save(ctx)
		if err != nil {
			return err
		}

		_nodes := make([]*ent.WorkflowNodeCreate, 0)
		for _, node := range nodes {
			_nodes = append(_nodes, tx.WorkflowNode.Create().SetID(node.ID).SetName(node.Name).SetDescription(node.Description).SetIcon(node.Icon).SetVersion(node.Version).SetType(node.Type).SetPluginName(node.PluginName).SetPluginVersion(node.PluginVersion).SetInputParams(node.InputParams).SetInputValues(node.InputValues).SetOutputParams(node.OutputParams).SetOutputValues(node.OutputValues).SetInputPorts(node.InputPorts).SetOutputPorts(node.OutputPorts).SetWorkflow(_workflow))
		}

		_, err = tx.WorkflowNode.CreateBulk(_nodes...).Save(ctx)
		if err != nil {
			return err
		}

		_links := make([]*ent.WorkflowLinkCreate, 0)
		for _, link := range links {
			_links = append(_links, tx.WorkflowLink.Create().SetFromNodeID(link.FromNodeID).SetToNodeID(link.ToNodeID).SetFromPortID(link.FromPortID).SetToPortID(link.ToPortID).SetWorkflow(_workflow))
		}

		_, err = tx.WorkflowLink.CreateBulk(_links...).Save(ctx)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return _workflow, nil
}

func (s *WorkflowStore) GetById(ctx context.Context, id uuid.UUID) (*ent.Workflow, error) {
	return s.client.Workflow.Query().Where(workflow.ID(id)).WithNodes().WithLinks().First(ctx)
}

func (s *WorkflowStore) CreateWithTx(ctx context.Context, userId uuid.UUID, w *ent.Workflow, createCall func(tx *ent.Tx, workflow *ent.Workflow) error) (*ent.Workflow, error) {
	var _workflow *ent.Workflow
	var err error
	err = database.WithTx(ctx, s.client, func(tx *ent.Tx) error {
		_workflow, err = tx.Workflow.Create().SetUserID(userId).SetName(w.Name).SetDescription(w.Description).SetIcon(w.Icon).SetStatus(w.Status).SetViewport(w.Viewport).Save(ctx)
		if err != nil {
			return err
		}

		err = createCall(tx, _workflow)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}
	return _workflow, nil
}

func (s *WorkflowStore) UpdateWithTx(ctx context.Context, w *ent.Workflow, updateCall func(tx *ent.Tx) error) (*ent.Workflow, error) {
	err := database.WithTx(ctx, s.client, func(tx *ent.Tx) error {
		// 更新工作流
		_, err := tx.Workflow.UpdateOneID(w.ID).SetName(w.Name).SetDescription(w.Description).SetStatus(w.Status).SetViewport(w.Viewport).Save(ctx)
		if err != nil {
			return err
		}

		err = updateCall(tx)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}
	return s.GetById(ctx, w.ID)
}

func (s *WorkflowStore) DeleteById(ctx context.Context, id uuid.UUID, deleteCall func(tx *ent.Tx) error) error {
	return database.WithTx(ctx, s.client, func(tx *ent.Tx) error {

		if deleteCall != nil {
			err := deleteCall(tx)
			if err != nil {
				return err
			}
		}

		// 删除工作流
		err := tx.Workflow.DeleteOneID(id).Exec(ctx)
		if err != nil {
			return err
		}
		return nil
	})
}

func (s *WorkflowStore) List(ctx context.Context) ([]*ent.Workflow, error) {
	return s.client.Workflow.Query().WithNodes().WithLinks().All(ctx)
}
