package store

import (
	"context"
	"github.com/google/uuid"
	"resflow/ent"
	"resflow/ent/user"
	"resflow/repo"
)

type UserStore struct {
	client *ent.Client
}

func NewUserStore(client *ent.Client) repo.User {
	return &UserStore{
		client: client,
	}
}

func (s *UserStore) Create(context context.Context, user *ent.User) (*ent.User, error) {
	return s.client.User.Create().SetUsername(user.Username).SetPassword(user.Password).SetStatus(user.Status).SetNickname(user.Nickname).Save(context)
}

func (s *UserStore) GetByUsername(context context.Context, username string) (*ent.User, error) {
	return s.client.User.Query().Where(user.Username(username)).First(context)
}

func (s *UserStore) GetById(context context.Context, id uuid.UUID) (*ent.User, error) {
	return s.client.User.Query().Where(user.ID(id)).First(context)
}
