package store

import (
	"context"
	"github.com/google/uuid"
	"resflow/ent"
	"resflow/ent/workflownode"
	"resflow/repo"
)

type WorkflowNodeStore struct {
	client *ent.Client
}

func NewWorkflowNodeStore(client *ent.Client) repo.WorkflowNode {
	return &WorkflowNodeStore{client: client}
}

func (s *WorkflowNodeStore) CreateWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, node *ent.WorkflowNode) (*ent.WorkflowNode, error) {
	return tx.WorkflowNode.Create().SetID(node.ID).SetWorkflowID(workflowId).SetName(node.Name).SetDescription(node.Description).SetType(node.Type).SetIcon(node.Icon).SetVersion(node.Version).SetPluginName(node.PluginName).SetPluginVersion(node.PluginVersion).SetInputParams(node.InputParams).SetInputValues(node.InputValues).SetOutputParams(node.OutputParams).SetOutputValues(node.OutputValues).SetInputPorts(node.InputPorts).SetOutputPorts(node.OutputPorts).SetPosition(node.Position).Save(ctx)
}

func (s *WorkflowNodeStore) UpdateWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, node *ent.WorkflowNode) (*ent.WorkflowNode, error) {
	return tx.WorkflowNode.UpdateOneID(node.ID).Where(workflownode.WorkflowID(workflowId)).SetName(node.Name).SetDescription(node.Description).SetType(node.Type).SetIcon(node.Icon).SetVersion(node.Version).SetPluginName(node.PluginName).SetPluginVersion(node.PluginVersion).SetInputParams(node.InputParams).SetInputValues(node.InputValues).SetOutputParams(node.OutputParams).SetOutputValues(node.OutputValues).SetInputPorts(node.InputPorts).SetOutputPorts(node.OutputPorts).SetPosition(node.Position).Save(ctx)
}

func (s *WorkflowNodeStore) DeleteByWorkflowIdWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID) (int, error) {
	return tx.WorkflowNode.Delete().Where(workflownode.WorkflowID(workflowId)).Exec(ctx)
}

func (s *WorkflowNodeStore) DeleteByWorkflowIdAndIdsNotInWithTx(ctx context.Context, tx *ent.Tx, workflowId uuid.UUID, ids []uuid.UUID) error {
	_, err := tx.WorkflowNode.Delete().Where(workflownode.WorkflowID(workflowId)).Where(workflownode.IDNotIn(ids...)).Exec(ctx)
	return err
}

func (s *WorkflowNodeStore) GetById(ctx context.Context, workflowId uuid.UUID, id uuid.UUID) (*ent.WorkflowNode, error) {
	return s.client.WorkflowNode.Query().Where(workflownode.WorkflowID(workflowId)).Where(workflownode.ID(id)).First(ctx)
}
