package store

import (
	"context"
	"github.com/brianvoe/gofakeit/v7"
	"github.com/stretchr/testify/assert"
	"resflow/database"
	"resflow/ent"
	"resflow/ent/workflowlink"
	"testing"
)

func createWorkflowLink(client *ent.Client, workflowId int) *ent.WorkflowLink {
	return client.WorkflowLink.Create().SetWorkflowID(workflowId).SetFromNode(gofakeit.UUID()).SetToNode(gofakeit.UUID()).SetFromPort(gofakeit.UUID()).SetToPort(gofakeit.UUID()).SaveX(context.Background())
}

func TestWorkflowLinkStore_CreateWithTx(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()

	user := createUser(client)

	cases := []struct {
		CaseDoc   string
		GiveLink  *ent.WorkflowLink
		SetUp     func(client *ent.Client) (*ent.Workflow, func())
		AssertErr func(err error) bool
	}{
		{
			CaseDoc: "成功创建边",
			GiveLink: &ent.WorkflowLink{
				FromNode: gofakeit.UUID(),
				ToNode:   gofakeit.UUID(),
				FromPort: gofakeit.UUID(),
				ToPort:   gofakeit.UUID(),
			},
			SetUp: func(client *ent.Client) (*ent.Workflow, func()) {
				w := createWorkflow(client, user)
				return w, func() {
					deleteWorkflow(client, w)
				}
			},
			AssertErr: func(err error) bool {
				return err == nil
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			w, cleaup := tc.SetUp(client)
			defer cleaup()

			store := NewWorkflowLinkStore(client)
			var gotLink *ent.WorkflowLink
			var gotErr error
			database.WithTx(context.Background(), client, func(tx *ent.Tx) error {
				gotLink, gotErr = store.CreateWithTx(context.Background(), tx, w.ID, tc.GiveLink)
				return gotErr
			})
			assert.Equal(t, w.ID, gotLink.WorkflowID)
			assert.Equal(t, tc.GiveLink.FromNode, gotLink.FromNode)
			assert.Equal(t, tc.GiveLink.ToNode, gotLink.ToNode)
			assert.Equal(t, tc.GiveLink.FromPort, gotLink.FromPort)
			assert.Equal(t, tc.GiveLink.ToPort, gotLink.ToPort)
			assert.True(t, tc.AssertErr(gotErr))
		})
	}
}

// TestWorkflowLinkStore_UpdateWithTx 测试更新边
func TestWorkflowLinkStore_UpdateWithTx(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()
	user := createUser(client)
	cases := []struct {
		CaseDoc   string
		SetUp     func(client *ent.Client) (*ent.WorkflowLink, *ent.WorkflowLink, func())
		AssertErr func(err error) bool
	}{
		{
			CaseDoc: "成功更新边",
			SetUp: func(client *ent.Client) (*ent.WorkflowLink, *ent.WorkflowLink, func()) {
				w := createWorkflow(client, user)
				link := createWorkflowLink(client, w.ID)
				newFromNode := gofakeit.UUID()
				newToNode := gofakeit.UUID()
				newFromPort := gofakeit.UUID()
				newToPort := gofakeit.UUID()
				giveLink := &ent.WorkflowLink{
					ID:         link.ID,
					WorkflowID: link.WorkflowID,
					FromNode:   newFromNode,
					ToNode:     newToNode,
					FromPort:   newFromPort,
					ToPort:     newToPort,
				}
				link.FromNode = newFromNode
				link.ToNode = newToNode
				link.FromPort = newFromPort
				link.ToPort = newToPort
				return giveLink, link, func() {
					deleteWorkflow(client, w)
				}
			},
			AssertErr: func(err error) bool {
				return err == nil
			},
		}, {
			CaseDoc: "更新边时当边不存在应返回NotFound错误",
			SetUp: func(client *ent.Client) (*ent.WorkflowLink, *ent.WorkflowLink, func()) {
				return &ent.WorkflowLink{
					ID:         gofakeit.Int(),
					WorkflowID: gofakeit.Int(),
					FromNode:   gofakeit.UUID(),
					ToNode:     gofakeit.UUID(),
					FromPort:   gofakeit.UUID(),
					ToPort:     gofakeit.UUID(),
				}, nil, func() {}
			},
			AssertErr: func(err error) bool {
				return ent.IsNotFound(err)
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			giveLink, wantLink, cleanup := tc.SetUp(client)
			defer cleanup()

			store := NewWorkflowLinkStore(client)
			var gotLink *ent.WorkflowLink
			var gotErr error
			database.WithTx(context.Background(), client, func(tx *ent.Tx) error {
				gotLink, gotErr = store.UpdateWithTx(context.Background(), tx, giveLink)
				return gotErr
			})
			assert.EqualExportedValues(t, wantLink, gotLink)
			assert.True(t, tc.AssertErr(gotErr))
		})
	}
}

// TestWorkflowLinkStore_DeleteByWorkflowIdWithTx 测试删除指定工作流所有边
func TestWorkflowLinkStore_DeleteByWorkflowIdWithTx(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()
	user := createUser(client)

	cases := []struct {
		CaseDoc   string
		SetUp     func(client *ent.Client) (*ent.Workflow, func())
		WantCount int
		AssertErr func(err error) bool
	}{
		{
			CaseDoc: "成功删除指定工作流所有边",
			SetUp: func(client *ent.Client) (*ent.Workflow, func()) {
				w := createWorkflow(client, user)
				createWorkflowLink(client, w.ID)
				createWorkflowLink(client, w.ID)
				createWorkflowLink(client, w.ID)
				return w, func() {
					deleteWorkflow(client, w)
				}
			},
			WantCount: 3,
			AssertErr: func(err error) bool {
				return err == nil
			},
		},
	}
	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			w, cleanup := tc.SetUp(client)
			defer cleanup()
			store := NewWorkflowLinkStore(client)
			var gotCount int
			var gotErr error
			database.WithTx(context.Background(), client, func(tx *ent.Tx) error {
				gotCount, gotErr = store.DeleteByWorkflowIdWithTx(context.Background(), tx, w.ID)
				return gotErr
			})
			assert.Equal(t, tc.WantCount, gotCount)
			assert.True(t, tc.AssertErr(gotErr))
		})
	}
}

// TestWorkflowLinkStore_DeleteByWorkflowIdAndIdsNotInWithTx 测试删除某工作流指定ID之外的边
func TestWorkflowLinkStore_DeleteByWorkflowIdAndIdsNotInWithTx(t *testing.T) {
	t.Run("测试删除指定ID之外的边", func(t *testing.T) {
		client := BuildTestClient(t, context.Background())
		defer client.Close()
		user := createUser(client)

		// 创建第一个工作流
		workflow1 := createWorkflow(client, user)
		link1 := createWorkflowLink(client, workflow1.ID)
		link2 := createWorkflowLink(client, workflow1.ID)
		createWorkflowLink(client, workflow1.ID)
		createWorkflowLink(client, workflow1.ID)
		// 创建第二个工作流
		workflow2 := createWorkflow(client, user)
		link5 := createWorkflowLink(client, workflow2.ID)
		link6 := createWorkflowLink(client, workflow2.ID)

		giveIds := make([]int, 0)
		giveIds = append(giveIds, link1.ID)
		giveIds = append(giveIds, link2.ID)

		store := NewWorkflowLinkStore(client)
		var gotErr error
		database.WithTx(context.Background(), client, func(tx *ent.Tx) error {
			return store.DeleteByWorkflowIdAndIdsNotInWithTx(context.Background(), tx, workflow1.ID, giveIds)
		})
		workflow1Links := client.WorkflowLink.Query().Where(workflowlink.WorkflowID(workflow1.ID)).AllX(context.Background())
		gotWorkflow1Ids := make([]int, 0)
		for _, link := range workflow1Links {
			gotWorkflow1Ids = append(gotWorkflow1Ids, link.ID)
		}
		assert.Equal(t, giveIds, gotWorkflow1Ids)
		wantWorkflow2Ids := []int{link5.ID, link6.ID}
		workflow2Links := client.WorkflowLink.Query().Where(workflowlink.WorkflowID(workflow2.ID)).AllX(context.Background())
		gotWorkflow2Ids := make([]int, 0)
		for _, link := range workflow2Links {
			gotWorkflow2Ids = append(gotWorkflow2Ids, link.ID)
		}
		assert.Equal(t, wantWorkflow2Ids, gotWorkflow2Ids)
		assert.Equal(t, nil, gotErr)
	})
}

func TestWorkflowLinkStore_GetById(t *testing.T) {
	client := BuildTestClient(t, context.Background())
	defer client.Close()
	user := createUser(client)

	workflow := createWorkflow(client, user)
	wantLink := createWorkflowLink(client, workflow.ID)

	store := NewWorkflowLinkStore(client)
	gotLink, gotErr := store.GetById(context.Background(), wantLink.ID)
	assert.EqualExportedValues(t, wantLink, gotLink)
	assert.Nil(t, gotErr)
}
