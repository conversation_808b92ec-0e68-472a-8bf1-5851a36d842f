package http

import (
	"github.com/gofiber/fiber/v2"
	"os"
	"path/filepath"
	"resflow/internal/plugin"
	"resflow/utils"
)

type PluginService struct {
	service plugin.Service
}

func NewPluginService(service plugin.Service) *PluginService {
	return &PluginService{
		service: service,
	}
}

func (s PluginService) HandleIcon(baseDir string) func(ctx *fiber.Ctx) error {
	return func(ctx *fiber.Ctx) error {
		pluginName := ctx.Params("name")
		pluginVersion := ctx.Params("version")
		pluginDTO, err := s.service.GetByNameAndVersion(ctx.Context(), pluginName, pluginVersion)
		if err != nil {
			return err
		}
		if pluginDTO.Icon == "" {
			ctx.Status(fiber.StatusNotFound)
			return ctx.SendString("icon not found")
		}
		iconPath := filepath.Join(baseDir, pluginName, pluginVersion, pluginDTO.Icon)
		utils.Logger.Debugf("download icon from %s", iconPath)
		_, err = os.Stat(iconPath)
		if err != nil {
			ctx.Status(fiber.StatusNotFound)
			utils.Logger.Debugf("file error: %v", err)
			return err
		}
		return ctx.SendFile(iconPath)
	}
}
