package nodes

import (
	"database/sql/driver"
	"fmt"
	"reflect"
)

type Node interface {
	Execute(config map[string]interface{}) error
}

type NodeType int

const (
	Internal NodeType = iota
	Executable
	External
)

var NodeTypeToString = map[NodeType]string{
	Internal:   "internal",
	Executable: "executable",
	External:   "external",
}

func (nt NodeType) String() string {
	if v, ok := NodeTypeToString[nt]; ok {
		return v
	}
	return "unknown"
}

func (NodeType) Values() []string {
	return []string{External.String(), Internal.String(), Executable.String()}
}

func (nt NodeType) Value() (driver.Value, error) {
	return int64(nt), nil
}

func (nt *NodeType) Scan(val any) error {
	i, ok := val.(int64)
	if !ok {
		return fmt.Errorf("enums: unable to scan '%v' into type %T", val, val)
	}
	*nt = NodeType(i)
	return nil
}

var ConfigStructMapping = map[NodeType]reflect.Type{}
